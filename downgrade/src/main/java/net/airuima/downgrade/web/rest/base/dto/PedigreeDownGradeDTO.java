package net.airuima.downgrade.web.rest.base.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 降级规则型号列表
 * <AUTHOR>
 * @date 2022-03-30
 **/
@Schema(description = "降级规则型号列表DTO")
public class PedigreeDownGradeDTO {
    /**
     * 降级型号id
     */
    @NotNull
    @Schema(description = "降级型号id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetPedigreeId;

    /**
     * 可降级比例
     */
    @NotNull
    @Schema(description = "可降级比例")
    private float proportion;

    public Long getTargetPedigreeId() {
        return targetPedigreeId;
    }

    public void setTargetPedigreeId(Long targetPedigreeId) {
        this.targetPedigreeId = targetPedigreeId;
    }

    public float getProportion() {
        return proportion;
    }

    public void setProportion(float proportion) {
        this.proportion = proportion;
    }
}
