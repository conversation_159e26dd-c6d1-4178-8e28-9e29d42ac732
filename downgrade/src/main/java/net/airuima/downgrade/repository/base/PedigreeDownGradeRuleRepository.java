package net.airuima.downgrade.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.downgrade.domain.base.PedigreeDownGradeRule;
import net.airuima.downgrade.web.rest.base.dto.PedigreeDownGradeSearchDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产成品降级规则
 *
 * <AUTHOR>
 * @date 2022/3/29-9:49
 **/
@Repository
public interface PedigreeDownGradeRuleRepository extends LogicDeleteableRepository<PedigreeDownGradeRule>,
        EntityGraphJpaSpecificationExecutor<PedigreeDownGradeRule>, EntityGraphJpaRepository<PedigreeDownGradeRule, Long> {

    /**
     * 根据规则编码或者名称获取产成品降级规则分页
     *
     * @param text     产成品降级规则编码或者名称
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.pedigree.PedigreeDownGradeRule> 产成品降级规则分页
     */
    @FetchMethod
    @Query("select rule from PedigreeDownGradeRule rule where (rule.code like concat('%',?1,'%') or rule.name like concat('%',?1,'%'))  and rule.deleted=0L order by rule.createdDate desc")
    @EntityGraph("pedigreeDownGradeRuleEntityGraph")
    Page<PedigreeDownGradeRule> findByNameOrCode(String text, Pageable pageable);

    /**
     * 通过产品名称和编码模糊查询降级产品型号
     *
     * @param text     产品谱系则编码或者名称
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.pedigree.PedigreeDownGradeRule> 产成品降级规则分页
     */
    @FetchMethod
    @Query("select rule from PedigreeDownGradeRule rule where (rule.originPedigree.code like concat('%',?1,'%') or rule.originPedigree.name like concat('%',?1,'%'))  and rule.deleted=0L order by rule.createdDate desc")
    Page<PedigreeDownGradeRule> findByPedigreeNameOrCode(String text, Pageable pageable);

    /**
     * 通过产品名称或编码查询降级产品型号
     *
     * @param text      产品谱系则编码或者名称
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.pedigree.PedigreeDownGradeRule> 产成品降级规则分页
     */
    @FetchMethod
    @Query("select rule from PedigreeDownGradeRule rule where (rule.originPedigree.code =?1 or rule.originPedigree.name =?1)  and rule.deleted=0L order by rule.createdDate desc")
    Page<PedigreeDownGradeRule> findByEqualPedigreeNameOrCode(String text, Pageable pageable);

    /**
     * 根据条件模糊查找降级规则
     * @param dto  产成品降级规则查询参数
     * @param endTimestamp 开始时间
     * @param endTimestamp 结束时间
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.pedigree.PedigreeDownGradeRule> 产成品降级规则分页
     */
    @FetchMethod
    @Query("select r from PedigreeDownGradeRule r where (:#{#dto.name} is null or r.name like concat('%',:#{#dto.name},'%') ) " +
            " and (:#{#dto.originPedigreeName} is null or r.originPedigree.name like concat('%',:#{#dto.originPedigreeName},'%') ) " +
            " and (:#{#dto.originPedigreeCode} is null or r.originPedigree.code like concat('%',:#{#dto.originPedigreeCode},'%') ) " +
            " and (:#{#dto.targetPedigreeCode} is null or r.targetPedigree.code like concat('%',:#{#dto.targetPedigreeCode},'%') ) " +
            " and (:startTimestamp is null or r.createdDate >=:startTimestamp) " +
            " and (:endTimestamp is null or r.createdDate <=:endTimestamp) " +
            " and (:#{#dto.isEnable} is null or r.isEnable=:#{#dto.isEnable}) " +
            " and r.deleted=0 order by r.createdDate desc")
    Page<PedigreeDownGradeRule>  findByCondition(@Param("dto") PedigreeDownGradeSearchDTO dto,
                                                 @Param("startTimestamp") Instant startTimestamp,
                                                 @Param("endTimestamp") Instant endTimestamp,
                                                 Pageable pageable);

    /**
     * 通过产成品降级规则规则名称查找规则
     * @param name 产成品降级规则规则名称
     * @return java.lang.Integer规则名称数量
     */
    @DataFilter(isSkip = true)
    @Query("select count(name) from PedigreeDownGradeRule rule where rule.name = :name and rule.deleted=0")
    Integer countByName(@Param("name") String name);

    /**
     * 查找产品是否已关联规则
     * @param originPedigreeId 产品谱系主键ID
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeDownGradeRule> 产成品降级规则列表
     */
    @DataFilter(isSkip = true)
    List<PedigreeDownGradeRule> findByOriginPedigreeIdAndDeleted(Long originPedigreeId, Long deleted);

    /**
     * 查找原比例合计
     * @param originPedigreeId 产品谱系主键ID
     * @return java.lang.Float 比例合计
     */
    @DataFilter(isSkip = true)
    @Query(value = "select sum(p.proportion) from PedigreeDownGradeRule p  where p.originPedigree.id=:originPedigreeId and p.deleted=0")
    Float sumProportionByOriginPedigreeId(@Param("originPedigreeId")Long originPedigreeId);
}
