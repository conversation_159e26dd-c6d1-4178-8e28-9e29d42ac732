<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="zhuhuawu (generated)" id="1737428657326-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="base_facility_cadence"/>
            </not>
        </preConditions>
        <createTable remarks="设备理论节拍表" tableName="base_facility_cadence">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="facility_id" remarks="设备ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="duration" remarks="理论节拍时间" type="DECIMAL(10, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="unit" remarks="理论节拍单位（0：秒，1：分钟，2：小时）" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="facility_id, deleted" constraintName="base_facility_cadence_unique" tableName="base_facility_cadence"/>
        <createIndex indexName="base_facility_cadence_facility_id_index" tableName="base_facility_cadence">
            <column name="facility_id"/>
        </createIndex>
        <createIndex indexName="base_facility_cadence_work_cell_id_index" tableName="base_facility_cadence">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1737428657326-2">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="procedure_facility_oee"/>
            </not>
        </preConditions>
        <createTable remarks="设备运行状态表" tableName="procedure_facility_oee">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="facility_id" type="BIGINT"/>
            <column name="actual_work_duration" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="plan_down_duration" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="down_line_duration" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="latest_status_operate_time" type="timestamp"/>
            <column defaultValueNumeric="0" name="finish_number" remarks="实际完成数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="qualified_number" remarks="合格数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="unqualified_number" remarks="不合格数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="record_date" remarks="记录日期" type="timestamp"/>
            <column name="ideal_work_duration" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="load_duration" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="activation_duration" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="abnormal_status_start_time" remarks="异常停机开始时间" type="timestamp"/>
        </createTable>
        <addUniqueConstraint columnNames="work_cell_id, facility_id, record_date, deleted" constraintName="procedure_facility_oee_unique" tableName="procedure_facility_oee"/>
        <createIndex indexName="procedure_facility_oee_facility_id_index" tableName="procedure_facility_oee">
            <column name="facility_id"/>
        </createIndex>
        <createIndex indexName="procedure_facility_oee_record_date_index" tableName="procedure_facility_oee">
            <column name="record_date"/>
        </createIndex>
        <createIndex indexName="procedure_facility_oee_work_cell_id_index" tableName="procedure_facility_oee">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="zhuhuawu (generated)" id="1737428657326-3">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="procedure_facility_plan_down_history"/>
            </not>
        </preConditions>
        <createTable remarks="计划停止历史表" tableName="procedure_facility_plan_down_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="facility_oee_id" remarks="设备OEE-ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="start_time" type="time"/>
            <column name="end_time" type="time"/>
            <column name="remark" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createIndex indexName="procedure_facility_plan_down_history_facility_oee_id_index" tableName="procedure_facility_plan_down_history">
            <column name="facility_oee_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
