package net.airuima.oee.dto.calendar;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工作日历表Domain
 *
 * <AUTHOR>
 * @date 2022-09-09
 */
@Schema(description = "工作日历(Calendar)")
public class CalendarDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 生产线ID
     */
    @Schema(description = "生产线ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workLineId;

    /**
     * 工作站ID
     */
    @Schema(description = "工作站ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workStationId;

    /**
     * 工作日期
     */
    @Schema(description = "工作日期")
    private LocalDate workTime;

    /**
     * 每日班次信息JSON数组 [{'id':班次ID,'name':'班次名称','planStartTime':'8:00','planEndTime'：'18:00','actualStartTime':'9:00','actualEndTime':'19:00'}]
     */
    @Schema(description = "每日班次信息")
    private List<EveryDayShiftDTO> shiftJsonList;

    /**
     * 是否自定义（0：否，1：是）
     */
    @Schema(description = "是否自定义（0：否，1：是）", required = true)
    private boolean custom;

    public Long getWorkLineId() {
        return workLineId;
    }

    public CalendarDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }

    public Long getWorkStationId() {
        return workStationId;
    }

    public void setWorkStationId(Long workStationId) {
        this.workStationId = workStationId;
    }

    public LocalDate getWorkTime() {
        return workTime;
    }

    public void setWorkTime(LocalDate workTime) {
        this.workTime = workTime;
    }

    public List<EveryDayShiftDTO> getShiftJsonList() {
        return shiftJsonList;
    }

    public void setShiftJsonList(List<EveryDayShiftDTO> shiftJsonList) {
        this.shiftJsonList = shiftJsonList;
    }

    public boolean isCustom() {
        return custom;
    }

    public void setCustom(boolean custom) {
        this.custom = custom;
    }
}
