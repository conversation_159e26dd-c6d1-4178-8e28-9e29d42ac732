package net.airuima.oee.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.oee.domain.base.FacilityCadence;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备理论节拍表Repository
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Repository
public interface FacilityCadenceRepository extends LogicDeleteableRepository<FacilityCadence>,
        EntityGraphJpaSpecificationExecutor<FacilityCadence>, EntityGraphJpaRepository<FacilityCadence, Long> {

    /**
     * 通过工位主键ID+设备主键ID集合+删除标识查询设备理论节拍集合
     *
     * @param workCellId 工位主键ID
     * @param facilityIdList 设备主键ID集合
     * @param deleted 删除标识
     * @return : java.util.List<net.airuima.rbase.domain.base.oee.FacilityCadence> 设备理论节拍列表
     * <AUTHOR>
     * @date 2022/11/30
     **/
    @DataFilter(isSkip = true)
    List<FacilityCadence> findByWorkCellIdAndFacilityIdInAndDeleted(Long workCellId, List<Long> facilityIdList, Long deleted);

    @DataFilter(isSkip = true)
    FacilityCadence findByWorkCellIdAndFacilityIdAndDeleted(Long workCellId, Long facilityId, Long deleted);
}
