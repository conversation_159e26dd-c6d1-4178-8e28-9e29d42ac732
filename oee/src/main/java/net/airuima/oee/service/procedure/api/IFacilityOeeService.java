package net.airuima.oee.service.procedure.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.oee.domain.procedure.FacilityOee;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@FuncDefault
public interface IFacilityOeeService {

    /**
     * 修改设备状态信息
     *
     * @param facilityOee
     * @param status
     */
    default boolean changeFacilityStatus(FacilityOee facilityOee, int status, Integer number, LocalDateTime changeDateTime) {
        return Boolean.FALSE;
    }
}
