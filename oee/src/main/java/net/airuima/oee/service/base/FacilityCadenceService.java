package net.airuima.oee.service.base;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.oee.domain.base.FacilityCadence;
import net.airuima.oee.repository.base.FacilityCadenceRepository;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备理论节拍表Service
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class FacilityCadenceService extends CommonJpaService<FacilityCadence> {

    private final String FACILITY_CADENCE_ENTITY_GRAPH = "facilityCadenceEntityGraph";
    private final FacilityCadenceRepository facilityCadenceRepository;

    public FacilityCadenceService(FacilityCadenceRepository facilityCadenceRepository) {
        this.facilityCadenceRepository = facilityCadenceRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<FacilityCadence> find(Specification<FacilityCadence> spec, Pageable pageable) {
        return facilityCadenceRepository.findAll(spec, pageable,new NamedEntityGraph(FACILITY_CADENCE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<FacilityCadence> find(Specification<FacilityCadence> spec) {
        return facilityCadenceRepository.findAll(spec,new NamedEntityGraph(FACILITY_CADENCE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<FacilityCadence> findAll(Pageable pageable) {
        return facilityCadenceRepository.findAll(pageable,new NamedEntityGraph(FACILITY_CADENCE_ENTITY_GRAPH));
    }

}
