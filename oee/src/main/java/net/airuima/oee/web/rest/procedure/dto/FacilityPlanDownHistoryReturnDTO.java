package net.airuima.oee.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Schema(description = "计划停止历史表返回DTO")
public class FacilityPlanDownHistoryReturnDTO implements Serializable {
    /**
     * ID
     */
    @Schema(description = "ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 计划停止时间开始
     */
    @Schema(description = "计划停止时间开始")
    private LocalTime startTime;

    /**
     * 计划停止时间结束
     */
    @Schema(description = "计划停止时间结束")
    private LocalTime endTime;

    /**
     * 停止原因
     */
    @Schema(description = "停止原因")
    private String remark;

    /**
     * 提交人
     */
    @Schema(description = "提交人")
    private String createdBy;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private LocalDateTime createdDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalTime startTime) {
        this.startTime = startTime;
    }

    public LocalTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalTime endTime) {
        this.endTime = endTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
}
