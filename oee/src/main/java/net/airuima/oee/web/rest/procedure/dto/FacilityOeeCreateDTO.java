package net.airuima.oee.web.rest.procedure.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.oee.dto.calendar.EveryDayShiftDTO;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Schema(description = "设备OEE创建DTO")
public class FacilityOeeCreateDTO implements Serializable {

    /**
     * Map<生产线ID/工站ID，List<每日班次信息>>
     **/
    @Schema(description = "Map<生产线ID/工站ID，List<每日班次信息>>")
    private Map<Long, List<EveryDayShiftDTO>> workIdAndWorkTime;

    /**
     * 记录日期
     **/
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    public Map<Long, List<EveryDayShiftDTO>> getWorkIdAndWorkTime() {
        return workIdAndWorkTime;
    }

    public FacilityOeeCreateDTO setWorkIdAndWorkTime(Map<Long, List<EveryDayShiftDTO>> workIdAndWorkTime) {
        this.workIdAndWorkTime = workIdAndWorkTime;
        return this;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
    }
}
