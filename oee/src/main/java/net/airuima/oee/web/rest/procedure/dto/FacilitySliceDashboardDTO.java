package net.airuima.oee.web.rest.procedure.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

@Schema(description = "设备切片看板DTO")
public class FacilitySliceDashboardDTO implements Serializable {
    /**
     * 设备切片集合
     */
    @Schema(description = "设备切片集合")
    private List<FacilitySlice> facilitySliceList;

    public List<FacilitySlice> getFacilitySliceList() {
        return facilitySliceList;
    }

    public FacilitySliceDashboardDTO setFacilitySliceList(List<FacilitySlice> facilitySliceList) {
        this.facilitySliceList = facilitySliceList;
        return this;
    }

    /**
     * 设备切片
     **/
    @Schema(description = "设备切片")
    public static class FacilitySlice implements Serializable{
        /**
         * 设备编码
         */
        @Schema(description = "设备编码")
        private String code;

        /**
         * 名称
         */
        @Schema(description = "设备名称")
        private String name;

        /**
         * 实际开始时间
         */
        @Schema(description = "实际开始时间")
        @DateTimeFormat(pattern = "HH:mm:ss")
        @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
        private LocalTime actualStartTime;

        /**
         * 实际结束时间
         */
        @Schema(description = "实际结束时间")
        @DateTimeFormat(pattern = "HH:mm:ss")
        @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
        private LocalTime actualEndTime;

        /**
         * 设备阶段集合
         */
        @Schema(description = "设备阶段集合")
        private List<FacilityStage> facilityStageList;

        public LocalTime getActualStartTime() {
            return actualStartTime;
        }

        public FacilitySlice setActualStartTime(LocalTime actualStartTime) {
            this.actualStartTime = actualStartTime;
            return this;
        }

        public LocalTime getActualEndTime() {
            return actualEndTime;
        }

        public FacilitySlice setActualEndTime(LocalTime actualEndTime) {
            this.actualEndTime = actualEndTime;
            return this;
        }

        public String getCode() {
            return code;
        }

        public FacilitySlice setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public FacilitySlice setName(String name) {
            this.name = name;
            return this;
        }

        public List<FacilityStage> getFacilityStageList() {
            return facilityStageList;
        }

        public FacilitySlice setFacilityStageList(List<FacilityStage> facilityStageList) {
            this.facilityStageList = facilityStageList;
            return this;
        }
    }

    /**
     * 设备阶段
     **/
    @Schema(description = "设备阶段")
    public static class FacilityStage implements Serializable{
        /**
         * 状态
         */
        @Schema(description = "状态")
        private Integer status;

        /**
         * 时间开始
         */
        @Schema(description = "时间开始")
        @DateTimeFormat(pattern = "HH:mm:ss")
        @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
        private LocalTime startTime;

        /**
         * 时间结束
         */
        @Schema(description = "时间结束")
        @DateTimeFormat(pattern = "HH:mm:ss")
        @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
        private LocalTime endTime;

        /**
         * 百分比
         */
        @Schema(description = "百分比")
        private BigDecimal percent;

        public Integer getStatus() {
            return status;
        }

        public FacilityStage setStatus(Integer status) {
            this.status = status;
            return this;
        }

        public LocalTime getStartTime() {
            return startTime;
        }

        public FacilityStage setStartTime(LocalTime startTime) {
            this.startTime = startTime;
            return this;
        }

        public LocalTime getEndTime() {
            return endTime;
        }

        public FacilityStage setEndTime(LocalTime endTime) {
            this.endTime = endTime;
            return this;
        }

        public BigDecimal getPercent() {
            return percent;
        }

        public FacilityStage setPercent(BigDecimal percent) {
            this.percent = percent;
            return this;
        }
    }
}
