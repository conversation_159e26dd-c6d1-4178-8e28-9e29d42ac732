package net.airuima.oee.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备状态历史表Domain
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Schema(name = "设备状态历史表新增DTO", description = "设备状态历史表新增DTO")
public class FacilityStatusChangeHistoryCreateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备OEE-ID
     */
    @Schema(description = "设备OEE-ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long facilityOeeId;

    /**
     * 原运行状态（0：正常运行，1：正常关机，2：异常停机，3：维护保养，4：调机换型）
     */
    @Schema(description = "原运行状态（0：正常运行，1：正常关机，2：异常停机，3：维护保养，4：调机换型）", required = true)
    private Integer originStatus;

    /**
     * 目标运行状态（0：正常运行，1：正常关机，2：异常停机，3：维护保养，4：调机换型）
     */
    @Schema(description = "目标运行状态（0：正常运行，1：正常关机，2：异常停机，3：维护保养，4：调机换型）", required = true)
    private Integer targetStatus;

    public Long getFacilityOeeId() {
        return facilityOeeId;
    }

    public void setFacilityOeeId(Long facilityOeeId) {
        this.facilityOeeId = facilityOeeId;
    }

    public Integer getOriginStatus() {
        return originStatus;
    }

    public void setOriginStatus(Integer originStatus) {
        this.originStatus = originStatus;
    }

    public Integer getTargetStatus() {
        return targetStatus;
    }

    public void setTargetStatus(Integer targetStatus) {
        this.targetStatus = targetStatus;
    }
}
