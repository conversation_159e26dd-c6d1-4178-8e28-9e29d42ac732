package net.airuima.dynamicdata.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.dynamicdata.domain.base.DynamicDataColumn;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 动态元数据定义表Repository
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Repository
public interface DynamicDataColumnRepository extends LogicDeleteableRepository<DynamicDataColumn>,
        EntityGraphJpaSpecificationExecutor<DynamicDataColumn>, EntityGraphJpaRepository<DynamicDataColumn, Long> {

    /**
     * 根据动态数据id 获取动态元数据对象
     *
     * @param id      动态数据id
     * @param deleted 逻辑删除
     * @return DynamicDataColumn
     */
    @DataFilter(isSkip = true)
    Optional<DynamicDataColumn> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 根据动态数据编码 获取动态元数据对象
     *
     * @param code    元数据编码
     * @param deleted 逻辑删除
     * @return DynamicDataColumn
     */
    @DataFilter(isSkip = true)
    Optional<DynamicDataColumn> findByCodeAndDeleted(String code, Long deleted);

    /**
     * 通过不为当前主键ID+编码+删除标识查询动态元数据字段
     *
     * @param id      动态元数据定义主键ID
     * @param code    动态元数据定义编码
     * @param deleted 删除标识
     * @return DynamicDataColumn
     **/
    @DataFilter(isSkip = true)
    Optional<DynamicDataColumn> findByIdNotAndCodeAndDeleted(Long id, String code, Long deleted);


    /**
     * 通过父类元数据id 获取子动态元数据
     *
     * @param parentId 父类元数据id
     * @param deleted  逻辑删除
     * @return List<DynamicDataColumn>
     */
    @DataFilter(isSkip = true)
    List<DynamicDataColumn> findByParentIdAndDeleted(Long parentId, Long deleted);

    /**
     * 通过父类元数据id 获取子动态元数据
     *
     * @param parentId 父类元数据id
     * @param deleted  逻辑删除
     * @return List<DynamicDataColumn>
     */
    @DataFilter(isSkip = true)
    List<DynamicDataColumn> findByParentIdInAndDeleted(List<Long> parentId, Long deleted);

    /**
     * 通过父类元数据id 删除子动态元数据
     *
     * @param parentId 父类元数据id
     * @param deleted  逻辑删除
     */
    @Modifying
    @Query("update DynamicDataColumn d set d.deleted = d.id where d.parent.id = ?1 and d.deleted = ?2")
    void deleteByParentIdAndDeleted(Long parentId, Long deleted);

    /**
     * 根据动态元数据编码或者名称获取启用的动态元数据列表
     *
     * @param text     动态元数据编码或者名称
     * @param pageable 分页
     */
    @Query("select dy from DynamicDataColumn dy where (" +
            "(coalesce(?1, null) is null or dy.code like concat('%',?1,'%'))" +
            "or " +
            "(coalesce(?1, null) is null or dy.name like concat('%',?1,'%')))" +
            "and dy.isEnable=?2 and dy.deleted=0L")
    Page<DynamicDataColumn> findByNameOrCode(String text, Boolean enable, Pageable pageable);

}
