package net.airuima.dynamicdata.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.dynamicdata.domain.base.StepDynamicDataColumn;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 动态元数据定义表Repository
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Repository
public interface StepDynamicDataColumnRepository extends LogicDeleteableRepository<StepDynamicDataColumn>,
        EntityGraphJpaSpecificationExecutor<StepDynamicDataColumn>, EntityGraphJpaRepository<StepDynamicDataColumn, Long> {


    /**
     * 通过动态数据id，删除对应的 动态元数据
     *
     * @param stepDynamicDataId 动态数据id
     */
    @Modifying
    @Query("update StepDynamicDataColumn sddc set sddc.deleted = sddc.id where sddc.stepDynamicData.id = ?1 and sddc.deleted = 0")
    void logicDeletedByDynamicDataId(Long stepDynamicDataId);

    /**
     * 通过动态数据id，获取对应的 动态元数据
     *
     * @param stepDynamicDataId 动态数据id
     * @param deleted           逻辑删除
     * @return List<StepDynamicDataColumn>
     */
    @Query("select sddc from  StepDynamicDataColumn sddc where sddc.stepDynamicData.id = ?1 and sddc.dynamicDataColumn is not null and sddc.deleted = ?2")
    List<StepDynamicDataColumn> findByStepDynamicDataIdAndDeleted(Long stepDynamicDataId, Long deleted);
}
