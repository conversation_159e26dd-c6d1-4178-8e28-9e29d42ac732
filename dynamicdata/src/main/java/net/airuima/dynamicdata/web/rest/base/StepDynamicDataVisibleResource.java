package net.airuima.dynamicdata.web.rest.base;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dynamicdata.domain.base.StepDynamicDataVisible;
import net.airuima.dynamicdata.service.base.StepDynamicDataVisibleService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 动态数据可见工序Resource"
 *
 * <AUTHOR>
 * @date 2023/11/14
 */
@Tag(name = "动态数据可见工序Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/step-dynamic-data-visibles")
@AuthorityRegion("动态数据")
@FuncInterceptor("StepDynamicData")
public class StepDynamicDataVisibleResource extends BaseResource<StepDynamicDataVisible> {


    private final StepDynamicDataVisibleService stepDynamicDataVisibleService;

    public StepDynamicDataVisibleResource(StepDynamicDataVisibleService stepDynamicDataVisibleService) {
        this.stepDynamicDataVisibleService = stepDynamicDataVisibleService;
        this.mapUri = "/api/step-dynamic-data-visibles";
    }
}
