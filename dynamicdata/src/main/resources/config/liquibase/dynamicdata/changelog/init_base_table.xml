<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.6.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet author="zhuhuawu (generated)" id="1737517162678-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="base_dynamic_data_column"/>
            </not>
        </preConditions>
        <createTable remarks="动态元数据定义表" tableName="base_dynamic_data_column">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="动态元数据名称" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="动态元数据编码" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="guidance" remarks="引导信息" type="VARCHAR(255)"/>
            <column name="prompt" remarks="提示信息" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="table_order" remarks="表格展示顺序" type="INT"/>
            <column defaultValueNumeric="0" name="form_order" remarks="表单展示顺序" type="INT"/>
            <column defaultValue="0" name="category" remarks="字段类型" type="VARCHAR(50)"/>
            <column name="widget" remarks="前端组件" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="widget_data" remarks="前端组件数据" type="LONGTEXT"/>
            <column name="column_validate" remarks="前端字段验证" type="LONGTEXT"/>
            <column name="parent_id" remarks="父级id" type="BIGINT"/>
            <column defaultValueBoolean="true" name="is_enable" remarks="是否启用" type="BIT(1)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="动态数据定义表" tableName="base_step_dynamic_data">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" remarks="动态数据名称" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="code" remarks="动态数据编码" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="description" remarks="描述信息" type="VARCHAR(255)"/>
            <column name="pedigree_id" remarks="产品谱系ID" type="BIGINT"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column name="step_id" remarks="工序ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="granularity" remarks="关联类型(0:工单,1:子工单,2:容器,3:单支)" type="TINYINT(3)"/>
            <column defaultValueBoolean="true" name="is_enable" remarks="是否启用" type="BIT(1)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="动态元数据定义表" tableName="base_step_dynamic_data_column">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="step_dynamic_data_id" remarks="动态数据ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="form_order" remarks="表单展示顺序" type="INT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="dynamic_data_column_id" remarks="动态元数据id" type="BIGINT"/>
        </createTable>
        <createTable remarks="动态数据可见工序" tableName="base_step_dynamic_data_visible">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="dynamic_id" remarks="动态数据ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="step_id" remarks="可见工序ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="工序动态生产数据明细表" tableName="procedure_step_dynamic_data_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="step_dynamic_data_id" remarks="动态数据定义ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="bussiness_id" remarks="业务数据ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="data_info" remarks="动态数据明细" type="JSON"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_dynamic_data_column_unique" tableName="base_dynamic_data_column"/>
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_step_dynamic_data_code_unique_index" tableName="base_step_dynamic_data"/>
        <addUniqueConstraint columnNames="step_dynamic_data_id, dynamic_data_column_id, deleted" constraintName="base_step_dynamic_data_column_unique" tableName="base_step_dynamic_data_column"/>
        <addUniqueConstraint columnNames="pedigree_id, work_flow_id, step_id, deleted" constraintName="base_step_dynamic_data_unique_index" tableName="base_step_dynamic_data"/>
        <createIndex indexName="base_dynamic_data_column_id_index" tableName="base_step_dynamic_data_column">
            <column name="dynamic_data_column_id"/>
        </createIndex>
        <createIndex indexName="base_dynamic_data_column_name_index" tableName="base_dynamic_data_column">
            <column name="name"/>
        </createIndex>
        <createIndex indexName="base_dynamic_data_column_parent_index" tableName="base_dynamic_data_column">
            <column name="parent_id"/>
        </createIndex>
        <createIndex indexName="base_step_dynamic_data_column_data_id_index" tableName="base_step_dynamic_data_column">
            <column name="step_dynamic_data_id"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
        <createIndex indexName="procedure_step_dynamic_data_detail_bussiness_id_index" tableName="procedure_step_dynamic_data_detail">
            <column name="bussiness_id"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
