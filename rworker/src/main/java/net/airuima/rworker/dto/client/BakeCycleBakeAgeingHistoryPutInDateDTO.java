package net.airuima.rworker.dto.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-05-23
 */
@Schema(description = "烘烤温循老化历史放入时间参数")
public class BakeCycleBakeAgeingHistoryPutInDateDTO {

    /**
     * 放入时间
     */
    @Schema(description = "放入时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime putInDate;

    /**
     * 截止时间
     */
    @Schema(description = "截止时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cutOffDate;

    /**
     * 时长范围
     */
    @Schema(description = "时长范围")
    private String durationRange;

    public LocalDateTime getPutInDate() {
        return putInDate;
    }

    public BakeCycleBakeAgeingHistoryPutInDateDTO setPutInDate(LocalDateTime putInDate) {
        this.putInDate = putInDate;
        return this;
    }

    public LocalDateTime getCutOffDate() {
        return cutOffDate;
    }

    public BakeCycleBakeAgeingHistoryPutInDateDTO setCutOffDate(LocalDateTime cutOffDate) {
        this.cutOffDate = cutOffDate;
        return this;
    }

    public String getDurationRange() {
        return durationRange;
    }

    public BakeCycleBakeAgeingHistoryPutInDateDTO setDurationRange(String durationRange) {
        this.durationRange = durationRange;
        return this;
    }
}
