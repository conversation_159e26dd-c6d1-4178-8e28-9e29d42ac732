package net.airuima.rworker.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rworker.dto.client.base.BaseClientDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork物料相关接口DTO
 *
 * <AUTHOR>
 * @date 2020/12/28
 */
@Schema(description = "Rwork工位保存上料请求参数")
public class ClientMaterialDTO extends BaseClientDTO {

    /**
     * 当前总工单id
     */
    @Schema(description = "总工单ID")
    private Long workSheetId;

    /**
     * 当前工序id
     */
    @Schema(description = "当前工序ID")
    private Long stepId;

    /**
     * 当前工位id
     */
    @Schema(description = "当前工位id")
    private Long workCellId;

    /**
     * 当前工序投产数
     */
    @Schema(description = "当前工序投产数")
    private Integer number;

    /**
     * 上料的物料相关信息
     */
    @Schema(description = "上料批次列表")
    private List<ClientMaterialDetailDTO> materialDetailDtoList;

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public ClientMaterialDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public ClientMaterialDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public ClientMaterialDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public List<ClientMaterialDetailDTO> getMaterialDetailDtoList() {
        return materialDetailDtoList;
    }

    public ClientMaterialDTO setMaterialDetailDtoList(List<ClientMaterialDetailDTO> materialDetailDtoList) {
        this.materialDetailDtoList = materialDetailDtoList;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public ClientMaterialDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    @Schema(description = "上料批次集合")
    public static class ClientMaterialDetailDTO {
        /**
         * 物料ID
         */
        @Schema(description = "物料ID")
        private Long materialId;
        /**
         * 物料编码
         */
        @Schema(description = "物料编码")
        private String materialCode;

        /**
         * 物料批次号
         */
        @Schema(description = "物料批次号")
        private String materialBatch;

        /**
         * 上料数量
         */
        @Schema(description = "上料数量")
        private Double number;

        /**
         * 物料管控粒度(0:单只序列号;1:批次号)
         */
        @Schema(description = "物料管控粒度(0:单只序列号;1:批次号)")
        private Integer controlMaterialGranularity;

        /**
         * 批次号/序列号
         */
        @Schema(description = "批次号/序列号")
        private String sn;

        public Long getMaterialId() {
            return materialId;
        }

        public ClientMaterialDetailDTO setMaterialId(Long materialId) {
            this.materialId = materialId;
            return this;
        }

        public String getMaterialCode() {
            return materialCode;
        }

        public ClientMaterialDetailDTO setMaterialCode(String materialCode) {
            this.materialCode = materialCode;
            return this;
        }

        public String getMaterialBatch() {
            return materialBatch;
        }

        public ClientMaterialDetailDTO setMaterialBatch(String materialBatch) {
            this.materialBatch = materialBatch;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public ClientMaterialDetailDTO setNumber(Double number) {
            this.number = number;
            return this;
        }

        public Integer getControlMaterialGranularity() {
            return controlMaterialGranularity;
        }

        public ClientMaterialDetailDTO setControlMaterialGranularity(Integer controlMaterialGranularity) {
            this.controlMaterialGranularity = controlMaterialGranularity;
            return this;
        }

        public String getSn() {
            return sn;
        }

        public ClientMaterialDetailDTO setSn(String sn) {
            this.sn = sn;
            return this;
        }

    }

}
