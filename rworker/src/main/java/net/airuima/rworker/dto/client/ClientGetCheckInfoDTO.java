package net.airuima.rworker.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rworker.dto.client.base.BaseClientDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
@Schema(description = "RWorker验证及获取首中末检相关信息DTO")
public class ClientGetCheckInfoDTO extends BaseClientDTO {

    @Schema(description = "类型(0,首检;1,QC抽检)")
    private Integer category;

    @Schema(description = "是否首次开机生产(false/true)")
    private Boolean isFirstTimeToWork;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "工位ID")
    private Long workCellId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "子工单ID")
    private Long subWsId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "工序ID")
    private Long stepId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "Rworker下次自动检测时间间隔(ms)")
    private Long nextAutoCheckTime;

    @Schema(description = "待检测工位及对应工序列表")
    private List<WorkCellStepInfo> workCellStepInfoList;

    @Schema(description = "检测规则信息")
    private CheckRuleInfo checkRuleInfo;

    public ClientGetCheckInfoDTO() {

    }

    public ClientGetCheckInfoDTO(BaseClientDTO baseClientDto) {
        this.setStatus(baseClientDto.getStatus());
        this.setMessage(baseClientDto.getMessage());
    }

    public Integer getCategory() {
        return category;
    }

    public ClientGetCheckInfoDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Boolean getIsFirstTimeToWork() {
        return isFirstTimeToWork;
    }

    public ClientGetCheckInfoDTO setIsFirstTimeToWork(Boolean isFirstTimeToWork) {
        this.isFirstTimeToWork = isFirstTimeToWork;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public ClientGetCheckInfoDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getSubWsId() {
        return subWsId;
    }

    public ClientGetCheckInfoDTO setSubWsId(Long subWsId) {
        this.subWsId = subWsId;
        return this;
    }

    public List<WorkCellStepInfo> getWorkCellStepInfoList() {
        return workCellStepInfoList;
    }

    public ClientGetCheckInfoDTO setWorkCellStepInfoList(List<WorkCellStepInfo> workCellStepInfoList) {
        this.workCellStepInfoList = workCellStepInfoList;
        return this;
    }

    public CheckRuleInfo getCheckRuleInfo() {
        return checkRuleInfo;
    }

    public ClientGetCheckInfoDTO setCheckRuleInfo(CheckRuleInfo checkRuleInfo) {
        this.checkRuleInfo = checkRuleInfo;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public ClientGetCheckInfoDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getNextAutoCheckTime() {
        return nextAutoCheckTime;
    }

    public ClientGetCheckInfoDTO setNextAutoCheckTime(Long nextAutoCheckTime) {
        this.nextAutoCheckTime = nextAutoCheckTime;
        return this;
    }

    /**
     * 待检的工位及工位对应的工序列表
     *
     * <AUTHOR>
     * @date 2021-03-23
     **/
    @Schema(description = "待检测的工位及对应的工序列表")
    public static class WorkCellStepInfo {
        @Schema(description = "待检测工位ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long workCellId;
        @Schema(description = "待检测工位编码")
        private String workCellCode;
        @Schema(description = "待检测工位名称")
        private String workCellName;
        @Schema(description = "待检测工序列表")
        private List<StepInfo> stepInfoList;

        public Long getWorkCellId() {
            return workCellId;
        }

        public WorkCellStepInfo setWorkCellId(Long workCellId) {
            this.workCellId = workCellId;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public WorkCellStepInfo setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public WorkCellStepInfo setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public List<StepInfo> getStepInfoList() {
            return stepInfoList;
        }

        public WorkCellStepInfo setStepInfoList(List<StepInfo> stepInfoList) {
            this.stepInfoList = stepInfoList;
            return this;
        }

        /**
         * 待检工位对应的工序
         *
         * <AUTHOR>
         * @date 2021-03-23
         **/
        @Schema(description = "待检测工序信息")
        public static class StepInfo {
            @Schema(description = "待检测工序ID")
            @JsonSerialize(using = ToStringSerializer.class)
            private Long stepId;
            @Schema(description = "待检测工序名称")
            private String stepName;
            @Schema(description = "待检测工序编码")
            private String stepCode;

            public StepInfo() {

            }

            public StepInfo(Step step) {
                this.stepName = step.getName();
                this.stepCode = step.getCode();
                this.stepId = step.getId();
            }

            public Long getStepId() {
                return stepId;
            }

            public StepInfo setStepId(Long stepId) {
                this.stepId = stepId;
                return this;
            }

            public String getStepName() {
                return stepName;
            }

            public StepInfo setStepName(String stepName) {
                this.stepName = stepName;
                return this;
            }
        }
    }

    @Schema(description = "检测规则信息")
    public static class CheckRuleInfo {
        @Schema(description = "检测数量")
        private Integer number;
        @Schema(description = "判定方式(0:数量;1:比例)")
        private Integer judgeWay;
        @Schema(description = "合格判定比例")
        private Double qualifiedRate;
        @Schema(description = "合格判定数量")
        private int qualifiedNumber;
        @Schema(description = "检测项目列表")
        List<CheckItemInfo> checkItemInfoList;

        public Integer getNumber() {
            return number;
        }

        public CheckRuleInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Double getQualifiedRate() {
            return qualifiedRate;
        }

        public CheckRuleInfo setQualifiedRate(Double qualifiedRate) {
            this.qualifiedRate = qualifiedRate;
            return this;
        }

        public Integer getJudgeWay() {
            return judgeWay;
        }

        public CheckRuleInfo setJudgeWay(Integer judgeWay) {
            this.judgeWay = judgeWay;
            return this;
        }

        public int getQualifiedNumber() {
            return qualifiedNumber;
        }

        public CheckRuleInfo setQualifiedNumber(int qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public List<CheckItemInfo> getCheckItemInfoList() {
            return checkItemInfoList;
        }

        public CheckRuleInfo setCheckItemInfoList(List<CheckItemInfo> checkItemInfoList) {
            this.checkItemInfoList = checkItemInfoList;
            return this;
        }

        @Schema(description = "检测项目信息")
        public static class CheckItemInfo {
            @Schema(description = "检测项目ID")
            @JsonSerialize(using = ToStringSerializer.class)
            private Long checkItemId;
            @Schema(description = "检测项目编码")
            private String checkItemCode;
            @Schema(description = "检测项目名称")
            private String checkItemName;
            @Schema(description = "合格范围(开闭区间或者OK)")
            private String qualifiedRange;
            /**
             * 项目类型
             */
            @Schema(description = "项目类型")
            private Integer variety;


            public CheckItemInfo() {

            }

            public CheckItemInfo(PedigreeStepCheckItem pedigreeStepCheckItem) {
                this.checkItemId = pedigreeStepCheckItem.getCheckItem().getId();
                this.checkItemCode = pedigreeStepCheckItem.getCheckItem().getCode();
                this.checkItemName = pedigreeStepCheckItem.getCheckItem().getName();
                this.qualifiedRange = pedigreeStepCheckItem.getQualifiedRange();
//                this.variety = pedigreeStepCheckItem.getCheckItem().getVariety();
            }

            public Long getCheckItemId() {
                return checkItemId;
            }

            public CheckItemInfo setCheckItemId(Long checkItemId) {
                this.checkItemId = checkItemId;
                return this;
            }

            public Integer getVariety() {
                return variety;
            }

            public CheckItemInfo setVariety(Integer variety) {
                this.variety = variety;
                return this;
            }

            public String getCheckItemCode() {
                return checkItemCode;
            }

            public CheckItemInfo setCheckItemCode(String checkItemCode) {
                this.checkItemCode = checkItemCode;
                return this;
            }

            public String getCheckItemName() {
                return checkItemName;
            }

            public CheckItemInfo setCheckItemName(String checkItemName) {
                this.checkItemName = checkItemName;
                return this;
            }

            public String getQualifiedRange() {
                return qualifiedRange;
            }

            public CheckItemInfo setQualifiedRange(String qualifiedRange) {
                this.qualifiedRange = qualifiedRange;
                return this;
            }
        }
    }
}
