package net.airuima.rworker.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class GrrCycleConfigDTO {
    /**
     * 台位编码
     */
    @Schema(description = "台位编码")
    private String workCellCode;

    /**
     * 是否管控(0:否;1:是)
     */
    @Schema(description = "是否管控(0:否;1:是)")
    private boolean isEnable;

    /**
     * 周期数值
     */
    @Schema(description = "周期数值")
    private int period;

    /**
     * 周期单位(0:年;1:月;2:周;3:天)
     */
    @Schema(description = "周期单位(0:年;1:月;2:周;3:天)")
    private int unit;

    /**
     * 到期提醒周期数值
     */
    @Schema(description = "到期提醒周期数值")
    private int tipPeriod;

    /**
     * 到期提醒周期单位
     */
    @Schema(description = "到期提醒周期单位(0:年;1:月;2:周;3:天)")
    private int tipUnit;

    /**
     * 到期时间
     */
    @Schema(description = "到期时间")
    private LocalDateTime expireTime;


    public String getWorkCellCode() {
        return workCellCode;
    }

    public GrrCycleConfigDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public GrrCycleConfigDTO setIsEnable(boolean enable) {
        isEnable = enable;
        return this;
    }

    public int getPeriod() {
        return period;
    }

    public GrrCycleConfigDTO setPeriod(int period) {
        this.period = period;
        return this;
    }

    public int getUnit() {
        return unit;
    }

    public GrrCycleConfigDTO setUnit(int unit) {
        this.unit = unit;
        return this;
    }

    public int getTipPeriod() {
        return tipPeriod;
    }

    public GrrCycleConfigDTO setTipPeriod(int tipPeriod) {
        this.tipPeriod = tipPeriod;
        return this;
    }

    public int getTipUnit() {
        return tipUnit;
    }

    public GrrCycleConfigDTO setTipUnit(int tipUnit) {
        this.tipUnit = tipUnit;
        return this;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public GrrCycleConfigDTO setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
        return this;
    }
}
