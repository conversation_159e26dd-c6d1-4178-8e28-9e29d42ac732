package net.airuima.rworker.service.client.facility.inspect;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.proxy.rfms.RbaseFacilityProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/7
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class ClientFacilityInspectServiceImpl implements ClientFacilityInspectService {


    @Autowired
    private  RbaseFacilityProxy rbaseFacilityProxy;


    /**
     * 根据设备主键ID列表验证设备点巡检合规
     * @param facilityIds 设备主键ID列表
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @Override
    public BaseClientDTO validateFacilityInspect(List<Long> facilityIds) {
        rbaseFacilityProxy.validateFacilityPointInspect(facilityIds);
        rbaseFacilityProxy.validateFacilityPatrolInspect(facilityIds);
        return new BaseClientDTO(Constants.OK);
    }
}
