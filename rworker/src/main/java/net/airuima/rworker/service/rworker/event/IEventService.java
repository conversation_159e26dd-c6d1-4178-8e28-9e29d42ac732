package net.airuima.rworker.service.rworker.event;

import com.google.common.collect.ImmutableBiMap;
import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@FuncDefault
public interface IEventService {

    /**
     *
     * 通过事件配置发起流程和消息发送
     * @param eventConfigCode 事件配置编码
     * @param parameter 工作流参数
     * @param businessKey 业务单据号
     * @param message 发送消息
     * @return boolean 是否成功
     */
    @FuncInterceptor(value = "Event")
    default boolean startProcess(String eventConfigCode, ImmutableBiMap<String, Object> parameter,String businessKey, String message){
        return Boolean.FALSE;
    }

    /**
     * 请求工序验证是否存在未处理的人工发起事件
     * @param stepProcessBaseDTO 待投产工序基础信息
     */
    @FuncInterceptor(value = "Event")
    default void validateRequestStepExistUnProcessedEvent(RworkerStepProcessBaseDTO stepProcessBaseDTO){

    }

    /**
     *  验证物料批次是否存在未处理的人工发起事件
     * @param materialBatchList 物料批次列表
     */
    @FuncInterceptor(value = "Event")
    default void validateMaterialExistUnProcessEvent(List<String> materialBatchList){

    }
}
