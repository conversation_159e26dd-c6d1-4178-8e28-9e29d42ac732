package net.airuima.rworker.service.rworker.process.impl;

import net.airuima.constant.Constants;
import net.airuima.rbase.constant.SnWorkStatusEnum;
import net.airuima.rbase.constant.StepIntervalEnum;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.pedigree.PedigreeSnReuseConfig;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepIntervalConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.quality.StepIntervalEvent;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerContainerStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerSnStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.rbase.dto.skill.SkillDTO;
import net.airuima.rbase.proxy.skill.RbaseSkillProxy;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepWorkCellRepository;
import net.airuima.rbase.repository.procedure.quality.StepIntervalEventRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.DateUtils;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.rworker.dto.client.GrrCycleConfigDTO;
import net.airuima.rworker.dto.client.GrrLedgerDTO;
import net.airuima.rworker.proxy.RworkerGrrHistoryProxy;
import net.airuima.rworker.proxy.RworkerGrrLedgerProxy;
import net.airuima.rworker.service.rworker.process.IToDoStepStepValidateService;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 待做工序的额外验证信息ServiceImpl
 *
 * <AUTHOR>
 * @date 2023/2/21
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class ToDoStepStepValidateServiceImpl implements IToDoStepStepValidateService {
    private static final String STEP_INTERVAL_ERROR = "stepIntervalError";
    private static final String OVER_INTERVAL_DURATION = "工序时间间隔不合规,请处理";
    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseSkillProxy rbaseSkillProxy;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private WsStepWorkCellRepository wsStepWorkCellRepository;
    @Autowired
    private RworkerGrrHistoryProxy rworkerGrrHistoryProxy;
    @Autowired
    private RworkerGrrLedgerProxy rworkerGrrLedgerProxy;
    @Autowired
    private StepIntervalEventRepository stepIntervalEventRepository;
    @Autowired
    private ContainerRepository containerRepository;

    /**
     * 验证员工技能是否匹配当前待做工序
     *
     * @param rworkerStepProcessBaseDTO 生产过程通用基础新消息
     */
    @Override
    public void validateStaffSkill(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        List<SkillDTO> stepSkillList = commonService.findPedigreeStepSkills(rworkerStepProcessBaseDTO.getWorkSheet().getPedigree(), rworkerStepProcessBaseDTO.getWorkSheet().getWorkFlow(), rworkerStepProcessBaseDTO.getStep());
        if (!ValidateUtils.isValid(stepSkillList)) {
            return;
        }
        List<SkillDTO> staffSkillList = rbaseSkillProxy.findLegalSkillByStaffIdAndDeleted(rworkerStepProcessBaseDTO.getStaffDTO().getId(), LocalDate.now(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(staffSkillList)) {
            throw new ResponseException("error.stepSkillNotMatched", "员工技能不匹配当前待做工序");
        }
        List<Long> staffSkillIdList = staffSkillList.stream().map(SkillDTO::getId).toList();
        List<Long> stepSkillIdList = stepSkillList.stream().map(SkillDTO::getId).toList();
        if (stepSkillIdList.stream().noneMatch(staffSkillIdList::contains)) {
            throw new ResponseException("error.stepSkillNotMatched", "员工技能不匹配当前待做工序");
        }
    }

    /**
     * 验证工单请求时的工序时间间隔
     *
     * @param rworkerStepProcessBaseDTO 生产通用基础数据
     * @param subWsProductionMode       投产粒度
     */
    @Override
    public BaseResultDTO validateBatchStepInterval(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, boolean subWsProductionMode) {
        List<PedigreeStepIntervalConfig> matchedPedigreeStepIntervalConfigList = this.findMatchedPedigreeStepIntervalConfig(rworkerStepProcessBaseDTO);
        if (CollectionUtils.isEmpty(matchedPedigreeStepIntervalConfigList)) {
            return new BaseResultDTO(Constants.OK);
        }
        //首先判断是否有未处理的工序间隔异常
        List<StepIntervalEvent> stepIntervalEventList = subWsProductionMode ? stepIntervalEventRepository.findBySubWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), Constants.LONG_ZERO)
                : stepIntervalEventRepository.findByWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getStatus() == Constants.INT_ZERO)) {
            throw new ResponseException(STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        List<StepIntervalEvent> stepIntervalEvents = new ArrayList<>();
        //其次判断前置工序与当前工序时间间隔合规，若不合规且无异常记录则记录异常
        matchedPedigreeStepIntervalConfigList.forEach(pedigreeStepIntervalConfig -> {
            Optional<BatchWorkDetail> batchWorkDetailOptional = subWsProductionMode ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), pedigreeStepIntervalConfig.getPreStep().getId(), Constants.LONG_ZERO)
                    : batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), pedigreeStepIntervalConfig.getPreStep().getId(), Constants.LONG_ZERO);
            batchWorkDetailOptional.ifPresent(preBatchWorkDetail -> {
                if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(preBatchWorkDetail.getStep().getId()))) {
                    return;
                }
                if (!validateStepInterval(preBatchWorkDetail.getEndDate(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                    StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                    stepIntervalEvent.setPreStep(preBatchWorkDetail.getStep()).setWorkSheet(!subWsProductionMode?rworkerStepProcessBaseDTO.getWorkSheet():null)
                            .setSubWorkSheet(subWsProductionMode?rworkerStepProcessBaseDTO.getSubWorkSheet():null).setOwnerId(rworkerStepProcessBaseDTO.getStaffDTO().getId()).setStatus(Constants.INT_ZERO)
                            .setWorkCell(rworkerStepProcessBaseDTO.getWorkCell()).setStep(rworkerStepProcessBaseDTO.getStep()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                    stepIntervalEvents.add(stepIntervalEvent);
                }
            });
        });
        if (!CollectionUtils.isEmpty(stepIntervalEvents)) {
            stepIntervalEventRepository.saveAll(stepIntervalEvents);
            return new BaseResultDTO(Constants.KO, STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        return new BaseResultDTO(Constants.OK);
    }


    /**
     * 批量生产下交时验证工序是否符合时间间隔
     *
     * @param rworkerBatchStepSaveRequestDTO 保存批量工序请求参数
     * @param rworkerStepProcessBaseDTO      工序生产过程通用基础数据
     * <AUTHOR>
     * @date 2023/10/11
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public BaseResultDTO validateBatchStepIntervalWhenSave(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        List<PedigreeStepIntervalConfig> matchedPedigreeStepIntervalConfigList = this.findMatchedSelfPedigreeStepIntervalConfig(rworkerStepProcessBaseDTO);
        if (CollectionUtils.isEmpty(matchedPedigreeStepIntervalConfigList)) {
            return new BaseResultDTO(Constants.OK);
        }
        boolean subWsProductionMode =  rworkerStepProcessBaseDTO.getSubWsProductionMode();
        //首先判断是否有未处理的工序间隔异常
        List<StepIntervalEvent> stepIntervalEventList = subWsProductionMode ? stepIntervalEventRepository.findBySubWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), Constants.LONG_ZERO)
                : stepIntervalEventRepository.findByWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(stepIntervalEventList) && (stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getStatus() == Constants.INT_ZERO))) {
            throw new ResponseException(STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        List<StepIntervalEvent> stepIntervalEvents = new ArrayList<>();
        matchedPedigreeStepIntervalConfigList.forEach(pedigreeStepIntervalConfig -> {
            if (!validateStepInterval(rworkerBatchStepSaveRequestDTO.getStartTime(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(rworkerStepProcessBaseDTO.getStep().getId()))) {
                    return;
                }
                StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                stepIntervalEvent.setPreStep(rworkerStepProcessBaseDTO.getStep()).setWorkSheet(!subWsProductionMode?rworkerStepProcessBaseDTO.getWorkSheet():null)
                        .setSubWorkSheet(subWsProductionMode?rworkerStepProcessBaseDTO.getSubWorkSheet():null).setOwnerId(rworkerStepProcessBaseDTO.getStaffDTO().getId()).setStatus(Constants.INT_ZERO)
                        .setWorkCell(rworkerStepProcessBaseDTO.getWorkCell()).setStep(rworkerStepProcessBaseDTO.getStep()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                stepIntervalEvents.add(stepIntervalEvent);
            }
        });
        if (!CollectionUtils.isEmpty(stepIntervalEvents)) {
            stepIntervalEventRepository.saveAll(stepIntervalEvents);
            return new BaseResultDTO(Constants.KO, STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 验证容器请求时的工序时间间隔
     *
     * @param rworkerStepProcessBaseDTO 工序生产过程基础数据
     * @param containerId               请求容器主键ID
     * @param subWsProductionMode       投产粒度
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public BaseResultDTO validateContainerStepInterval(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, Long containerId, boolean subWsProductionMode) {

        List<PedigreeStepIntervalConfig> matchedPedigreeStepIntervalConfigList = this.findMatchedPedigreeStepIntervalConfig(rworkerStepProcessBaseDTO);
        if (CollectionUtils.isEmpty(matchedPedigreeStepIntervalConfigList)) {
            return new BaseResultDTO(Constants.OK);
        }
        List<StepIntervalEvent> stepIntervalEventList = subWsProductionMode ? stepIntervalEventRepository.findBySubWorkSheetIdAndStepIdAndRequestContainerIdAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), containerId, Constants.LONG_ZERO)
                : stepIntervalEventRepository.findByWorkSheetIdAndStepIdAndRequestContainerIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), containerId, Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getStatus() == Constants.INT_ZERO)) {
            throw new ResponseException(STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        List<StepIntervalEvent> stepIntervalEvents = new ArrayList<>();
        ContainerDetail containerDetail = subWsProductionMode ?
                containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), containerId, Constants.LONG_ZERO)
                : containerDetailRepository.findTop1ByBatchWorkDetailWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getWorkSheet().getId(), containerId, Constants.LONG_ZERO);
        matchedPedigreeStepIntervalConfigList.forEach(pedigreeStepIntervalConfig -> {
            //优先判断容器流转的工序是否超出时间间隔
            List<ContainerDetail> preContainerDetailList = Lists.newArrayList();
            if (null != containerDetail) {
                findStepContainerDetail(containerDetail, pedigreeStepIntervalConfig.getPreStep(), preContainerDetailList);
            }
            if (!CollectionUtils.isEmpty(preContainerDetailList)) {
                preContainerDetailList.forEach(preContainerDetail -> {
                    if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream()
                            .anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(preContainerDetail.getBatchWorkDetail().getStep().getId())
                                    && stepIntervalEvent.getPreContainer().getId().equals(preContainerDetail.getContainer().getId()))) {
                        return;
                    }
                    if (!validateStepInterval(preContainerDetail.getBindTime(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                        StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                        stepIntervalEvent.setPreStep(preContainerDetail.getBatchWorkDetail().getStep()).setWorkSheet(!subWsProductionMode?rworkerStepProcessBaseDTO.getWorkSheet():null).setRequestContainer(new Container(containerId))
                                .setPreContainer(new Container(preContainerDetail.getContainer().getId()))
                                .setSubWorkSheet(subWsProductionMode?rworkerStepProcessBaseDTO.getSubWorkSheet():null).setOwnerId(rworkerStepProcessBaseDTO.getStaffDTO().getId()).setStatus(Constants.INT_ZERO)
                                .setWorkCell(rworkerStepProcessBaseDTO.getWorkCell()).setStep(rworkerStepProcessBaseDTO.getStep()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                        stepIntervalEvents.add(stepIntervalEvent);
                    }
                });
                return;
            }
            //若无容器流转数据则判断前置批量详情数据是否超出时间间隔
            if (CollectionUtils.isEmpty(preContainerDetailList)) {
                Optional<BatchWorkDetail> preBatchWorkDetailOptional = subWsProductionMode ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), pedigreeStepIntervalConfig.getPreStep().getId(), Constants.LONG_ZERO) : batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), pedigreeStepIntervalConfig.getPreStep().getId(), Constants.LONG_ZERO);
                preBatchWorkDetailOptional.ifPresent(preBatchWorkDetail -> {
                    if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(preBatchWorkDetail.getStep().getId()))) {
                        return;
                    }
                    if (!validateStepInterval(preBatchWorkDetail.getEndDate(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                        StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                        stepIntervalEvent.setPreStep(preBatchWorkDetail.getStep()).setWorkSheet(!subWsProductionMode?rworkerStepProcessBaseDTO.getWorkSheet():null).setRequestContainer(new Container(containerId))
                                .setSubWorkSheet(subWsProductionMode?rworkerStepProcessBaseDTO.getSubWorkSheet():null).setOwnerId(rworkerStepProcessBaseDTO.getStaffDTO().getId()).setStatus(Constants.INT_ZERO)
                                .setWorkCell(rworkerStepProcessBaseDTO.getWorkCell()).setStep(rworkerStepProcessBaseDTO.getStep()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                        stepIntervalEvents.add(stepIntervalEvent);
                    }
                });
            }
        });
        if (!CollectionUtils.isEmpty(stepIntervalEvents)) {
            stepIntervalEventRepository.saveAll(stepIntervalEvents);
            return new BaseResultDTO(Constants.KO, STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 容器生产下交时验证工序是否符合时间间隔
     *
     * @param rworkerContainerStepSaveRequestDTO 保存容器工序下交参数
     * @param containerStepSaveBaseInfo          工序生产过程通用基础数据
     * <AUTHOR>
     * @date 2023/10/11
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public BaseResultDTO validateContainerStepIntervalWhenSave(RworkerContainerStepSaveRequestDTO rworkerContainerStepSaveRequestDTO, RworkerStepProcessBaseDTO containerStepSaveBaseInfo) {
        if (CollectionUtils.isEmpty(rworkerContainerStepSaveRequestDTO.getBingContainerInfoList())) {
            return new BaseResultDTO(Constants.OK);
        }
        List<PedigreeStepIntervalConfig> matchedPedigreeStepIntervalConfigList = this.findMatchedSelfPedigreeStepIntervalConfig(containerStepSaveBaseInfo);
        if (CollectionUtils.isEmpty(matchedPedigreeStepIntervalConfigList)) {
            return new BaseResultDTO(Constants.OK);
        }
        //首先判断是否有未处理的工序间隔异常
        Set<Long> requestContainerIds = new HashSet<>();
        rworkerContainerStepSaveRequestDTO.getBingContainerInfoList().forEach(bingContainerInfo ->{
            if(!CollectionUtils.isEmpty(bingContainerInfo.getRequestContainerInfoList())){
                requestContainerIds.addAll(bingContainerInfo.getRequestContainerInfoList().stream().map(RworkerContainerStepSaveRequestDTO.BingContainerInfo.RequestContainerInfo::getId).toList());
            }
        } );
        boolean subWsProductionMode = containerStepSaveBaseInfo.getSubWsProductionMode();
        List<StepIntervalEvent> stepIntervalEventList;
        if(!CollectionUtils.isEmpty(requestContainerIds)){
            stepIntervalEventList = subWsProductionMode ? stepIntervalEventRepository.findBySubWorkSheetIdAndStepIdAndRequestContainerIdInAndDeleted(containerStepSaveBaseInfo.getSubWorkSheet().getId(), containerStepSaveBaseInfo.getStep().getId(), requestContainerIds.stream().toList(), Constants.LONG_ZERO) :
                    stepIntervalEventRepository.findByWorkSheetIdAndStepIdAndRequestContainerIdInAndDeleted(containerStepSaveBaseInfo.getWorkSheet().getId(), containerStepSaveBaseInfo.getStep().getId(), requestContainerIds.stream().toList(), Constants.LONG_ZERO);
        }else {
            stepIntervalEventList = subWsProductionMode ? stepIntervalEventRepository.findBySubWorkSheetIdAndStepIdAndDeleted(containerStepSaveBaseInfo.getSubWorkSheet().getId(), containerStepSaveBaseInfo.getStep().getId(), Constants.LONG_ZERO)
                    : stepIntervalEventRepository.findByWorkSheetIdAndStepIdAndDeleted(containerStepSaveBaseInfo.getWorkSheet().getId(), containerStepSaveBaseInfo.getStep().getId(), Constants.LONG_ZERO);
        }
        if (!CollectionUtils.isEmpty(stepIntervalEventList) && (stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getStatus() == Constants.INT_ZERO))) {
            throw new ResponseException(STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        List<StepIntervalEvent> stepIntervalEvents = new ArrayList<>();
        if(!CollectionUtils.isEmpty(requestContainerIds)) {
            matchedPedigreeStepIntervalConfigList.forEach(pedigreeStepIntervalConfig -> {
                if (!validateStepInterval(rworkerContainerStepSaveRequestDTO.getStartTime(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                    requestContainerIds.forEach(requestContainerId -> {
                        if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream()
                                .anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(containerStepSaveBaseInfo.getStep().getId())
                                        && stepIntervalEvent.getPreContainer().getId().equals(requestContainerId))) {
                            return;
                        }
                        StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                        stepIntervalEvent.setPreStep(containerStepSaveBaseInfo.getStep()).setWorkSheet(!subWsProductionMode?containerStepSaveBaseInfo.getWorkSheet():null).setRequestContainer(new Container(requestContainerId))
                                .setSubWorkSheet(subWsProductionMode?containerStepSaveBaseInfo.getSubWorkSheet():null).setOwnerId(containerStepSaveBaseInfo.getStaffDTO().getId()).setStatus(Constants.INT_ZERO).setPreContainer(new Container(requestContainerId))
                                .setWorkCell(containerStepSaveBaseInfo.getWorkCell()).setStep(containerStepSaveBaseInfo.getStep()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                        stepIntervalEvents.add(stepIntervalEvent);
                    });
                }
            });
        }else {
            matchedPedigreeStepIntervalConfigList.forEach(pedigreeStepIntervalConfig -> {
                if (!validateStepInterval(rworkerContainerStepSaveRequestDTO.getStartTime(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                    if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(containerStepSaveBaseInfo.getStep().getId()))) {
                        return;
                    }
                    StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                    stepIntervalEvent.setPreStep(containerStepSaveBaseInfo.getStep()).setWorkSheet(!subWsProductionMode?containerStepSaveBaseInfo.getWorkSheet():null)
                            .setSubWorkSheet(subWsProductionMode?containerStepSaveBaseInfo.getSubWorkSheet():null).setOwnerId(containerStepSaveBaseInfo.getStaffDTO().getId()).setStatus(Constants.INT_ZERO)
                            .setWorkCell(containerStepSaveBaseInfo.getWorkCell()).setStep(containerStepSaveBaseInfo.getStep()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                    stepIntervalEvents.add(stepIntervalEvent);
                }
            });
        }
        if (!CollectionUtils.isEmpty(stepIntervalEvents)) {
            stepIntervalEventRepository.saveAll(stepIntervalEvents);
            return new BaseResultDTO(Constants.KO, STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 验证SN请求时的工序时间间隔
     *
     * @param rworkerStepProcessBaseDTO 工序生产过程基础数据
     * @param sn                        请求SN
     * @param subWsProductionMode       投产粒度
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public BaseResultDTO validateSnStepInterval(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, String sn, boolean subWsProductionMode) {
        List<PedigreeStepIntervalConfig> matchedPedigreeStepIntervalConfigList = this.findMatchedPedigreeStepIntervalConfig(rworkerStepProcessBaseDTO);
        if (CollectionUtils.isEmpty(matchedPedigreeStepIntervalConfigList)) {
            return new BaseResultDTO(Constants.OK);
        }
        List<StepIntervalEvent> stepIntervalEventList = subWsProductionMode ? stepIntervalEventRepository.findBySubWorkSheetIdAndStepIdAndSnAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), sn, Constants.LONG_ZERO)
                : stepIntervalEventRepository.findByWorkSheetIdAndStepIdAndSnAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), sn, Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getStatus() == Constants.INT_ZERO)) {
            throw new ResponseException(STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        Optional<SnWorkDetail> snWorkDetailOptional = subWsProductionMode ? snWorkDetailRepository.findTop1BySubWorkSheetIdAndSnAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), sn, Constants.LONG_ZERO)
                : snWorkDetailRepository.findTop1ByWorkSheetIdAndSnAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getWorkSheet().getId(), sn, Constants.LONG_ZERO);
        List<Long> preIntervalStepIdList = matchedPedigreeStepIntervalConfigList.stream().map(pedigreeStepIntervalConfig -> pedigreeStepIntervalConfig.getPreStep().getId()).toList();
        List<ContainerDetail> preContainerDetailList = new ArrayList<>();
        if (snWorkDetailOptional.isPresent() && Objects.nonNull(snWorkDetailOptional.get().getContainerDetail())) {
            this.findAllPreContainerDetail(snWorkDetailOptional.get().getContainerDetail(), preContainerDetailList);
            preContainerDetailList.add(snWorkDetailOptional.get().getContainerDetail());
            preContainerDetailList = preContainerDetailList.stream().filter(preContainerDetail -> preIntervalStepIdList.contains(preContainerDetail.getBatchWorkDetail().getId())).toList();
        }
        List<StepIntervalEvent> stepIntervalEvents = new ArrayList<>();
        List<ContainerDetail> finalPreContainerDetailList = preContainerDetailList;
        matchedPedigreeStepIntervalConfigList.forEach(pedigreeStepIntervalConfig -> {
            //优先判断SN流转下来的前置工序是否超出时间间隔
            Optional<SnWorkDetail> preSnWorkDetailOptional = subWsProductionMode ? snWorkDetailRepository.findTop1BySubWorkSheetIdAndStepIdAndSnAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), pedigreeStepIntervalConfig.getPreStep().getId(), sn, Constants.LONG_ZERO)
                    : snWorkDetailRepository.findTop1ByWorkSheetIdAndStepIdAndSnAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getWorkSheet().getId(), pedigreeStepIntervalConfig.getPreStep().getId(), sn, Constants.LONG_ZERO);
            if (preSnWorkDetailOptional.isPresent()) {
                if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(preSnWorkDetailOptional.get().getStep().getId()))) {
                    return;
                }
                if (!validateStepInterval(preSnWorkDetailOptional.get().getEndDate(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                    StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                    stepIntervalEvent.setPreStep(pedigreeStepIntervalConfig.getPreStep()).setWorkSheet(!subWsProductionMode?rworkerStepProcessBaseDTO.getWorkSheet():null).setSn(sn).setStep(rworkerStepProcessBaseDTO.getStep())
                            .setSubWorkSheet(subWsProductionMode?rworkerStepProcessBaseDTO.getSubWorkSheet():null).setOwnerId(rworkerStepProcessBaseDTO.getStaffDTO().getId()).setStatus(Constants.INT_ZERO)
                            .setWorkCell(rworkerStepProcessBaseDTO.getWorkCell()).setDeleted(Constants.LONG_ZERO);
                    stepIntervalEvents.add(stepIntervalEvent);
                }
                return;
            }
            //其次判断是否存在容器流转下来的时间间隔超出
            if (snWorkDetailOptional.isPresent() && null != snWorkDetailOptional.get().getContainerDetail() && !CollectionUtils.isEmpty(finalPreContainerDetailList)) {
                finalPreContainerDetailList.forEach(preContainerDetail -> {
                    if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream()
                            .anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(preContainerDetail.getBatchWorkDetail().getId())
                                    && Objects.nonNull(stepIntervalEvent.getPreContainer()) && stepIntervalEvent.getPreContainer().getId().equals(preContainerDetail.getContainer().getId()))) {
                        return;
                    }
                    if (!validateStepInterval(preContainerDetail.getBindTime(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                        StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                        stepIntervalEvent.setPreStep(preContainerDetail.getBatchWorkDetail().getStep()).setWorkSheet(!subWsProductionMode?rworkerStepProcessBaseDTO.getWorkSheet():null)
                                .setPreContainer(new Container(preContainerDetail.getContainer().getId())).setSn(sn)
                                .setSubWorkSheet(subWsProductionMode?rworkerStepProcessBaseDTO.getSubWorkSheet():null).setOwnerId(rworkerStepProcessBaseDTO.getStaffDTO().getId()).setStatus(Constants.INT_ZERO)
                                .setWorkCell(rworkerStepProcessBaseDTO.getWorkCell()).setStep(rworkerStepProcessBaseDTO.getStep()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                        stepIntervalEvents.add(stepIntervalEvent);
                    }
                });
            } else if (snWorkDetailOptional.isPresent()) {
                //最后判断批量前置工序是否超出时间间隔
                Optional<BatchWorkDetail> batchWorkDetailOptional = subWsProductionMode ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), pedigreeStepIntervalConfig.getPreStep().getId(), Constants.LONG_ZERO) : batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), pedigreeStepIntervalConfig.getPreStep().getId(), Constants.LONG_ZERO);
                batchWorkDetailOptional.ifPresent(batchWorkDetail -> {
                    if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(batchWorkDetail.getStep().getId()))) {
                        return;
                    }
                    if (!validateStepInterval(batchWorkDetail.getEndDate(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                        StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                        stepIntervalEvent.setPreStep(batchWorkDetail.getStep()).setWorkSheet(!subWsProductionMode?rworkerStepProcessBaseDTO.getWorkSheet():null)
                                .setSn(sn).setSubWorkSheet(subWsProductionMode?rworkerStepProcessBaseDTO.getSubWorkSheet():null).setOwnerId(rworkerStepProcessBaseDTO.getStaffDTO().getId()).setStatus(Constants.INT_ZERO)
                                .setWorkCell(rworkerStepProcessBaseDTO.getWorkCell()).setStep(rworkerStepProcessBaseDTO.getStep()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                        stepIntervalEvents.add(stepIntervalEvent);
                    }
                });
            }
        });
        if (!CollectionUtils.isEmpty(stepIntervalEvents)) {
            stepIntervalEventRepository.saveAll(stepIntervalEvents);
            return new BaseResultDTO(Constants.KO, STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 单支生产下交时验证工序是否符合时间间隔
     *
     * @param rworkerSnStepSaveRequestDTO 保存SN工序生产数据下交参数
     * @param rworkerStepProcessBaseDTO   工序生产过程基础数据
     * <AUTHOR>
     * @date 2023/10/11
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public BaseResultDTO validateSnStepIntervalWhenSave(RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        List<PedigreeStepIntervalConfig> matchedPedigreeStepIntervalConfigList = this.findMatchedSelfPedigreeStepIntervalConfig(rworkerStepProcessBaseDTO);
        if (CollectionUtils.isEmpty(matchedPedigreeStepIntervalConfigList)) {
            return new BaseResultDTO(Constants.OK);
        }
        Set<String> snSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList())) {
            rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList().forEach(containerSnSaveInfo -> {
                snSet.addAll(containerSnSaveInfo.getSingleSnSaveInfoList().stream().map(RworkerSnStepSaveRequestDTO.SingleSnSaveInfo::getSn).toList());
            });
        } else {
            snSet.addAll(rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().stream().map(RworkerSnStepSaveRequestDTO.SingleSnSaveInfo::getSn).toList());
        }
        boolean subWsProductionMode = rworkerStepProcessBaseDTO.getSubWsProductionMode();
        List<StepIntervalEvent> stepIntervalEventList = subWsProductionMode ? stepIntervalEventRepository.findBySubWorkSheetIdAndStepIdAndSnInAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), snSet.stream().toList(), Constants.LONG_ZERO)
                : stepIntervalEventRepository.findByWorkSheetIdAndStepIdAndSnInAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), snSet.stream().toList(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream().anyMatch(stepIntervalEvent -> stepIntervalEvent.getStatus() == Constants.INT_ZERO)) {
            throw new ResponseException(STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        List<StepIntervalEvent> stepIntervalEvents = new ArrayList<>();
        matchedPedigreeStepIntervalConfigList.forEach(pedigreeStepIntervalConfig -> {
            if (!validateStepInterval(rworkerSnStepSaveRequestDTO.getStartTime(), LocalDateTime.now(), pedigreeStepIntervalConfig)) {
                snSet.forEach(sn -> {
                    if (!CollectionUtils.isEmpty(stepIntervalEventList) && stepIntervalEventList.stream()
                            .anyMatch(stepIntervalEvent -> stepIntervalEvent.getPreStep().getId().equals(rworkerStepProcessBaseDTO.getStep().getId())
                                    && stepIntervalEvent.getSn().equals(sn))) {
                        return;
                    }
                    StepIntervalEvent stepIntervalEvent = new StepIntervalEvent();
                    stepIntervalEvent.setPreStep(rworkerStepProcessBaseDTO.getStep()).setWorkSheet(!subWsProductionMode?rworkerStepProcessBaseDTO.getWorkSheet():null)
                            .setSubWorkSheet(subWsProductionMode?rworkerStepProcessBaseDTO.getSubWorkSheet():null).setOwnerId(rworkerStepProcessBaseDTO.getStaffDTO().getId()).setStatus(Constants.INT_ZERO).setSn(sn)
                            .setWorkCell(rworkerStepProcessBaseDTO.getWorkCell()).setStep(rworkerStepProcessBaseDTO.getStep()).setRecordTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
                    stepIntervalEvents.add(stepIntervalEvent);
                });
            }
        });
        if (!CollectionUtils.isEmpty(stepIntervalEvents)) {
            stepIntervalEventRepository.saveAll(stepIntervalEvents);
            return new BaseResultDTO(Constants.KO, STEP_INTERVAL_ERROR, OVER_INTERVAL_DURATION);
        }
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 验证SN是否复用
     *
     * @param subWsProductionMode 生产粒度(true:子工单;false:工单)
     * @param snWorkStatus        SN生产状态
     * @param workSheet           当前投产工单
     * @return boolean
     */
    @Override
    @Transactional(readOnly = true)
    public boolean validateSnReuse(boolean subWsProductionMode, SnWorkStatus snWorkStatus, WorkSheet workSheet) {
        if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            return Boolean.FALSE;
        }
        if (null == snWorkStatus) {
            return Boolean.FALSE;
        }
        if (subWsProductionMode && snWorkStatus.getSubWorkSheet().getWorkSheet().getId().equals(workSheet.getId())) {
            return Boolean.FALSE;
        }
        if (!subWsProductionMode && snWorkStatus.getWorkSheet().getId().equals(workSheet.getId())) {
            return Boolean.FALSE;
        }
        PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(workSheet.getPedigree());
        if (null == pedigreeConfig || !pedigreeConfig.getIsReuseSN()) {
            return Boolean.FALSE;
        }
        List<PedigreeSnReuseConfig> pedigreeSnReuseConfigs = commonService.findPedigreeSnReuseConfig(workSheet.getPedigree());
        if (CollectionUtils.isEmpty(pedigreeSnReuseConfigs)) {
            return Boolean.FALSE;
        }
        //待复用工单
        WorkSheet reUseWorkSheet = subWsProductionMode ? snWorkStatus.getSubWorkSheet().getWorkSheet() : snWorkStatus.getWorkSheet();
        //待复用工单对应的产品谱系数组
        List<Pedigree> reUsePedigrees = Lists.newArrayList();
        commonService.findParentPedigreeGroupByLevel(reUseWorkSheet.getPedigree()).forEach((key, value) -> reUsePedigrees.addAll(value));
        //如果产品谱系复用配置数据中被复用的产品谱系都不包含待复用工单对应的产品谱系的话则不允许复用
        if (pedigreeSnReuseConfigs.stream().noneMatch(pedigreeReuseConfig -> reUsePedigrees.stream().anyMatch(reUsePedigree -> reUsePedigree.getId().equals(pedigreeReuseConfig.getReusePedigree().getId())))) {
            return Boolean.FALSE;
        }
        if (snWorkStatus.getStatus() <= SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus()) {
            throw new ResponseException("error.unFinishedSnNotBeReUse", "SN尚未完成，不可进行复用转产");
        }
        if (snWorkStatus.getStatus() != SnWorkStatusEnum.QUALIFIED.getStatus()) {
            throw new ResponseException("error.unQualifiedSnNotBeReUse", "SN不合格，不可进行复用转产");
        }
        return Boolean.TRUE;
    }

    /**
     * 通过下个待做工序快照获取匹配到的时间间隔配置
     *
     * @param rworkerStepProcessBaseDTO 生产通用基础数据
     * @return java.util.List<net.airuima.domain.base.pedigree.PedigreeStepIntervalConfig>
     */
    @Transactional(readOnly = true)
    public List<PedigreeStepIntervalConfig> findMatchedPedigreeStepIntervalConfig(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        // 如果不存在前置工序，直接 return
        if (StringUtils.isBlank(rworkerStepProcessBaseDTO.getWsStep().getPreStepId())) {
            return Collections.emptyList();
        }
        //获取工序时长间隔配置
        List<PedigreeStepIntervalConfig> pedigreeStepIntervalConfigList = commonService.findPedigreeStepIntervalConfig(rworkerStepProcessBaseDTO.getWorkSheet().getPedigree(), rworkerStepProcessBaseDTO.getStep(), rworkerStepProcessBaseDTO.getWorkFlow(),rworkerStepProcessBaseDTO.getWsStepList());
        if (!ValidateUtils.isValid(pedigreeStepIntervalConfigList)) {
            return Collections.emptyList();
        }
        pedigreeStepIntervalConfigList = new ArrayList<>(pedigreeStepIntervalConfigList);
        //获取当前生产工序除去自身的时长间隔配置
        pedigreeStepIntervalConfigList.stream().filter(pedigreeStepIntervalConfig -> pedigreeStepIntervalConfig.getPreStep().getId().equals(rworkerStepProcessBaseDTO.getStep().getId())).findFirst().ifPresent(pedigreeStepIntervalConfigList::remove);        if (CollectionUtils.isEmpty(pedigreeStepIntervalConfigList)) {
            return Collections.emptyList();
        }
        List<WsStep> parentWsStepList = Lists.newArrayList();
        commonService.findParentWsStep(rworkerStepProcessBaseDTO.getWsStepList(),parentWsStepList,rworkerStepProcessBaseDTO.getWsStep());
        parentWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).toList();
        List<Long> preStepIds = parentWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).toList();
        return pedigreeStepIntervalConfigList.stream().filter(pedigreeStepIntervalConfig -> preStepIds.contains(pedigreeStepIntervalConfig.getPreStep().getId())).collect(Collectors.toList());
    }

    /**
     * 获取工序自身比较的时间间隔配置
     *
     * @param rworkerStepProcessBaseDTO
     * @return java.util.List<net.airuima.domain.base.pedigree.PedigreeStepIntervalConfig>
     * <AUTHOR>
     * @date 2023/10/11
     */
    @Transactional(readOnly = true)
    public List<PedigreeStepIntervalConfig> findMatchedSelfPedigreeStepIntervalConfig(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        //获取工序时长间隔配置
        List<PedigreeStepIntervalConfig> pedigreeStepIntervalConfigList = commonService.findPedigreeStepIntervalConfig(rworkerStepProcessBaseDTO.getWorkSheet().getPedigree(), rworkerStepProcessBaseDTO.getStep(), rworkerStepProcessBaseDTO.getWorkFlow(),rworkerStepProcessBaseDTO.getWsStepList());
        if (!ValidateUtils.isValid(pedigreeStepIntervalConfigList)) {
            return Collections.emptyList();
        }
        return pedigreeStepIntervalConfigList.stream().filter(pedigreeStepIntervalConfig -> pedigreeStepIntervalConfig.getPreStep().getId().equals(rworkerStepProcessBaseDTO.getStep().getId())).collect(Collectors.toList());
    }

    /**
     * 通过当前容器详情找到指定工序的前置流转容器详情
     *
     * @param containerDetail        容器详情
     * @param step                   匹配工序
     * @param preContainerDetailList 前置容器详情列表
     */
    @Transactional(readOnly = true)
    public void findStepContainerDetail(ContainerDetail containerDetail, Step step, List<ContainerDetail> preContainerDetailList) {
        if (containerDetail.getBatchWorkDetail().getStep().getId().equals(step.getId())) {
            preContainerDetailList.add(containerDetail);
        }
        if (!CollectionUtils.isEmpty(containerDetail.getPreContainerDetailInfoList())) {
            for (PreContainerDetailInfo preContainerDetailInfo : containerDetail.getPreContainerDetailInfoList()) {
                ContainerDetail preContainerDetail = containerDetailRepository.getReferenceById(preContainerDetailInfo.getPreContainerDetailId());
                findStepContainerDetail(preContainerDetail, step, preContainerDetailList);
            }
        }
    }


    /**
     * 验证当前（子）工单工位，是否符合工单工序指派的工位
     *
     * @param rworkerStepProcessBaseDTO 工序生产过程基础数据
     * @date 2022/8/11
     */
    @Override
    @Transactional(readOnly = true)
    public void validWsStepWorkCell(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        List<WsStepWorkCell> wsStepWorkCellList = rworkerStepProcessBaseDTO.getSubWsProductionMode()
                ? wsStepWorkCellRepository.findBySubWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), Constants.LONG_ZERO) :
                wsStepWorkCellRepository.findByWorkSheetIdAndStepIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), rworkerStepProcessBaseDTO.getStep().getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepWorkCellList)) {
            return;
        }
        boolean isExist = wsStepWorkCellList.stream().anyMatch(wsStepWorkCell -> rworkerStepProcessBaseDTO.getWorkCell().getId().equals(wsStepWorkCell.getWorkCell().getId()));
        if (!isExist) {
            throw new ResponseException("error.wsStepWorkCellNotMatched", "工单工序指定的工位，不包括当前工位");
        }
    }

    /**
     * 验证工位GRR是否合规
     *
     * @param rworkerStepProcessBaseDTO 工序生产过程基础数据
     */
    @Override
    @Transactional(readOnly = true)
    public void validateWorkCellGrr(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        GrrCycleConfigDTO grrCycleConfigDTO = rworkerGrrHistoryProxy.findByWorkCellCodeAndDeleted(rworkerStepProcessBaseDTO.getWorkCell().getCode(), Constants.LONG_ZERO).orElse(null);
        if (null != grrCycleConfigDTO && grrCycleConfigDTO.getIsEnable()) {
            GrrLedgerDTO grrLedgerDTO = rworkerGrrLedgerProxy.findByWorkCellCodeAndDeleted(rworkerStepProcessBaseDTO.getWorkCell().getCode(), Constants.LONG_ZERO).orElse(null);
            if (null == grrLedgerDTO || grrLedgerDTO.getExpireTime().isBefore(LocalDateTime.now())) {
                throw new ResponseException("error.workCellGrrExpired", "工位GRR已超期!");
            }
        }
    }

    /**
     * 验证工序时间间隔是否合规
     *
     * @param startTime                  开始时间
     * @param endTime                    结束时间
     * @param pedigreeStepIntervalConfig 间隔配置
     * @return 比较结果
     */
    public boolean validateStepInterval(LocalDateTime startTime, LocalDateTime endTime, PedigreeStepIntervalConfig pedigreeStepIntervalConfig) {
        String waitCompareDuration = String.valueOf(DateUtils.LocalDateTimeSeconds(startTime, endTime));
        String range = pedigreeStepIntervalConfig.getDuration();
        boolean result = Boolean.TRUE;
        if (pedigreeStepIntervalConfig.getDurationUnit() == StepIntervalEnum.MINUTE.getUnit()) {
            result = ToolUtils.compareIntervalWithUnit(range, waitCompareDuration, 60);
        } else if (pedigreeStepIntervalConfig.getDurationUnit() == StepIntervalEnum.HOUR.getUnit()) {
            result = ToolUtils.compareIntervalWithUnit(range, waitCompareDuration, 3600);
        } else if (pedigreeStepIntervalConfig.getDurationUnit() == StepIntervalEnum.DAY.getUnit()) {
            result = ToolUtils.compareIntervalWithUnit(range, waitCompareDuration, 86400);
        }else if(pedigreeStepIntervalConfig.getDurationUnit() == StepIntervalEnum.SECOND.getUnit()){
            result = ToolUtils.compareIntervalWithUnit(range, waitCompareDuration, 1);
        }
        return result;
    }

    private List<ContainerDetail> findAllPreContainerDetail(ContainerDetail containerDetail, List<ContainerDetail> containerDetailList) {
        if (containerDetail.getPreContainerDetailInfoList() != null && containerDetail.getPreContainerDetailInfoList().size() > 0) {
            for (PreContainerDetailInfo preContainerDetailInfo : containerDetail.getPreContainerDetailInfoList()) {
                ContainerDetail preContainerDetail = containerDetailRepository.getReferenceById(preContainerDetailInfo.getPreContainerDetailId());
                containerDetailList.add(preContainerDetail);
                findAllPreContainerDetail(preContainerDetail, containerDetailList);
            }
        }
        return containerDetailList;
    }
}
