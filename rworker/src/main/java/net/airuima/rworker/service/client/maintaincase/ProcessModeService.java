package net.airuima.rworker.service.client.maintaincase;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.dto.client.ClientSaveStepInfoDTO;

import java.util.Collections;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/28
 */
public interface ProcessModeService {


    default BaseClientDTO maintainAnalyse(List<Long> requestContainerIds, SubWorkSheet subWorkSheet, WsStep currWsStep){
        return new BaseClientDTO(Constants.OK);
    }


    /**
     * 保存工序批次详情信息
     * @param subWorkSheet 子工单
     * @param wsStep 工序
     * @param workCell 工位
     * @param clientSaveStepInfoDto 请求保存信息DTO
     * <AUTHOR>
     * @date  2022/9/28
     * @return BatchWorkDetail
     */
    default BatchWorkDetail saveBatchWorkDetail(SubWorkSheet subWorkSheet, WsStep wsStep, WorkCell workCell, ClientSaveStepInfoDTO clientSaveStepInfoDto){
        return new BatchWorkDetail();
    }

    /**
     * 保存容器详情信息
     *
     * @param clientSaveStepInfoDto 请求保存的参数
     * @param batchWorkDetail       批量详情
     * @param isLastStep            是否为最后一个工序
     * @return ContainerDetail
     **/
    default ContainerDetail saveContainerInfo(ClientSaveStepInfoDTO clientSaveStepInfoDto, BatchWorkDetail batchWorkDetail,WsStep wsStep, boolean isLastStep){
        return null;
    }

    /**
     * 保存SN相关信息
     *
     * @param clientSaveStepInfoDto 待保存的工序信息
     * @param subWorkSheet          子工单
     * @param step                  工序
     * @param workCell              工位
     * @param containerDetail       容器详情
     * @param isLastStep            是否为最后一个工序
     */
    default List<SnWorkDetail> saveSnInfo(ClientSaveStepInfoDTO clientSaveStepInfoDto, SubWorkSheet subWorkSheet, Step step,
                                          WorkCell workCell, ContainerDetail containerDetail, boolean isLastStep) {
        return Collections.emptyList();
    }

    /**
     * 保存前置容器详情信息，创建当前容器详情
     * @param clientSaveStepInfoDto 保存内容
     * @param batchWorkDetail 当前批次
     * @param isLastStep 是否为最后工序
     * <AUTHOR>
     * @date  2022/10/18
     * @return ContainerDetail
     */
    default ContainerDetail savePreContainerDetails(ClientSaveStepInfoDTO clientSaveStepInfoDto, BatchWorkDetail batchWorkDetail,WsStep wsStep, boolean isLastStep){
        return null;
    }

}
