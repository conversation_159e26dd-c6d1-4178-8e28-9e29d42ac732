package net.airuima.rworker.service.rworker.process;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.rworker.process.dto.*;
import net.airuima.rworker.web.rest.rworker.process.dto.RworkerBindSnValidateGetDTO;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker单支生产过程请求相关Service
 * <AUTHOR>
 * @date 2023/3/28
 */
@FuncDefault
public interface ISnProcessRequestService {

    /**
     * 工单请求时获取所属工单投产SN信息
     * @param batchStepSaveBaseInfo 批量待做工序信息
     * @param rworkerBatchToDoStepGetDTO 批量待投产工序信息DTO
     */
    @FuncInterceptor("Single")
    default void findToDoBatchSn(RworkerStepProcessBaseDTO batchStepSaveBaseInfo, RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO){

    }

    /**
     *
     * 容器请求时获取容器内所拥有的SN列表
     * @param containerId 容器主键ID
     * @param rworkerContainerToDoStepGetDTO 容器待生产工序信息DTO
     * @param rworkerStepProcessBaseDTO 请求通用参数DTO
     */
    @FuncInterceptor("Container && Single")
    default void findToDoContainerSn(long containerId, RworkerContainerToDoStepGetDTO rworkerContainerToDoStepGetDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){

    }

    /**
     * 工单请求单支生产时验证SN是否合规(一般为第一个生产模式为单支的工序会进行验证)
     * @param rworkerBindSnValidateRequestDTO 验证参数DTO
     */
    @FuncInterceptor("Single")
    default RworkerBindSnValidateGetDTO validate(RworkerBindSnValidateRequestDTO rworkerBindSnValidateRequestDTO){
        return null;
    }

    /**
     *
     * @param sn 生产SN
     * @param workSheet 生产工单
     * @param subWorkSheet 生产子工单
     * @param subWsProductionMode 投产模式
     */
    @FuncInterceptor("Single && WorkSheetBindSn")
    default void validateWorkSheetBindSn(String sn, WorkSheet workSheet, SubWorkSheet subWorkSheet, boolean subWsProductionMode){

    }


    /**
     *
     * 获取SN请求模式下的待做工序信息
     * @param rworkerSnTodoRequestDTO SN请求工序生产参数DTO
     * @return net.airuima.web.rest.rworker.process.dto.RworkerSnToDoStepGetDTO SN待投产工序生产信息DTO
     */
    @FuncInterceptor("Single")
    default RworkerSnToDoStepGetDTO snTodoStep(RworkerSnTodoRequestDTO rworkerSnTodoRequestDTO){
        return null;
    }

    /**
     * 验证包装Sn合法性
     * @param sn
     * @param workSheet
     */
    @FuncInterceptor(value = "RmpsService")
    default BaseResultDTO validatePackageProcessSn(String sn, WorkSheet workSheet){
        BaseResultDTO resultDTO = new BaseResultDTO();
        resultDTO.setData(Boolean.FALSE);
        resultDTO.setStatus(Constants.OK);
        return resultDTO;
    }

}
