package net.airuima.rworker.service.rworker.process.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.MaintainEnum;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDetailDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerToDoWsGetDTO;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryDetailProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rworker.service.rworker.process.IWorkSheetApsService;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WorkSheetApsServiceImpl implements IWorkSheetApsService {


    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private RbaseMaintainHistoryDetailProxy rbaseMaintainHistoryDetailProxy;

    /**
     * 通过工位主键ID获取待做工单列表
     * @param workCellId 工位主键ID
     * @return java.util.List<net.airuima.web.rest.rworker.process.dto.RworkerToDoWsGetDTO> 待做工单列表信息
     */
    @Override
    @Transactional(readOnly = true)
    public List<RworkerToDoWsGetDTO> findToDoWs(Long workCellId) {
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        //投产方式为子工单
        if (StringUtils.isBlank(mode) || Boolean.parseBoolean(mode)) {
            return this.findToDoSubWs(workCellId);
        }
        //投产方式为工单
        return this.findToDoSumWs(workCellId);
    }

    /**
     * 通过(子)工单号获取投产工单信息
     *
     * @param serialNumber (子)工单号
     * @return net.airuima.rbase.web.rest.rworker.process.dto.RworkerToDoWsGetDTO 待做工单信息
     */
    @Override
    @Transactional(readOnly = true)
    public RworkerToDoWsGetDTO findToDoWsBySerialNumber(String serialNumber) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        if (subWsProductionMode) {
            SubWorkSheet subWorkSheet = subWorkSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.subWorkSheetNotExist", "子工单不存在!"));
            WorkSheet workSheet = subWorkSheet.getWorkSheet();
            if(Objects.isNull(workSheet)){
                throw new ResponseException("error.workSheetNotExist", "工单不存在!");
            }
            if(Objects.isNull(workSheet.getWorkFlow())){
                throw new ResponseException("error.workFlowNotExist", "工单工艺路线不存在!");
            }
            //根据子工单获取可能存在的维修分析产生的返工单
            return new RworkerToDoWsGetDTO(findMaintainReworkSubWorkSheet(subWorkSheet));
        } else {
            WorkSheet currentWorkSheet = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null);
            //验证工单是否存在
            if (null == currentWorkSheet) {
                throw new ResponseException("error.workSheetNotExist", "工单不存在!");
            }
            if(Objects.isNull(currentWorkSheet.getWorkFlow())){
                throw new ResponseException("error.workFlowNotExist", "工单工艺路线不存在!");
            }
            //根据工单递归获取可能存在的维修分析产生的返工单
            return new RworkerToDoWsGetDTO(findMaintainReworkWorkSheet(currentWorkSheet));
        }
    }

    /**
     * 根据扫描的工单递归获取可能存在的维修分析产生的返工单
     *
     * @param workSheet 扫描或者选择的投产工单
     * @return net.airuima.rbase.domain.procedure.aps.WorkSheet 工单
     */
    @Override
    @Transactional(readOnly = true)
    public WorkSheet findMaintainReworkWorkSheet(WorkSheet workSheet) {
        if (!(workSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && workSheet.getStatus() <= ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName())) {
            throw new ResponseException("error.workSheetIsFinishedOrCanceled", "工单已完成或已取消!");
        }
        MaintainHistoryDTO notFinishedMaintainHistory = rbaseMaintainHistoryProxy.findTop1ByWorkSheetIdAndStatusNotAndDeleted(workSheet.getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO).orElse(null);
        if (null != notFinishedMaintainHistory) {
            throw new ResponseException("error.subWorkSheetMaintainNotFinished", "工单维修分析尚未完成!");
        }
        MaintainHistoryDTO maintainHistory = rbaseMaintainHistoryProxy.findTop1ByWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
        if (null == maintainHistory || maintainHistory.getResult() == MaintainEnum.MAINTAIN_RESULT_RELEASE.getStatus()) {
            if (!(workSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && workSheet.getStatus() <= ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName())) {
                throw new ResponseException("error.workSheetIsFinishedOrCanceled", "工单已完成或已取消!");
            }
            return workSheet;
        }
        if (maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
            throw new ResponseException("error.subWorkSheetWaitForAnalyze", "工单处于待分析中!");
        }
        if (maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
            throw new ResponseException("error.subWorkSheetWaitForMaintain", "工单处于待维修中!");
        }
        List<MaintainHistoryDetailDTO> maintainHistoryDetails = rbaseMaintainHistoryDetailProxy.findByMaintainHistoryIdAndResultAndDeleted(maintainHistory.getId(), MaintainEnum.MAINTAIN_RESULT_REWORK.getStatus(),Constants.LONG_ZERO);
        if (ValidateUtils.isValid(maintainHistoryDetails)){
            if (maintainHistoryDetails.size() > Constants.INT_ONE){
                throw new ResponseException("error.multiReworkWorkSheet", "工单维修产生的返工单存在多个"+maintainHistoryDetails
                        .stream().map(maintainHistoryDetail -> maintainHistoryDetail.getWsRework().getReworkWorkSheet().getSerialNumber())
                        .collect(Collectors.joining(Constants.STR_SEMICOLON))+"不能通过原始工单直接请求!");
            }
            return findMaintainReworkWorkSheet(maintainHistoryDetails.get(Constants.INT_ZERO).getWsRework().getReworkWorkSheet());
        }
        return workSheet;
    }

    /**
     * 根据扫描的子工单递归获取可能存在的维修分析产生的返工单
     *
     * @param subWorkSheet 扫描或者选择的投产子工单
     * @return net.airuima.rbase.domain.procedure.aps.SubWorkSheet 子工单
     */
    @Override
    @Transactional(readOnly = true)
    public SubWorkSheet findMaintainReworkSubWorkSheet(SubWorkSheet subWorkSheet) {
        if (!(subWorkSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && subWorkSheet.getStatus() <= ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName())) {
            throw new ResponseException("error.subWorkSheetIsFinishedOrCanceled", "子工单已完成或已取消!");
        }
        MaintainHistoryDTO notFinishedMaintainHistory = rbaseMaintainHistoryProxy.findTop1BySubWorkSheetIdAndStatusNotAndDeleted(subWorkSheet.getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO).orElse(null);
        if (null != notFinishedMaintainHistory) {
            throw new ResponseException("error.subWorkSheetMaintainNotFinished", "工单维修分析尚未完成!");
        }
        MaintainHistoryDTO maintainHistory = rbaseMaintainHistoryProxy.findTop1BySubWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(subWorkSheet.getId(), Constants.LONG_ZERO).orElse(null);
        if (null == maintainHistory || maintainHistory.getResult() == MaintainEnum.MAINTAIN_RESULT_RELEASE.getStatus()) {
            if (!(subWorkSheet.getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && subWorkSheet.getStatus() <= ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName())) {
                throw new ResponseException("error.subWorkSheetIsFinishedOrCanceled", "子工单已完成或已取消!");
            }
            return subWorkSheet;
        }
        if (maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
            throw new ResponseException("error.subWorkSheetWaitForAnalyze", "工单处于待分析中!");
        }
        if (maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
            throw new ResponseException("error.subWorkSheetWaitForMaintain", "工单处于待维修中!");
        }
        if(maintainHistory.getResult() != MaintainEnum.MAINTAIN_RESULT_REWORK.getStatus()){
            return subWorkSheet;
        }
        List<MaintainHistoryDetailDTO> maintainHistoryDetails = rbaseMaintainHistoryDetailProxy.findByMaintainHistoryIdAndResultAndDeleted(maintainHistory.getId(),MaintainEnum.MAINTAIN_RESULT_REWORK.getStatus(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(maintainHistoryDetails)){
            if (maintainHistoryDetails.size() > Constants.INT_ONE){
                throw new ResponseException("error.multiReworkSubWorkSheet", "子工单维修产生的返工单存在多个"+maintainHistoryDetails
                        .stream().map(maintainHistoryDetail -> maintainHistoryDetail.getWsRework().getReworkWorkSheet().getSerialNumber())
                        .collect(Collectors.joining(Constants.STR_SEMICOLON))+"不能通过原始子工单直接请求!");
            }
            SubWorkSheet reworkSubWorkSheet = subWorkSheetRepository.findTop1ByWorkSheetIdAndDeleted(maintainHistoryDetails.get(Constants.INT_ZERO).getWsRework().getReworkWorkSheet().getId(), Constants.LONG_ZERO).orElse(null);
            if (null == reworkSubWorkSheet) {
                throw new ResponseException("error.reworkSubWorkSheetNotGenerated", "维修产生的返工单未分子工单!");
            }
            return findMaintainReworkSubWorkSheet(reworkSubWorkSheet);
        }
        return subWorkSheet;
    }

    /**
     * 通过工位ID获取待做子工单列表
     *
     * @param workCellId 工位ID
     * @return List<RworkerToDoWsGetDTO>
     */
    private List<RworkerToDoWsGetDTO> findToDoSubWs(Long workCellId) {
        List<RworkerToDoWsGetDTO> rworkerToDoWsGetDTOList = Lists.newArrayList();
        WorkCell workCell = workCellRepository.getReferenceById(workCellId);
        if (null == workCell.getWorkLine()) {
            throw new ResponseException("error.workCellNotBindWorkLine", "工位未关联生产线");
        }
        //获取已下单以及正在投产中的子工单列表
        List<Integer> processedStatuses = Arrays.asList(ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName());
        List<SubWorkSheet> subWorkSheets = subWorkSheetRepository.findByWorkLineIdAndStatusInAndDeleted(workCell.getWorkLine().getId(), processedStatuses, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(subWorkSheets)) {
            return rworkerToDoWsGetDTOList;
        }
        List<Step> stepList = workCellStepRepository.findByWorkCellId(workCellId);
        Map<WorkSheet, List<SubWorkSheet>> subWorkSheetGroups = subWorkSheets.stream().collect(Collectors.groupingBy(SubWorkSheet::getWorkSheet));
        subWorkSheetGroups.forEach((workSheet, subWorkSheetList) -> {
            final List<WsStep>[] wsSteps = new List[]{wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO)};
            subWorkSheetList.forEach(subWorkSheet -> {
                List<WsStep> subWsSteps = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
                wsSteps[0] = ValidateUtils.isValid(subWsSteps) ? subWsSteps : wsSteps[0];
                //获取当前子工单批量详情列表
                List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
                boolean firstStepCanDo = wsSteps[0].stream().anyMatch(wsStep -> !ValidateUtils.isValid(wsStep.getPreStepId()) && stepList.stream().anyMatch(
                        step -> step.getId().equals(wsStep.getStep().getId())));
                //若当前子工单未开工且工位能生产第一个工序则将工单放入待做列表中
                if (!ValidateUtils.isValid(batchWorkDetailList) && firstStepCanDo) {
                    rworkerToDoWsGetDTOList.add(new RworkerToDoWsGetDTO(subWorkSheet));
                    return;
                }
                //工单工序快照进行分组排序
                List<List<WsStep>> lists = commonService.dealWsStep(wsSteps[0]);
                if (ValidateUtils.isValid(batchWorkDetailList) && Boolean.TRUE.equals(ableTodoWorkSheet(lists, stepList, batchWorkDetailList))) {
                    rworkerToDoWsGetDTOList.add(new RworkerToDoWsGetDTO(subWorkSheet));
                }

            });
        });
        if (!ValidateUtils.isValid(rworkerToDoWsGetDTOList)) {
            return rworkerToDoWsGetDTOList;
        }
        Map<Integer, List<RworkerToDoWsGetDTO>> rworkerToDoWsGetDtoGroupMap = rworkerToDoWsGetDTOList.stream().sorted(Comparator.comparing(RworkerToDoWsGetDTO::getPriority).reversed()).collect(Collectors.toList()).stream().collect(Collectors.groupingBy(RworkerToDoWsGetDTO::getPriority));
        Map<Integer, List<RworkerToDoWsGetDTO>> orderedGroupMap = rworkerToDoWsGetDtoGroupMap.entrySet().stream().sorted(Map.Entry.<Integer, List<RworkerToDoWsGetDTO>>comparingByKey().reversed()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        ;
        List<RworkerToDoWsGetDTO> finalRworkerToDoWsGetDTOList = Lists.newArrayList();
        orderedGroupMap.forEach((priority, rworkerToDoWsGetDtos) -> {
            finalRworkerToDoWsGetDTOList.addAll(rworkerToDoWsGetDtos.stream().sorted(Comparator.comparing(RworkerToDoWsGetDTO::getPlanEndDate)).collect(Collectors.toList()));
        });
        return finalRworkerToDoWsGetDTOList;

    }

    /**
     * 通过工位ID获取待做工单列表
     *
     * @param workCellId 工位ID
     * @return List<RworkerToDoWsGetDTO>
     */
    @Transactional(readOnly = true)
    public List<RworkerToDoWsGetDTO> findToDoSumWs(Long workCellId) {
        List<RworkerToDoWsGetDTO> rworkerToDoWsGetDTOList = Lists.newArrayList();
        WorkCell workCell = workCellRepository.getReferenceById(workCellId);
        if (null == workCell.getWorkLine()) {
            throw new ResponseException("error.workCellNotBindWorkLine", "工位未关联生产线");
        }
        //获取已下单以及正在投产中的子工单列表
        List<Integer> processedStatuses = Arrays.asList(ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName());
        List<WorkSheet> workSheets = workSheetRepository.findByWorkLineIdAndStatusInAndDeleted(workCell.getWorkLine().getId(), processedStatuses, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workSheets)) {
            return rworkerToDoWsGetDTOList;
        }
        List<Step> stepList = workCellStepRepository.findByWorkCellId(workCellId);
        workSheets.forEach(workSheet -> {
            List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            //获取当前子工单批量详情列表
            List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            boolean firstStepCanDo = wsStepList.stream().anyMatch(wsStep -> !ValidateUtils.isValid(wsStep.getPreStepId()) && stepList.stream().anyMatch(
                    step -> step.getId().equals(wsStep.getStep().getId())));
            //若当前子工单未开工且工位能生产第一个工序则将工单放入待做列表中
            if (!ValidateUtils.isValid(batchWorkDetailList) && firstStepCanDo) {
                rworkerToDoWsGetDTOList.add(new RworkerToDoWsGetDTO(workSheet));
                return;
            }
            //工单工序快照进行分组排序
            List<List<WsStep>> lists = commonService.dealWsStep(wsStepList);
            if (ValidateUtils.isValid(batchWorkDetailList) && Boolean.TRUE.equals(ableTodoWorkSheet(lists, stepList, batchWorkDetailList))) {
                rworkerToDoWsGetDTOList.add(new RworkerToDoWsGetDTO(workSheet));
            }
        });
        Map<Integer, List<RworkerToDoWsGetDTO>> rworkerToDoWsGetDtoGroupMap = rworkerToDoWsGetDTOList.stream().sorted(Comparator.comparing(RworkerToDoWsGetDTO::getPriority).reversed()).collect(Collectors.toList()).stream().collect(Collectors.groupingBy(RworkerToDoWsGetDTO::getPriority));
        Map<Integer, List<RworkerToDoWsGetDTO>> orderedGroupMap = rworkerToDoWsGetDtoGroupMap.entrySet().stream().sorted(Map.Entry.<Integer, List<RworkerToDoWsGetDTO>>comparingByKey().reversed()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        ;
        List<RworkerToDoWsGetDTO> finalRworkerToDoWsGetDTOList = Lists.newArrayList();
        orderedGroupMap.forEach((priority, rworkerToDoWsGetDtos) -> {
            finalRworkerToDoWsGetDTOList.addAll(rworkerToDoWsGetDtos.stream().sorted(Comparator.comparing(RworkerToDoWsGetDTO::getPlanEndDate)).collect(Collectors.toList()));
        });
        return finalRworkerToDoWsGetDTOList;
    }


    /**
     * 判断工位可生产当前工单
     *
     * @param lists               工单工艺快照分层排序的列表
     * @param stepList            工位绑定的工序列表
     * @param batchWorkDetailList 批量详情列表
     * @return Boolean
     */
    private Boolean ableTodoWorkSheet(List<List<WsStep>> lists, List<Step> stepList, List<BatchWorkDetail> batchWorkDetailList) {
        //按照工序对工单详情进行分组
        Map<Long, List<BatchWorkDetail>> collect = batchWorkDetailList.stream().collect(Collectors.groupingBy(batchWorkDetail -> batchWorkDetail.getStep().getId()));
        for (List<WsStep> wsSteps : lists) {
            List<WsStep> mayNextToDoWsSteps = wsSteps.stream().filter(wsStep -> stepList.stream().anyMatch(step -> step.getId().equals(wsStep.getStep().getId()))).collect(Collectors.toList());
            if (!ValidateUtils.isValid(mayNextToDoWsSteps)) {
                continue;
            }
            for (WsStep wsStep : mayNextToDoWsSteps) {
                List<BatchWorkDetail> batchWorkDetails = collect.get(wsStep.getStep().getId());
                BatchWorkDetail batchWorkDetail = ValidateUtils.isValid(batchWorkDetails) ? batchWorkDetails.get(Constants.INT_ZERO) : null;
                //若工序已完成则继续判断下一个工序
                if (null != batchWorkDetail && batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
                    continue;
                }
                //第一个工序未完成则放入待做列表
                if (StringUtils.isBlank(wsStep.getPreStepId())) {
                    return Boolean.TRUE;
                }
                //前置工序未完成则继续判断下一个匹配到的工序
                if (Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA))
                        .noneMatch(preStepId -> collect.get(Long.parseLong(preStepId)) == null ||
                                collect.get(Long.parseLong(preStepId)).stream().anyMatch(currBatchWorkDetail -> currBatchWorkDetail.getFinish() == ConstantsEnum.UNFINISHED_STATUS.getCategoryName()))) {
                    return Boolean.TRUE;
                }

            }
        }
        return Boolean.FALSE;
    }

    /**
     * 完工上传
     * @param wsStep 工序快照
     * @param subWorkSheet 子工单
     * @param workSheet 工单
     */
    @Override
    public void wsCompleteUpload(WsStep wsStep, SubWorkSheet subWorkSheet, WorkSheet workSheet) {
    }
}
