package net.airuima.rworker.service.client.facility.inspect;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.client.base.BaseClientDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/7
 */
@FuncDefault
public interface ClientFacilityInspectService {

    /**
     * 根据设备主键ID列表验证设备点巡检合规
     * @param facilityIds 设备主键ID列表
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor(value = "FBase && FInspection")
    default BaseClientDTO validateFacilityInspect(List<Long> facilityIds){
        return new BaseClientDTO(Constants.OK);
    }
}
