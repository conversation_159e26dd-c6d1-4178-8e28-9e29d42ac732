package net.airuima.rworker.service.rworker.process;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerContainerStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerSnStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 待做工序的额外验证信息Service
 * <AUTHOR>
 * @date 2023/2/21
 */
@FuncDefault
public interface IToDoStepStepValidateService {

    /**
     * 验证员工技能是否匹配当前待做工序
     * @param rworkerStepProcessBaseDTO 生产过程通用基础新消息
     */
    @FuncInterceptor("StaffSkillControl")
    default void validateStaffSkill(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){

    }

    /**
     * 验证工单请求时的工序时间间隔
     * @param rworkerStepProcessBaseDTO 生产通用基础数据
     * @param subWsProductionMode 投产粒度
     */
    @FuncInterceptor("StepInterval")
    default BaseResultDTO validateBatchStepInterval(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, boolean subWsProductionMode){
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 批量生产下交时验证工序是否符合时间间隔
     * @param rworkerBatchStepSaveRequestDTO  保存批量工序请求参数
     * @param rworkerStepProcessBaseDTO 工序生产过程通用基础数据
     * <AUTHOR>
     */
    @FuncInterceptor("StepInterval")
    default BaseResultDTO validateBatchStepIntervalWhenSave(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 验证容器请求时的工序时间间隔
     * @param rworkerStepProcessBaseDTO 工序生产过程基础数据
     * @param containerId 请求容器主键ID
     * @param subWsProductionMode 投产粒度
     */
    @FuncInterceptor("StepInterval")
    default BaseResultDTO validateContainerStepInterval(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO,Long containerId,boolean subWsProductionMode){
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 容器生产下交时验证工序是否符合时间间隔
     * @param rworkerContainerStepSaveRequestDTO 保存容器工序下交参数
     * @param containerStepSaveBaseInfo    工序生产过程通用基础数据
     * <AUTHOR>
     * @date 2023/10/11
     */
    @FuncInterceptor("StepInterval")
    default BaseResultDTO validateContainerStepIntervalWhenSave(RworkerContainerStepSaveRequestDTO rworkerContainerStepSaveRequestDTO, RworkerStepProcessBaseDTO containerStepSaveBaseInfo){
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 验证SN请求时的工序时间间隔
     * @param rworkerStepProcessBaseDTO 工序生产过程基础数据
     * @param sn 请求SN
     * @param subWsProductionMode 投产粒度
     */
    @FuncInterceptor("StepInterval")
    default BaseResultDTO validateSnStepInterval(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, String sn,boolean subWsProductionMode){
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 单支生产下交时验证工序是否符合时间间隔
     * @param rworkerSnStepSaveRequestDTO 保存SN工序生产数据下交参数
     * @param rworkerStepProcessBaseDTO 工序生产过程基础数据
     * <AUTHOR>
     */
    @FuncInterceptor("StepInterval")
    default BaseResultDTO validateSnStepIntervalWhenSave(RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){
        return new BaseResultDTO(Constants.OK);
    }

    /**
     * 验证SN是否复用
     * @param subWsProductionMode 生产粒度(true:子工单;false:工单)
     * @param snWorkStatus SN生产状态
     * @param workSheet 工单
     * @return boolean
     */
    @FuncInterceptor("Single && SnReuse")
    default boolean validateSnReuse(boolean subWsProductionMode, SnWorkStatus snWorkStatus, WorkSheet workSheet){
        return Boolean.FALSE;
    }

    /**
     * 验证当前（子）工单工位，是否符合工单工序指派的工位
     * @param rworkerStepProcessBaseDTO 工序生产过程基础数据
     */
    @FuncInterceptor("SpecificStepCell")
    default void validWsStepWorkCell(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){

    }

    /**
     * 验证工位GRR是否合规
     * @param rworkerStepProcessBaseDTO 工序生产过程基础数据
     */
    @FuncInterceptor("GRR")
    default void validateWorkCellGrr(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){

    }
}
