package net.airuima.rworker.service.rworker.process.impl;

import net.airuima.domain.base.AuditSfIdEntity;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.domain.procedure.single.WorkSheetSn;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.rmps.PackageYsnRelationDTO;
import net.airuima.rbase.dto.rworker.process.dto.*;
import net.airuima.rbase.dto.rworker.process.dto.general.FacilityGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.ProcessDocumentGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.SnGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.WsStepGetInfo;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryDetailProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.proxy.organization.RbaseStaffProxy;
import net.airuima.rbase.proxy.rmps.RbaseRmpsProxy;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.quality.CheckHistoryDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.repository.procedure.single.WorkSheetSnRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rworker.service.rworker.cache.IRworkerCacheService;
import net.airuima.rworker.service.rworker.dynamic.IDynamicService;
import net.airuima.rworker.service.rworker.event.IEventService;
import net.airuima.rworker.service.rworker.facility.*;
import net.airuima.rworker.service.rworker.material.IMaterialService;
import net.airuima.rworker.service.rworker.oem.IOemService;
import net.airuima.rworker.service.rworker.process.IBatchProcessRequestService;
import net.airuima.rworker.service.rworker.process.ISnProcessRequestService;
import net.airuima.rworker.service.rworker.process.IToDoStepConfigService;
import net.airuima.rworker.service.rworker.process.IToDoStepStepValidateService;
import net.airuima.rworker.service.rworker.quality.IEnvironmentService;
import net.airuima.rworker.service.rworker.quality.IQualityService;
import net.airuima.rworker.web.rest.rworker.process.dto.RworkerBindSnValidateGetDTO;
import net.airuima.util.BeanUtil;
import net.airuima.util.FuncKeyUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/3/29
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class SnProcessRequestServiceImpl implements ISnProcessRequestService {
    private static final String ERR_MSG = "当前工位无工序可生产";
    private static final String NOT_EXIST_TODO_STEP = "notExistTodoStep";

    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private RbaseStaffProxy rbaseStaffProxy;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private WorkFlowStepRepository workFlowStepRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private IMaterialService[] materialServices;
    @Autowired
    private IToDoStepStepValidateService[] toDoStepStepValidateServices;
    @Autowired
    private IEnvironmentService[] environmentServices;
    @Autowired
    private IFacilityService[] facilityServices;
    @Autowired
    private IFacilityCalibrateService[] facilityCalibrateServices;
    @Autowired
    private IFacilityInspectionService[] facilityInspectionServices;
    @Autowired
    private IFacilityMaintainService[] facilityMaintainServices;
    @Autowired
    private IQualityService[] qualityServices;
    @Autowired
    private IDynamicService[] dynamicServices;
    @Autowired
    private IToDoStepConfigService[] toDoStepConfigServices;
    @Autowired
    private IWearingPartService[] wearingPartServices;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private IEventService[] eventServices;
    @Autowired
    private CheckHistoryDetailRepository checkHistoryDetailRepository;
    @Autowired
    private IRworkerCacheService[] rworkerCacheServices;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private WorkSheetSnRepository workSheetSnRepository;
    @Autowired
    private IBatchProcessRequestService[] batchProcessRequestServices;
    @Autowired
    private RbaseRmpsProxy rbaseRmpsProxy;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;
    @Autowired
    private RbaseMaintainHistoryDetailProxy rbaseMaintainHistoryDetailProxy;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;
    @Autowired
    private IOemService[] oemServices;
    @Autowired
    private StepRepository stepRepository;

    /**
     * 工单请求时获取所属工单投产SN信息
     *
     * @param rworkerStepProcessBaseDTO  批量待做工序信息
     * @param rworkerBatchToDoStepGetDTO 批量待投产工序信息DTO
     */
    @Override
    public void findToDoBatchSn(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO) {
        //返工单请求时获取属于该返工单且sn状态为返修中的所有SN列表，否则获取投产中的SN列表
        int snWorkStatus = rworkerStepProcessBaseDTO.getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory() ? SnWorkStatusEnum.IN_THE_REPAIR.getStatus() : SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus();
        List<SnWorkStatus> snWorkStatuses = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode()) ?
                snWorkStatusRepository.findBySubWorkSheetIdAndStatusAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), snWorkStatus, Constants.LONG_ZERO)
                : snWorkStatusRepository.findByWorkSheetIdAndStatusAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), snWorkStatus, Constants.LONG_ZERO);

        //SN状态表没有数据则获取包装MPS(子)工单已生成的SN列表
        List<String> packageRelationSubWorkSheetSnList = new ArrayList<>();
        if (StringUtils.isBlank(rworkerStepProcessBaseDTO.getWsStep().getPreStepId()) && rworkerStepProcessBaseDTO.getWorkSheet().getCategory() == WsEnum.NORMAL_WS.getCategory() && CollectionUtils.isEmpty(snWorkStatuses) ) {
            packageRelationSubWorkSheetSnList = rbaseRmpsProxy.findWorkSheetProcessSn(rworkerStepProcessBaseDTO.getSubWsProductionMode()
                    ? rworkerStepProcessBaseDTO.getSubWorkSheet().getSerialNumber() : rworkerStepProcessBaseDTO.getWorkSheet().getSerialNumber(), rworkerStepProcessBaseDTO.getSubWsProductionMode());
        }
        //以上都没有SN列表则最后获取(子)工单关联SN列表
        List<WorkSheetSn> workSheetSnList = new ArrayList<>();
        if(CollectionUtils.isEmpty(packageRelationSubWorkSheetSnList) && CollectionUtils.isEmpty(snWorkStatuses) && StringUtils.isBlank(rworkerStepProcessBaseDTO.getWsStep().getPreStepId()) && rworkerStepProcessBaseDTO.getWorkSheet().getCategory() == WsEnum.NORMAL_WS.getCategory()){
            workSheetSnList = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode())
                    ? workSheetSnRepository.findBySubWorkSheetIdAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), Constants.LONG_ZERO)
                    : workSheetSnRepository.findByWorkSheetIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        if (CollectionUtils.isEmpty(snWorkStatuses) && CollectionUtils.isEmpty(workSheetSnList) && CollectionUtils.isEmpty(packageRelationSubWorkSheetSnList)) {
            return;
        }
        List<SnGetInfo> snGetInfoList = null;
        if (CollectionUtils.isEmpty(snWorkStatuses) && !CollectionUtils.isEmpty(packageRelationSubWorkSheetSnList)) {
            if(packageRelationSubWorkSheetSnList.size()!=rworkerBatchToDoStepGetDTO.getNumber()){
                throw new ResponseException("error.SnSizeNotMatchStepDevoteNumber","打标生成的SN个数和工序投产数不一致");
            }
            snGetInfoList = packageRelationSubWorkSheetSnList.stream().map(SnGetInfo::new).toList();
        } else if (CollectionUtils.isEmpty(snWorkStatuses)) {
            snGetInfoList = workSheetSnList.stream().map(SnGetInfo::new).toList();
            if(snGetInfoList.size()!=rworkerBatchToDoStepGetDTO.getNumber()){
                throw new ResponseException("error.SnSizeNotMatchStepDevoteNumber","SN个数和工序投产数不一致");
            }
        } else {
            if(rworkerBatchToDoStepGetDTO.getNumber()>Constants.INT_ZERO && (rworkerBatchToDoStepGetDTO.getCategory() ==  ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName()
                    || rworkerBatchToDoStepGetDTO.getCategory() ==ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName()) && !CollectionUtils.isEmpty(rworkerStepProcessBaseDTO.getOnlineReworkUnQualifiedItemList())){
                List<Long> onlineReworkUnqualifiedItemIds = rworkerStepProcessBaseDTO.getOnlineReworkUnQualifiedItemList().stream().map(UnqualifiedItem::getId).toList();
                snWorkStatuses = snWorkStatuses.stream().filter(snWorkStatus1 -> Objects.nonNull(snWorkStatus1.getLatestUnqualifiedItem())&&onlineReworkUnqualifiedItemIds.contains(snWorkStatus1.getLatestUnqualifiedItem().getId()))
                        .toList();
                if(CollectionUtils.isEmpty(snWorkStatuses)){
                    return;
                }
            }
            //若为返修单则应该要过滤在返修单中产生不合格的SN
            if (rworkerStepProcessBaseDTO.getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory()) {
                List<SnWorkStatus> unqualifiedSnWorkStatus = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode())
                        ? snWorkStatuses.stream().filter(snWorkStatusTemp -> Objects.nonNull(snWorkStatusTemp.getLatestReworkSnWorkDetail()) && snWorkStatusTemp.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(rworkerStepProcessBaseDTO.getSubWorkSheet().getId())).toList()
                        : snWorkStatuses.stream().filter(snWorkStatusTemp -> Objects.nonNull(snWorkStatusTemp.getLatestReworkSnWorkDetail()) && snWorkStatusTemp.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(rworkerStepProcessBaseDTO.getWorkSheet().getId())).toList();
                if (!CollectionUtils.isEmpty(unqualifiedSnWorkStatus)) {
                    snWorkStatuses.removeAll(unqualifiedSnWorkStatus);
                }
            }
            List<SnWorkStatus> snWorkStatusList;
            //如果是返工单且第一个工序时获取其待返工的SN列表
            if (rworkerStepProcessBaseDTO.getWorkSheet().getCategory() == WsEnum.ONLINE_RE_WS.getCategory() && StringUtils.isBlank(rworkerStepProcessBaseDTO.getWsStep().getPreStepId())) {
                snWorkStatusList = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode()) ?
                        snWorkStatuses.stream().filter(snWorkStatusTemp -> !snWorkStatusTemp.getLatestSnWorkDetail().getSubWorkSheet().getId().equals(rworkerStepProcessBaseDTO.getSubWorkSheet().getId())).collect(Collectors.toList())
                        : snWorkStatuses.stream().filter(snWorkStatusTemp -> !snWorkStatusTemp.getLatestSnWorkDetail().getWorkSheet().getId().equals(rworkerStepProcessBaseDTO.getWorkSheet().getId())).collect(Collectors.toList());
            } else {
                snWorkStatusList = snWorkStatuses.stream().filter(snWorkStatusTemp -> rworkerStepProcessBaseDTO.getWsStep().getPreStepId().contains(snWorkStatusTemp.getLatestSnWorkDetail().getStep().getId().toString())).collect(Collectors.toList());
            }
            //如果是正常工单第一个工序则还需要继续获取工单预绑定的SN
            if (CollectionUtils.isEmpty(snWorkStatusList) && rworkerStepProcessBaseDTO.getWorkSheet().getCategory() == WsEnum.NORMAL_WS.getCategory() && StringUtils.isBlank(rworkerStepProcessBaseDTO.getWsStep().getPreStepId())) {
                workSheetSnList = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode())
                        ? workSheetSnRepository.findBySubWorkSheetIdAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), Constants.LONG_ZERO)
                        : workSheetSnRepository.findByWorkSheetIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), Constants.LONG_ZERO);
                if (!CollectionUtils.isEmpty(workSheetSnList)) {
                    List<String> workStatusSnList = snWorkStatuses.stream().map(SnWorkStatus::getSn).toList();
                    List<WorkSheetSn> todoWorkSheetSnList = workSheetSnList.stream().filter(workSheetSn -> !workStatusSnList.contains(workSheetSn.getSn())).toList();
                    if (!CollectionUtils.isEmpty(todoWorkSheetSnList)) {
                        snGetInfoList = todoWorkSheetSnList.stream().map(SnGetInfo::new).toList();
                    }
                }
            } else if (CollectionUtils.isEmpty(snWorkStatusList)) {
                return;
            } else {
                snGetInfoList = snWorkStatusList.stream().map(SnGetInfo::new).toList();
            }
        }
        if (CollectionUtils.isEmpty(snGetInfoList)) {
            return;
        }
        if (snGetInfoList.stream().anyMatch(snGetInfo -> Objects.isNull(snGetInfo.getYsn()))) {
            //获取内部SN对应的YSN
            List<PackageYsnRelationDTO> packageYsnRelationDTOList = rbaseRmpsProxy.findYsn(snGetInfoList.stream().map(SnGetInfo::getSn).toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(packageYsnRelationDTOList)) {
                snGetInfoList.forEach(snGetInfo -> packageYsnRelationDTOList.stream().filter(packageYsnRelationDTO -> packageYsnRelationDTO.getSn().equals(snGetInfo.getSn())).findFirst().ifPresent(packageYsnRelationDTO -> {
                    snGetInfo.setYsn(packageYsnRelationDTO.getYsn());
                }));
            }
        }
        rworkerBatchToDoStepGetDTO.setSnInfoList(snGetInfoList);

    }

    /**
     * 容器请求时获取容器内所拥有的SN列表
     *
     * @param containerId                    容器主键ID
     * @param rworkerContainerToDoStepGetDTO 容器待生产工序信息DTO
     * @param rworkerStepProcessBaseDTO      请求通用参数DTO
     */
    @Override
    public void findToDoContainerSn(long containerId, RworkerContainerToDoStepGetDTO rworkerContainerToDoStepGetDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        int snWorkStatus = rworkerStepProcessBaseDTO.getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory() ? SnWorkStatusEnum.IN_THE_REPAIR.getStatus() : SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus();
        int wsCategory = rworkerStepProcessBaseDTO.getWorkSheet().getCategory();
        List<SnWorkStatus> snWorkStatuses = null;
        ContainerDetail containerDetail = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode()) ?
                containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), containerId, Constants.LONG_ZERO)
                : containerDetailRepository.findTop1ByBatchWorkDetailWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getWorkSheet().getId(), containerId, Constants.LONG_ZERO);
        if (null != containerDetail) {
            List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
            snWorkStatuses = ValidateUtils.isValid(snWorkDetailList) ? snWorkStatusRepository.findByLatestSnWorkDetailIdInAndStatusAndDeleted(snWorkDetailList.stream().map(SnWorkDetail::getId).collect(Collectors.toList()), snWorkStatus, Constants.LONG_ZERO) : null;
        } else if (wsCategory == WsEnum.ONLINE_RE_WS.getCategory()) {
            List<MaintainHistoryDTO> maintainHistoryList = rbaseMaintainHistoryDetailProxy.findByWsReworkReworkWorkSheetIdAndContainerDetailContainerIdAndSnWorkStatusIsNotNullAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), containerId, MaintainEnum.MAINTAIN_RESULT_REWORK.getStatus(),Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(maintainHistoryList)) {
                return;
            }
            snWorkStatuses = maintainHistoryList.stream().map(MaintainHistoryDTO::getSnWorkStatus).distinct().collect(Collectors.toList());
        }
        if (null == snWorkStatuses) {
            return;
        }
        if(rworkerContainerToDoStepGetDTO.getNumber()>Constants.INT_ZERO && (rworkerContainerToDoStepGetDTO.getCategory() ==  ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName()
                || rworkerContainerToDoStepGetDTO.getCategory() ==ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName()) && !CollectionUtils.isEmpty(rworkerStepProcessBaseDTO.getOnlineReworkUnQualifiedItemList())){
            List<Long> onlineReworkUnqualifiedItemIds = rworkerStepProcessBaseDTO.getOnlineReworkUnQualifiedItemList().stream().map(UnqualifiedItem::getId).toList();
            snWorkStatuses = snWorkStatuses.stream().filter(snWorkStatus1 -> Objects.nonNull(snWorkStatus1.getLatestUnqualifiedItem())&&onlineReworkUnqualifiedItemIds.contains(snWorkStatus1.getLatestUnqualifiedItem().getId()))
                    .toList();
        }
        if(CollectionUtils.isEmpty(snWorkStatuses)){
            return;
        }
        //若为返修单或者返工单则应该要过滤在返修单及返工单中产生不合格的SN
        if (rworkerStepProcessBaseDTO.getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            List<SnWorkStatus> unqualifiedSnWorkStatus = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode())
                    ? snWorkStatuses.stream().filter(snWorkStatusTemp -> Objects.nonNull(snWorkStatusTemp.getLatestReworkSnWorkDetail()) && snWorkStatusTemp.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(rworkerStepProcessBaseDTO.getSubWorkSheet().getId())).toList()
                    : snWorkStatuses.stream().filter(snWorkStatusTemp -> Objects.nonNull(snWorkStatusTemp.getLatestReworkSnWorkDetail()) && snWorkStatusTemp.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(rworkerStepProcessBaseDTO.getWorkSheet().getId())).toList();
            if (!CollectionUtils.isEmpty(unqualifiedSnWorkStatus)) {
                snWorkStatuses.removeAll(unqualifiedSnWorkStatus);
            }
        }
        List<SnGetInfo> snGetInfoList = snWorkStatuses.stream().map(SnGetInfo::new).toList();
        if (snGetInfoList.stream().anyMatch(snGetInfo -> Objects.isNull(snGetInfo.getYsn()))) {
            //获取内部SN对应的YSN
            List<PackageYsnRelationDTO> packageYsnRelationDTOList = rbaseRmpsProxy.findYsn(snGetInfoList.stream().map(SnGetInfo::getSn).toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(packageYsnRelationDTOList)) {
                snGetInfoList.forEach(snGetInfo -> packageYsnRelationDTOList.stream().filter(packageYsnRelationDTO -> packageYsnRelationDTO.getSn().equals(snGetInfo.getSn())).findFirst().ifPresent(packageYsnRelationDTO -> {
                    snGetInfo.setYsn(packageYsnRelationDTO.getYsn());
                }));
            }
        }
        rworkerContainerToDoStepGetDTO.setSnInfoList(snGetInfoList);
    }

    /**
     * 工单请求单支生产时验证SN是否合规(一般为第一个生产模式为单支的工序会进行验证)
     *
     * @param rworkerBindSnValidateRequestDTO 验证参数DTO
     */
    @Override
    public RworkerBindSnValidateGetDTO validate(RworkerBindSnValidateRequestDTO rworkerBindSnValidateRequestDTO) {
        Long productWorkSheetId = rworkerBindSnValidateRequestDTO.getProductWorkSheetId();
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(rworkerBindSnValidateRequestDTO.getSn(), Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(snWorkStatus)) {
            snWorkStatus = snWorkStatusRepository.findByYsnAndDeleted(rworkerBindSnValidateRequestDTO.getSn(), Constants.LONG_ZERO).orElse(null);
        }
        String transferSn = Objects.nonNull(snWorkStatus) ? snWorkStatus.getSn() : rworkerBindSnValidateRequestDTO.getSn();
        //内部SN对应的YSN
        String ysn = Objects.nonNull(snWorkStatus) ? snWorkStatus.getYsn() : null;
        Long stepId = rworkerBindSnValidateRequestDTO.getStepId();
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = null;
        WorkSheet workSheet;
        if (subWsProductionMode) {
            subWorkSheet = subWorkSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.subWorkSheetNotExist", "子工单不存在!"));
            workSheet = subWorkSheet.getWorkSheet();
        } else {
            workSheet = workSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.workSheetNotExist", "工单不存在!"));
        }

        boolean validatePackageSnAlready = FuncKeyUtil.checkApi(FuncKeyConstants.RMPS_FUNC_KEY);
        //如果SN生产状态查询不到则可能是用YSN请求工序的，那么需要获取RMPS的真实内部流转Sn
        if (Objects.isNull(snWorkStatus)) {
            WsRework wsRework = null;
            if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
                wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
            }
            BaseResultDTO baseResultDTO = rbaseRmpsProxy.findSn(transferSn, null != wsRework ? wsRework.getOriginalWorkSheet().getSerialNumber() : workSheet.getSerialNumber());
            if(Objects.isNull(baseResultDTO)){
                return null;
            }
            if(baseResultDTO.getStatus().equals(Constants.KO)){
                throw new ResponseException("error.illegalSn", baseResultDTO.getMessage());
            }
            String processSn =  String.valueOf(baseResultDTO.getData());
            if (StringUtils.isNotBlank(processSn) && !processSn.equals(transferSn)) {
                transferSn = processSn;
                ysn = rworkerBindSnValidateRequestDTO.getSn();
                snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(processSn, Constants.LONG_ZERO).orElse(null);
            }
        }
        //验证SN是否复用
        boolean isSnReUse = toDoStepStepValidateServices[0].validateSnReuse(subWsProductionMode, snWorkStatus, workSheet);
        //如果当前工单类型为返工单则需要验证SN是否在系统里存在以及SN状态是否一致
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory() && null != snWorkStatus && snWorkStatus.getStatus() != SnWorkStatusEnum.IN_THE_REPAIR.getStatus()) {
            throw new ResponseException("error.snIsNotRework", "SN不处于返修中状态!");
        }
        if(subWsProductionMode && !isSnReUse && null != snWorkStatus
                && workSheet.getCategory() != WsEnum.OFFLINE_RE_WS.getCategory()
                && (snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()||snWorkStatus.getStatus() ==SnWorkStatusEnum.SCRAP.getStatus() )  && !snWorkStatus.getSubWorkSheet().getId().equals(productWorkSheetId)){
            throw new ResponseException("error.snWorSheetNotMatched", "SN在子工单("+snWorkStatus.getSubWorkSheet().getSerialNumber()+")已完成生产!");
        }
        if(subWsProductionMode && !isSnReUse && null != snWorkStatus
                && workSheet.getCategory() != WsEnum.OFFLINE_RE_WS.getCategory()
                && (snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()||snWorkStatus.getStatus() ==SnWorkStatusEnum.SCRAP.getStatus() )  && !snWorkStatus.getSubWorkSheet().getId().equals(productWorkSheetId)){
            throw new ResponseException("error.snWorSheetNotMatched", "SN在子工单("+snWorkStatus.getSubWorkSheet().getSerialNumber()+")已完成生产!");
        }

        if (subWsProductionMode && !isSnReUse && null != snWorkStatus && workSheet.getCategory() != WsEnum.OFFLINE_RE_WS.getCategory()
                && !snWorkStatus.getSubWorkSheet().getId().equals(productWorkSheetId)) {
            throw new ResponseException("error.snWorSheetNotMatched", "SN正在子工单(" + snWorkStatus.getSubWorkSheet().getSerialNumber() + ")生产中!");
        }
        if (subWsProductionMode && null != snWorkStatus && workSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()
                && snWorkStatus.getStatus() == SnWorkStatusEnum.IN_THE_REPAIR.getStatus()
                && snWorkStatus.getSubWorkSheet().getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory()
                && (Objects.isNull(snWorkStatus.getLatestReworkSnWorkDetail()) || !snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(snWorkStatus.getSubWorkSheet().getId()))
                && !snWorkStatus.getSubWorkSheet().getId().equals(productWorkSheetId) && snWorkStatus.getSubWorkSheet().getWorkSheet().getStatus() != ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
            throw new ResponseException("error.snWorSheetNotMatched", "SN正在返修单(" + snWorkStatus.getSubWorkSheet().getSerialNumber() + ")返修中!");
        }
        if (!subWsProductionMode && null != snWorkStatus && workSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()
                && snWorkStatus.getStatus() == SnWorkStatusEnum.IN_THE_REPAIR.getStatus()
                && snWorkStatus.getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory()
                && (Objects.isNull(snWorkStatus.getLatestReworkSnWorkDetail()) || !snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(snWorkStatus.getWorkSheet().getId()))
                && !snWorkStatus.getWorkSheet().getId().equals(productWorkSheetId) && snWorkStatus.getWorkSheet().getStatus() != ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
            throw new ResponseException("error.snWorSheetNotMatched", "SN正在返修单(" + snWorkStatus.getWorkSheet().getSerialNumber() + ")返修中!");
        }
        if(!subWsProductionMode && !isSnReUse && null != snWorkStatus
                && workSheet.getCategory() != WsEnum.OFFLINE_RE_WS.getCategory()
                && (snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()||snWorkStatus.getStatus() ==SnWorkStatusEnum.SCRAP.getStatus() )  && !snWorkStatus.getWorkSheet().getId().equals(productWorkSheetId)){
            throw new ResponseException("error.snWorSheetNotMatched", "SN在工单("+snWorkStatus.getWorkSheet().getSerialNumber()+")已完成生产!");
        }
        if (!subWsProductionMode && !isSnReUse && null != snWorkStatus && workSheet.getCategory() != WsEnum.OFFLINE_RE_WS.getCategory() && !snWorkStatus.getWorkSheet().getId().equals(productWorkSheetId)) {
            throw new ResponseException("error.snWorSheetNotMatched", "SN正在工单(" + snWorkStatus.getWorkSheet().getSerialNumber() + ")生产中!");
        }
        if (subWsProductionMode && null != snWorkStatus && workSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory() && snWorkStatus.getStatus() == SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus()
                && snWorkStatus.getSubWorkSheet().getWorkSheet().getStatus() != ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
            throw new ResponseException("error.snIsProduction", "SN正在子工单(" + snWorkStatus.getSubWorkSheet().getSerialNumber() + ")投产中!");
        }

        if(!subWsProductionMode && !isSnReUse && null != snWorkStatus
                && workSheet.getCategory() != WsEnum.OFFLINE_RE_WS.getCategory()
                && (snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()||snWorkStatus.getStatus() ==SnWorkStatusEnum.SCRAP.getStatus() )  && !snWorkStatus.getWorkSheet().getId().equals(productWorkSheetId)){
            throw new ResponseException("error.snWorSheetNotMatched", "SN在工单("+snWorkStatus.getWorkSheet().getSerialNumber()+")已完成生产!");
        }

        if (!subWsProductionMode && null != snWorkStatus && workSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory() && snWorkStatus.getStatus() == SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus()
                && snWorkStatus.getWorkSheet().getStatus() != ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
            throw new ResponseException("error.snIsProduction", "SN正在工单(" + snWorkStatus.getSubWorkSheet().getSerialNumber() + ")投产中!");
        }
        //单支在线返工时不可以在其他离线单中进行投产
        if (subWsProductionMode && Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.IN_THE_REPAIR.getStatus()
                && null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet()
                && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(snWorkStatus.getSubWorkSheet().getId())
                && !snWorkStatus.getSubWorkSheet().getId().equals(productWorkSheetId)) {
            throw new ResponseException("error.snWorSheetNotMatched", "SN正在单支模式在线返工中，不可混合工单投产!");
        } else if (!subWsProductionMode && Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.IN_THE_REPAIR.getStatus()
                && null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet()
                && snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(snWorkStatus.getWorkSheet().getId())
                && !snWorkStatus.getWorkSheet().getId().equals(productWorkSheetId)) {
            throw new ResponseException("error.snWorSheetNotMatched", "SN正在单支模式在线返工中，不可混合工单投产!");
        }

        validSnWork(productWorkSheetId, transferSn, stepId, subWsProductionMode, snWorkStatus);
        //验证SN在RMPS里面是否属于当前工单
        BaseResultDTO baseDTO = BeanUtil.getHighestPrecedenceBean(SnProcessRequestServiceImpl.class).validatePackageProcessSn(transferSn, workSheet);
        if (baseDTO.getStatus().equals(Constants.KO)) {
            throw new ResponseException("error.snNotValidateInRmps", baseDTO.getMessage());
        }
        //判断是否存在有效的包装层级配置
        boolean existPackageLevelConfig = Objects.nonNull(baseDTO.getData()) && Boolean.parseBoolean(String.valueOf(baseDTO.getData()));
        if(validatePackageSnAlready && existPackageLevelConfig && !isSnReUse && Objects.isNull(snWorkStatus)  && workSheet.getCategory() == WsEnum.NORMAL_WS.getCategory()){
            RworkerPackageWorkSheetSnResultDTO rworkerPackageWorkSheetSnResultDTO = rbaseRmpsProxy.findWorkSheetSn(transferSn);
            if(Objects.isNull(rworkerPackageWorkSheetSnResultDTO) && subWsProductionMode){
                throw new ResponseException("error.snNotExistInSystem", "SN尚未进行子工单关联");
            }
            if(Objects.isNull(rworkerPackageWorkSheetSnResultDTO)){
                throw new ResponseException("error.snNotExistInSystem", "SN尚未进行工单关联");
            }
            if((subWsProductionMode)&&(!rworkerPackageWorkSheetSnResultDTO.getSerialNumber().equals(subWorkSheet.getSerialNumber())
                    && !rworkerPackageWorkSheetSnResultDTO.getSerialNumber().equals(workSheet.getSerialNumber()))){
                throw new ResponseException("error.snNotBelongCurrentSubWorkSheet", "SN不属于当前子工单");
            }
            if(!subWsProductionMode && !rworkerPackageWorkSheetSnResultDTO.getSerialNumber().equals(workSheet.getSerialNumber())){
                throw new ResponseException("error.snNotBelongCurrentWorkSheet", "SN不属于当前工单");
            }
        }

        //正常单验证SN是否属于绑定的(子)工单
        if (!validatePackageSnAlready && !isSnReUse && Objects.isNull(snWorkStatus) && workSheet.getCategory() == WsEnum.NORMAL_WS.getCategory()) {
            BeanUtil.getHighestPrecedenceBean(ISnProcessRequestService.class).validateWorkSheetBindSn(transferSn, workSheet, subWorkSheet, subWsProductionMode);
        }
        if (Objects.isNull(ysn)) {
            List<PackageYsnRelationDTO> packageYsnRelationDTOList = rbaseRmpsProxy.findYsn(Collections.singletonList(transferSn));
            if (!CollectionUtils.isEmpty(packageYsnRelationDTOList)) {
                ysn = packageYsnRelationDTOList.get(Constants.INT_ZERO).getYsn();
            }
        }
        return new RworkerBindSnValidateGetDTO().setSn(transferSn).setYsn(ysn);
    }

    /**
     * @param productWorkSheetId  投产工单id
     * @param sn                  sn
     * @param stepId              工序id
     * @param subWsProductionMode 投产粒度
     * @param snWorkStatus        单支生产状态
     */
    private void validSnWork(Long productWorkSheetId, String sn, Long stepId, boolean subWsProductionMode, SnWorkStatus snWorkStatus) {
        SnWorkDetail snWorkDetail = null;
        if (null != snWorkStatus) {
            if (subWsProductionMode) {
                snWorkDetail = snWorkDetailRepository.findBySnAndSubWorkSheetIdAndStepIdAndReworkTimeAndDeleted(sn, productWorkSheetId, stepId, snWorkStatus.getReworkTime(), Constants.LONG_ZERO).orElse(null);
            } else {
                snWorkDetail = snWorkDetailRepository.findBySnAndWorkSheetIdAndStepIdAndReworkTimeAndDeleted(sn, productWorkSheetId, stepId, snWorkStatus.getReworkTime(), Constants.LONG_ZERO).orElse(null);
            }
        }
        if (null != snWorkDetail) {
            throw new ResponseException("error.snStepIsFinished", "SN(" + sn + ")在当前工序已完成");
        }
    }

    /**
     * @param sn                  生产SN
     * @param workSheet           生产工单
     * @param subWorkSheet        生产子工单
     * @param subWsProductionMode 投产模式
     */
    @Override
    public void validateWorkSheetBindSn(String sn, WorkSheet workSheet, SubWorkSheet subWorkSheet, boolean subWsProductionMode) {
        if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            return;
        }
        WorkSheetSn workSheetSn = workSheetSnRepository.findBySnAndDeleted(sn, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(workSheetSn)) {
            throw new ResponseException("error.snNotExistInSystem", "SN尚未进行工单关联");
        }
        if (subWsProductionMode && !workSheetSn.getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
            throw new ResponseException("error.snNotBelongCurrentSubWorkSheet", "SN不属于当前子工单");
        }
        if (!subWsProductionMode && !workSheetSn.getWorkSheet().getId().equals(workSheet.getId())) {
            throw new ResponseException("error.snNotBelongCurrentWorkSheet", "SN不属于当前工单");
        }
    }

    /**
     * 获取SN请求模式下的待做工序信息
     *
     * @param rworkerSnTodoRequestDTO SN请求工序生产参数DTO
     * @return net.airuima.web.rest.rworker.process.dto.RworkerSnToDoStepGetDTO SN待投产工序生产信息DTO
     */
    @Override
    public RworkerSnToDoStepGetDTO snTodoStep(RworkerSnTodoRequestDTO rworkerSnTodoRequestDTO) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(rworkerSnTodoRequestDTO.getSn(), Constants.LONG_ZERO).orElse(null);
        //若通过SN查询不到SN生产状态则调用YSN查询生产状态
        if (Objects.isNull(snWorkStatus)) {
            snWorkStatus = snWorkStatusRepository.findByYsnAndDeleted(rworkerSnTodoRequestDTO.getSn(), Constants.LONG_ZERO).orElse(null);
        }
        RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO = new RworkerStepProcessBaseDTO();
        //内部SN对应的YSN
        String ysn = Objects.nonNull(snWorkStatus) ? snWorkStatus.getYsn() : null;
        //生产流转SN
        String transferSn = Objects.nonNull(snWorkStatus) ? snWorkStatus.getSn() : rworkerSnTodoRequestDTO.getSn();
        //RMPS中SN关联的(子)工单信息
        RworkerPackageWorkSheetSnResultDTO rworkerPackageWorkSheetSnResultDTO = null;
        //工单SN关联对象
        WorkSheetSn workSheetSn = null;
        if(Objects.isNull(snWorkStatus)){
            rworkerPackageWorkSheetSnResultDTO = rbaseRmpsProxy.findWorkSheetSn(transferSn);
        }
        if(Objects.isNull(snWorkStatus) && Objects.isNull(rworkerPackageWorkSheetSnResultDTO)){
            workSheetSn = workSheetSnRepository.findBySnAndDeleted(transferSn, Constants.LONG_ZERO).orElse(null);
        }
        //如果为空可能是YSN，则需要获取RMPS的流转SN
        if(Objects.isNull(snWorkStatus) && Objects.isNull(workSheetSn) && Objects.isNull(rworkerPackageWorkSheetSnResultDTO)){
            BaseResultDTO baseResultDTO = rbaseRmpsProxy.findSn(rworkerSnTodoRequestDTO.getSn(), null);
            if(Objects.isNull(baseResultDTO)){
                throw new ResponseException("error.snIsNotExist", "SN在系统中不存在!");
            }
            if(baseResultDTO.getStatus().equals(Constants.KO)){
                throw new ResponseException("error.illegalSn", baseResultDTO.getMessage());
            }
            String sn =  String.valueOf(baseResultDTO.getData());
            if (StringUtils.isNotBlank(sn)) {
                ysn = rworkerSnTodoRequestDTO.getSn();
                transferSn = sn;
                snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(sn, Constants.LONG_ZERO).orElse(null);
            }
        }

        if (Objects.isNull(ysn)) {
            List<PackageYsnRelationDTO> packageYsnRelationDTOList = rbaseRmpsProxy.findYsn(Collections.singletonList(transferSn));
            if (!CollectionUtils.isEmpty(packageYsnRelationDTOList)) {
                ysn = packageYsnRelationDTOList.get(Constants.INT_ZERO).getYsn();
            }
        }
        if (null == snWorkStatus && Objects.isNull(workSheetSn) && Objects.isNull(rworkerPackageWorkSheetSnResultDTO)) {
            throw new ResponseException("error.snIsNotExist", "SN在系统中不存在!");
        }
        if (Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.SCRAP.getStatus()) {
            throw new ResponseException("error.snIsScraped", "SN已报废，不可继续投产!");
        }
        if (Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()) {
            throw new ResponseException("error.snIsFinished", "SN已完成，不可继续投产!");
        }
        if (Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.MAINTAIN.getStatus()) {
            throw new ResponseException("error.snIsWaitMaintain", "SN待维修分析，不可继续投产!");
        }
        if (Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.STEP_REINSPECT.getStatus()) {
            throw new ResponseException("error.snIsWaitReinspect", "SN待复检，不可继续投产!");
        }
        rworkerSnTodoRequestDTO.setSn(transferSn);
        //验证请求的子工单是否存在缓存，如果有缓存则需要提示
        rworkerCacheServices[0].validateCacheWhenRequestTodoStep(Constants.INT_THREE, transferSn);
        //获取工位可生产的工序列表
        List<Step> workCellStepList = workCellStepRepository.findByWorkCellId(rworkerSnTodoRequestDTO.getWorkCellId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellStepList)) {
            throw new ResponseException("error.WorkCellNotBindStep", "工位未绑定任何工序");
        }
        StaffDTO staffDTO = rbaseStaffProxy.findByIdAndDeleted(rworkerSnTodoRequestDTO.getStaffId(), Constants.LONG_ZERO);
        WorkCell workCell = workCellRepository.getReferenceById(rworkerSnTodoRequestDTO.getWorkCellId());
        RworkerSnToDoStepGetDTO rworkerSnToDoStepGetDTO = new RworkerSnToDoStepGetDTO();
        //若SN处于投产中状态则认为SN正在正常单中投产
        if (Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus()) {
            rworkerSnToDoStepGetDTO = this.findTodoStepWhenProductionStatus(rworkerStepProcessBaseDTO, workCellStepList, snWorkStatus, subWsProductionMode);
        }
        //若SN处于返修中状态则认为SN在返工单、返修单中返修或者单支在线直接返修
        if (Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.IN_THE_REPAIR.getStatus()) {
            rworkerSnToDoStepGetDTO = this.findTodoStepWhenReWorkStatus(rworkerStepProcessBaseDTO, workCellStepList, snWorkStatus, subWsProductionMode);
        }
        //如果SN生产状态为空且工单关联SN不为空则认为SN在做第一个工序
        if (Objects.isNull(snWorkStatus)) {
            rworkerSnToDoStepGetDTO = this.findTodoStepWhenNormalWsFirstStep(rworkerStepProcessBaseDTO, rworkerPackageWorkSheetSnResultDTO,workSheetSn, workCellStepList, subWsProductionMode);
        }
        rworkerStepProcessBaseDTO.setStaffDTO(staffDTO).setWorkCell(workCell);
        //验证外协工序不能进行投产
        oemServices[0].validProcessOemStep(new WsStep().setCategory(rworkerSnToDoStepGetDTO.getCategory()));
        //验证工序时间间隔是否合规
        BaseResultDTO baseResultDTO = toDoStepStepValidateServices[0].validateSnStepInterval(rworkerStepProcessBaseDTO, rworkerSnTodoRequestDTO.getSn(), subWsProductionMode);
        if (Objects.nonNull(baseResultDTO) && baseResultDTO.getStatus().equals(Constants.KO)) {
            throw new ResponseException(baseResultDTO.getKey(), baseResultDTO.getMessage());
        }
        //当前SN是否为单支在线返工
        boolean singleSnOnlineRepair = Boolean.FALSE;
        if (subWsProductionMode && null != snWorkStatus && snWorkStatus.getReworkTime() > Constants.INT_ZERO && null != snWorkStatus.getLatestReworkSnWorkDetail() && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(rworkerStepProcessBaseDTO.getSubWorkSheet().getId())) {
            singleSnOnlineRepair = Boolean.TRUE;
        } else if (!subWsProductionMode && null != snWorkStatus && snWorkStatus.getReworkTime() > Constants.INT_ZERO && null != snWorkStatus.getLatestReworkSnWorkDetail() && snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(rworkerStepProcessBaseDTO.getWorkSheet().getId())) {
            singleSnOnlineRepair = Boolean.TRUE;
        }
        //验证工单状态
        if (null != rworkerStepProcessBaseDTO.getSubWorkSheet() && !singleSnOnlineRepair) {
            batchProcessRequestServices[0].validateSubWorkSheetStatus(rworkerStepProcessBaseDTO.getSubWorkSheet());
        } else if(!singleSnOnlineRepair){
            batchProcessRequestServices[0].validateWorkSheetStatus(rworkerStepProcessBaseDTO.getWorkSheet());
        }
        //验证指定工单工序工位是否合规
        toDoStepStepValidateServices[0].validWsStepWorkCell(rworkerStepProcessBaseDTO);
        //验证员工技能是否匹配当前待做工序
        toDoStepStepValidateServices[0].validateStaffSkill(rworkerStepProcessBaseDTO);
        //验证工位GRR是否合规
        toDoStepStepValidateServices[0].validateWorkCellGrr(rworkerStepProcessBaseDTO);
        //验证环境-温湿度是否合规
        BaseWaringDTO baseWaringDTO = environmentServices[0].validateHumiture(rworkerStepProcessBaseDTO);
        if (null != baseWaringDTO) {
            rworkerSnToDoStepGetDTO.setKey(baseWaringDTO.getKey()).setMessage(baseWaringDTO.getMessage());
        }
        //验证环境-洁净度是否合规
        environmentServices[0].validateCleanliness(rworkerStepProcessBaseDTO);
        //获取工位工序设备信息
        List<FacilityGetInfo> facilityGetInfoList = facilityServices[0].findStepFacilityInfo(rworkerSnTodoRequestDTO.getWorkCellId(), rworkerStepProcessBaseDTO.getStep().getId());
        rworkerSnToDoStepGetDTO.setFacilityInfoList(facilityGetInfoList);
        rworkerStepProcessBaseDTO.setFacilityGetInfoList(facilityGetInfoList);
        List<Long> facilityIdList = CollectionUtils.isEmpty(facilityGetInfoList) ? null : facilityGetInfoList.stream().map(FacilityGetInfo::getId).collect(Collectors.toList());
        //验证请求的待做工序的基础信息是否存在未处理的事件
        eventServices[0].validateRequestStepExistUnProcessedEvent(rworkerStepProcessBaseDTO);
        //验证工位设备校准
        facilityCalibrateServices[0].validateFacilityCalibrate(rworkerSnTodoRequestDTO.getWorkCellId(), facilityIdList);
        //验证设备基础状态
        facilityInspectionServices[0].validateFacilityBaseStatus(facilityIdList);
        //验证设备点检等是否合规
        facilityInspectionServices[0].validateFacilityPointInspection(facilityIdList);
        //验证设备巡检等是否合规
        facilityInspectionServices[0].validateFacilityPatrolInspection(facilityIdList);
        //验证设备是否存在逾期维保任务
        facilityMaintainServices[0].validateFacilityMaintain(facilityIdList);
        //获取工序不良信息
        rworkerSnToDoStepGetDTO.setUnqualifiedItemInfoList(qualityServices[0].findStepUnqualifiedItemInfo(rworkerStepProcessBaseDTO.getStep().getId(), rworkerStepProcessBaseDTO.getWorkSheet().getPedigree(), rworkerStepProcessBaseDTO.getWorkSheet().getWorkFlow().getId(), rworkerStepProcessBaseDTO.getWorkSheet().getClientId()));
        //获取BOM物料清单列表
        rworkerSnToDoStepGetDTO.setBomMaterialInfoList(materialServices[0].findBomMaterialInfo(rworkerStepProcessBaseDTO.getWorkSheet().getId()));
        // 设置动态数据
        rworkerSnToDoStepGetDTO.setStepDynamicDataGetDto(dynamicServices[0].getStepDynamicDataInfo(rworkerStepProcessBaseDTO.getWorkSheet().getPedigree(), rworkerStepProcessBaseDTO.getWorkFlow(), rworkerStepProcessBaseDTO.getStep()));
        rworkerSnToDoStepGetDTO.setStepDynamicDataGetVisibleDtoList(dynamicServices[0].getSnStepDynamicDataVisibleInfo(rworkerStepProcessBaseDTO.getSubWorkSheet(), rworkerStepProcessBaseDTO.getWorkSheet(), rworkerStepProcessBaseDTO.getStep(), rworkerSnTodoRequestDTO.getSn()));
        //工序生产过程物料库存管控级别(0:不管控物料库存;1:工单物料库存;2:工位物料库存)
        int materialControlLevel = commonService.getMaterialControlLevel();
        rworkerSnToDoStepGetDTO.setIsFeedingMaterial(rworkerStepProcessBaseDTO.getWsStep().getIsControlMaterial()).setMaterialControlLevel(materialControlLevel);
        //获取工序技术指标及SOP
        PedigreeStepSpecification pedigreeStepSpecification = toDoStepConfigServices[0].findStepSpecificationSop(rworkerStepProcessBaseDTO.getWorkSheet().getPedigree(), rworkerStepProcessBaseDTO.getWorkFlow(), rworkerStepProcessBaseDTO.getStep(), rworkerStepProcessBaseDTO.getWorkSheet().getClientId());
        if (null != pedigreeStepSpecification) {
            //设置技术指标
            rworkerSnToDoStepGetDTO.setSpecification(pedigreeStepSpecification.getQualification());
            //设置工序SOP信息
            rworkerSnToDoStepGetDTO.setStepSopInfoList(toDoStepConfigServices[0].getStepSpecificationSop(pedigreeStepSpecification));
        }
        //获取BOM里定义的工艺图列表
        List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(rworkerStepProcessBaseDTO.getWorkSheet().getBomInfoId());
        if(!CollectionUtils.isEmpty(documentDTOList)){
            rworkerSnToDoStepGetDTO.setProductsCraftInfoList(documentDTOList.stream().map(ProcessDocumentGetInfo::new).collect(Collectors.toList()));
        }
        rworkerSnToDoStepGetDTO.setSn(transferSn);
        rworkerSnToDoStepGetDTO.setYsn(ysn);
        rworkerSnToDoStepGetDTO.setWorkFlowId(rworkerStepProcessBaseDTO.getWorkFlow().getId());
        //获取当前待做工序的易损件信息
        rworkerSnToDoStepGetDTO.setWearingPartGroupInfoList(wearingPartServices[0].getWearingPartInfo(rworkerStepProcessBaseDTO));
        //获取SN烘烤温循工序相关配置及验证
        bakeCycleBakeAgeingModelServices[0].bakeCycleBakeAgeingSnInfo(rworkerSnTodoRequestDTO.getSn(), Objects.isNull(snWorkStatus) ? Constants.INT_ZERO : snWorkStatus.getReworkTime(), rworkerSnToDoStepGetDTO, rworkerStepProcessBaseDTO);
        //请求工序时更新设备状态为正常运行
        facilityServices[0].updateFacilityStatus(facilityIdList, ConstantsEnum.FACILITY_STATUS_RUNNING.getCategoryName());
        //判断当前投产工序是否存在转工艺或者为在线返工
        SubWorkSheet subWorkSheet = rworkerStepProcessBaseDTO.getSubWorkSheet();
        WorkSheet workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
        String onlineMaintainMaterialControl = null;
        if (subWsProductionMode && null != snWorkStatus && snWorkStatus.getReworkTime() > net.airuima.constant.Constants.INT_ZERO && null != snWorkStatus.getLatestReworkSnWorkDetail() && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
            rworkerSnToDoStepGetDTO.setOnlineMaintainOrTransferWorkFlow(Boolean.TRUE);
            onlineMaintainMaterialControl =rbaseSysCodeProxy.findByCode(Constants.KEY_ONLINE_MAINTAIN_MATERIAL_CONTROL);
        } else if (!subWsProductionMode && null != snWorkStatus && snWorkStatus.getReworkTime() > net.airuima.constant.Constants.INT_ZERO && null != snWorkStatus.getLatestReworkSnWorkDetail() && snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(workSheet.getId())) {
            rworkerSnToDoStepGetDTO.setOnlineMaintainOrTransferWorkFlow(Boolean.TRUE);
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_ONLINE_MAINTAIN_MATERIAL_CONTROL);
        }else if(workSheet.getCategory() == ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()){
            rworkerSnToDoStepGetDTO.setOnlineMaintainOrTransferWorkFlow(Boolean.TRUE);
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_ONLINE_MAINTAIN_MATERIAL_CONTROL);
        }else if(workSheet.getCategory() == ConstantsEnum.WORK_SHEET_OFFLINE_CATEGORY.getCategoryName()){
            rworkerSnToDoStepGetDTO.setOnlineMaintainOrTransferWorkFlow(Boolean.TRUE);
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_OFFLINE_MAINTAIN_MATERIAL_CONTROL);
        }else if(null != snWorkStatus && !snWorkStatus.getWorkFlow().getId().equals(workSheet.getWorkFlow().getId())){
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_TRANSFER_MAINTAIN_MATERIAL_CONTROL);
        }
        rworkerSnToDoStepGetDTO.setOnlineMaintainMaterialControl(StringUtils.isNotBlank(onlineMaintainMaterialControl) ? Integer.parseInt(onlineMaintainMaterialControl): net.airuima.constant.Constants.INT_ZERO);
        return rworkerSnToDoStepGetDTO;
    }

    /**
     * SN生产状态为投产中时获取下个待做工序生产信息,只有正常单的SN才会处于投产中
     *
     * @param workCellStepList    工位绑定的工序列表
     * @param snWorkStatus        SN生产状态
     * @param subWsProductionMode 工单投产粒度
     * @return net.airuima.web.rest.rworker.process.dto.RworkerStepProcessBaseDTO 通用参数DTO
     */
    private RworkerSnToDoStepGetDTO findTodoStepWhenProductionStatus(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, List<Step> workCellStepList, SnWorkStatus snWorkStatus, boolean subWsProductionMode) {
        SubWorkSheet subWorkSheet = subWsProductionMode ? snWorkStatus.getSubWorkSheet() : null;
        WorkSheet workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : snWorkStatus.getWorkSheet();
        if (null != subWorkSheet && (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())) {
            throw new ResponseException("error.subWorkSheetFinished", "子工单已完成!");
        }
        if (null != workSheet && (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())) {
            throw new ResponseException("error.workSheetFinished", "工单已完成!");
        }
        if (null != subWorkSheet && (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName())) {
            throw new ResponseException("error.subWorkSheetPaused", "子工单已暂停投产!");
        }
        if (null != workSheet && (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName())) {
            throw new ResponseException("error.workSheetPaused", "工单已暂停投产!");
        }
        List<CheckHistoryDetail> checkHistoryDetails;
        if (subWsProductionMode) {
            checkHistoryDetails = checkHistoryDetailRepository.findByCheckHistorySubWorkSheetIdAndCheckHistoryStatusAndCheckHistoryWorkSheetIsNullAndSnAndVirtualAndDeleted(subWorkSheet.getId(), Boolean.FALSE, snWorkStatus.getSn(), Boolean.FALSE, Constants.LONG_ZERO);
        } else {
            checkHistoryDetails = checkHistoryDetailRepository.findByCheckHistoryWorkSheetIdAndCheckHistoryStatusAndCheckHistorySubWorkSheetIsNullAndSnAndVirtualAndDeleted(workSheet.getId(), Boolean.FALSE, snWorkStatus.getSn(), Boolean.FALSE, Constants.LONG_ZERO);
        }
        if (!CollectionUtils.isEmpty(checkHistoryDetails)) {
            if (checkHistoryDetails.stream().anyMatch(checkHistoryDetail -> checkHistoryDetail.getCheckHistory().getCategory() == Constants.INSPECT_PQC_CATEGORY)) {
                throw new ResponseException("error.SnPqcNotDeal", "SN抽检不合格未处理!");
            }
            if (checkHistoryDetails.stream().anyMatch(checkHistoryDetail -> checkHistoryDetail.getCheckHistory().getCategory() == Constants.INSPECT_FQC_CATEGORY)) {
                throw new ResponseException("error.SnFqcNotDeal", "SN终检不合格未处理!");
            }
        }
        List<WsStep> wsStepList = null;
        if (null != subWorkSheet) {
            wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        }
        if (null != workSheet && CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        if (CollectionUtils.isEmpty(wsStepList)) {
            throw new ResponseException("error.wsStepNotExist", "工单工艺快照不存在");
        }
        //返工单时需要进行额外判断，因为SN在返工单开始生产时的第一个工序为返工单工艺快照的第一个工序，而不能直接通过最新的SN工作详情判断下个工序
        List<WsStep> firstReworkWsStepList = null;
        WsStep nextTodoWsStep = null;
        assert workSheet != null;
        if (subWsProductionMode) {
            if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory() && !snWorkStatus.getLatestSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
                firstReworkWsStepList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).collect(Collectors.toList());
            }
        } else if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory() && !snWorkStatus.getLatestSnWorkDetail().getWorkSheet().getId().equals(workSheet.getId())) {
            firstReworkWsStepList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(firstReworkWsStepList)) {
            nextTodoWsStep = firstReworkWsStepList.stream().filter(wsStep -> workCellStepList.stream().map(Step::getId).toList().contains(wsStep.getStep().getId())).findFirst().orElse(null);
            if(Objects.isNull(nextTodoWsStep)) {
                throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + firstReworkWsStepList.get(net.airuima.constant.Constants.INT_ZERO).getStep().getName() + ",当前工位不可生产该工序");
            }
        } else {
            //非返工单则按照SN最新完成SN工作详情以及结合工单工艺快照来判断下个待做工序信息
            WsStep latestWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(snWorkStatus.getLatestSnWorkDetail().getStep().getId())).findFirst().orElseThrow(() -> new ResponseException("error.latestWsStepNotExist", "最新完成的工序在工单工艺快照中不存在"));
            WsStep nextFirstWsStep = wsStepList.stream().filter(wsStep -> latestWsStep.getAfterStepId().contains(wsStep.getStep().getId().toString())).findFirst().get();
            Step nextTodoStep = workCellStepList.stream().filter(step -> latestWsStep.getAfterStepId().contains(step.getId().toString())).findFirst().orElse(null);
            if(Objects.isNull(nextTodoStep)){
                throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + nextFirstWsStep.getStep().getName() + ",当前工位不可生产该工序");
            }
            nextTodoWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(nextTodoStep.getId())).findFirst().orElse(null);
            if(Objects.isNull(nextTodoWsStep)){
                throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + nextFirstWsStep.getStep().getName() + ",当前工位不可生产该工序");
            }
        }
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.workSheetRequestModeError", "请扫描工单进行请求待做工序");
        }
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.containerRequestModeError", "请扫描容器进行请求待做工序");
        }
        RworkerSnToDoStepGetDTO rworkerSnToDoStepGetDTO = new RworkerSnToDoStepGetDTO(nextTodoWsStep);
        rworkerSnToDoStepGetDTO.setWsStepInfoList(wsStepList.stream().map(WsStepGetInfo::new).collect(Collectors.toList())).setWorkSheetInfo(subWsProductionMode ? new RworkerToDoWsGetDTO(subWorkSheet) : new RworkerToDoWsGetDTO(workSheet));
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null != nextTodoWsStep.getWorkFlow() ? nextTodoWsStep.getWorkFlow() : workSheet.getWorkFlow();
        rworkerStepProcessBaseDTO.setWsStepList(wsStepList).setStep(nextTodoWsStep.getStep()).setWsStep(nextTodoWsStep).setSubWorkSheet(subWorkSheet).setWorkSheet(workSheet).setSubWsProductionMode(subWsProductionMode).setWorkFlow(snapshotWorkFlow);
        return rworkerSnToDoStepGetDTO;
    }

    /**
     * SN生产状态为返修中时获取下个待做工序生产信息
     *
     * @param workCellStepList    工位绑定的工序列表
     * @param snWorkStatus        SN生产状态
     * @param subWsProductionMode 工单投产粒度
     * @return net.airuima.web.rest.rworker.process.dto.RworkerStepProcessBaseDTO 通用参数DTO
     */
    private RworkerSnToDoStepGetDTO findTodoStepWhenReWorkStatus(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, List<Step> workCellStepList, SnWorkStatus snWorkStatus, boolean subWsProductionMode) {
        SubWorkSheet subWorkSheet = subWsProductionMode ? snWorkStatus.getSubWorkSheet() : null;
        WorkSheet workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : snWorkStatus.getWorkSheet();
        //如果SN生产状态里的工单为非正常单时则按照返工单/返修单模式进行获取下个待做工序
        if (workSheet.getCategory() != WsEnum.NORMAL_WS.getCategory()) {
            if (Objects.isNull(snWorkStatus.getLatestReworkSnWorkDetail()) || (subWsProductionMode && !snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId()))
                    || (!subWsProductionMode && !snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(workSheet.getId())))
                return findTodoStepWhenProductionStatus(rworkerStepProcessBaseDTO, workCellStepList, snWorkStatus, subWsProductionMode);
        }
        List<WorkFlowStep> workFlowStepList = null;
        //如果SN生产状态的工单为正常单时则按照单支直接在线反工模式进行获取下个待做工序
        if (subWsProductionMode) {
            if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && snWorkStatus.getLatestSnWorkDetail().getSubWorkSheet().getId().equals(snWorkStatus.getSubWorkSheet().getId())) {
                workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(snWorkStatus.getWorkFlow().getId(), Constants.LONG_ZERO);
            }
        } else if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && snWorkStatus.getLatestSnWorkDetail().getWorkSheet().getId().equals(snWorkStatus.getWorkSheet().getId())) {
            workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(snWorkStatus.getWorkFlow().getId(), Constants.LONG_ZERO);
        }
        if (CollectionUtils.isEmpty(workFlowStepList)) {
            throw new ResponseException(NOT_EXIST_TODO_STEP, ERR_MSG);
        }
        rworkerStepProcessBaseDTO.setWorkSheet(workSheet).setSubWorkSheet(subWorkSheet).setSubWsProductionMode(subWsProductionMode);
        WsStep wsStep = new WsStep();
        //如果最新的SN工作详情返修次数标志和SN生产状态的不通，则返修工艺路线的第一个工序就是当前待做工序
        if (snWorkStatus.getLatestSnWorkDetail().getReworkTime() != snWorkStatus.getReworkTime()) {
            List<WorkFlowStep> firstWorkFlowStepList = workFlowStepList.stream().filter(workFlowStep -> StringUtils.isBlank(workFlowStep.getPreStepId())).toList();
            WorkFlowStep nextTodoWorkFlowStep = firstWorkFlowStepList.stream().filter(workFlowStep -> workCellStepList.stream().map(Step::getId).toList().contains(workFlowStep.getStep().getId())).findFirst().orElse(null);
            if (null == nextTodoWorkFlowStep) {
                throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + firstWorkFlowStepList.get(net.airuima.constant.Constants.INT_ZERO).getStep().getName() + ",当前工位不可生产该工序");
            }
            rworkerStepProcessBaseDTO.setStep(nextTodoWorkFlowStep.getStep());
            wsStep.setPreStepId(nextTodoWorkFlowStep.getPreStepId()).setAfterStepId(nextTodoWorkFlowStep.getAfterStepId());
        } else {
            WorkFlowStep latestWorkFlowStep = workFlowStepList.stream().filter(workFlowStep -> workFlowStep.getStep().getId().equals(snWorkStatus.getLatestSnWorkDetail().getStep().getId())).findFirst().orElse(null);
            if (null == latestWorkFlowStep || StringUtils.isBlank(latestWorkFlowStep.getAfterStepId())) {
                throw new ResponseException(NOT_EXIST_TODO_STEP, ERR_MSG);
            }
            List<WorkFlowStep> nextTodoWorkFlowStepList = workFlowStepList.stream().filter(workFlowStep -> latestWorkFlowStep.getAfterStepId().contains(workFlowStep.getStep().getId().toString())).toList();
            if (CollectionUtils.isEmpty(nextTodoWorkFlowStepList)) {
                throw new ResponseException(NOT_EXIST_TODO_STEP, ERR_MSG);
            }
            WorkFlowStep nextTodoWorkFlowStep = nextTodoWorkFlowStepList.stream().filter(workFlowStep -> workCellStepList.stream().map(Step::getId).toList().contains(workFlowStep.getStep().getId())).findFirst().orElse(null);
            if (null == nextTodoWorkFlowStep) {
                throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + nextTodoWorkFlowStepList.get(net.airuima.constant.Constants.INT_ZERO).getStep().getName() + ",当前工位不可生产该工序");
            }
            rworkerStepProcessBaseDTO.setStep(nextTodoWorkFlowStep.getStep());
            wsStep.setPreStepId(nextTodoWorkFlowStep.getPreStepId()).setAfterStepId(nextTodoWorkFlowStep.getAfterStepId());
        }
        StepDTO stepDTO = commonService.findPedigreeStepConfig(workSheet.getClientId(), workSheet.getPedigree(), snWorkStatus.getWorkFlow(), rworkerStepProcessBaseDTO.getStep());
        if (null == stepDTO) {
            throw new ResponseException("error.stepConfigNotExist", "工序(" + rworkerStepProcessBaseDTO.getStep().getCode() + ")请求模式配置不存在");
        }
        if (stepDTO.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.workSheetRequestModeError", "请扫描工单进行请求待做工序");
        }
        if (stepDTO.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.containerRequestModeError", "请扫描容器进行请求待做工序");
        }
        wsStep.setStep(rworkerStepProcessBaseDTO.getStep())
                .setWorkSheet(workSheet)
                .setSubWorkSheet(subWorkSheet)
                .setRequestMode(stepDTO.getRequestMode())
                .setCategory(stepDTO.getCategory())
                .setIsControlMaterial(stepDTO.getIsControlMaterial())
                .setIsBindContainer(stepDTO.getIsBindContainer())
                .setControlMode(stepDTO.getControlMode())
                .setInputRate(Constants.INT_ONE);
        rworkerStepProcessBaseDTO.setWsStep(wsStep).setWorkFlow(snWorkStatus.getWorkFlow());
        List<WsStepGetInfo> wsStepInfoList = Lists.newArrayList();
        workFlowStepList.forEach(workFlowStep -> {
            StepDTO stepConfigDTO = commonService.findPedigreeStepConfig(workSheet.getClientId(), workSheet.getPedigree(), snWorkStatus.getWorkFlow(), workFlowStep.getStep());
            if (null == stepConfigDTO) {
                throw new ResponseException("error.stepConfigNotExist", "工序(" + workFlowStep.getStep().getCode() + ")请求模式配置不存在");
            }
            wsStepInfoList.add(new WsStepGetInfo(workFlowStep, stepConfigDTO));
        });
        RworkerSnToDoStepGetDTO rworkerSnToDoStepGetDTO = new RworkerSnToDoStepGetDTO(wsStep);
        rworkerSnToDoStepGetDTO.setWsStepInfoList(wsStepInfoList).setWorkSheetInfo(subWsProductionMode ? new RworkerToDoWsGetDTO(subWorkSheet) : new RworkerToDoWsGetDTO(workSheet));
        //如果是单支在线返那么获取对应工艺路线的工序组装快照
        if(!CollectionUtils.isEmpty(wsStepInfoList)){
            List<WsStep> wsStepList = new ArrayList<>();
            List<Step> stepList = stepRepository.findByIdInAndDeleted(wsStepInfoList.stream().map(WsStepGetInfo::getId).toList(),Constants.LONG_ZERO);
            Map<Long, Step> stepGroup = stepList.stream().collect(Collectors.groupingBy(AuditSfIdEntity::getId, Collectors.collectingAndThen(
                    Collectors.toList(), list -> list.get(0))));
            wsStepInfoList.forEach(wsStepGetInfo -> {
                WsStep wsStep1 = new WsStep();
                wsStep1.setAfterStepId(wsStepGetInfo.getAfterStepId())
                        .setCategory(wsStepGetInfo.getCategory())
                        .setControlMode(wsStepGetInfo.getControlMode())
                        .setInputRate(wsStepGetInfo.getInputRate()).setIsBindContainer(wsStepGetInfo.getBindContainer())
                        .setIsControlMaterial(wsStepGetInfo.getControlMaterial()).setPreStepId(wsStepGetInfo.getPreStepId())
                        .setRequestMode(wsStepGetInfo.getRequestMode())
                        .setStep(stepGroup.get(wsStepGetInfo.getId()))
                        .setSubWorkSheet(subWorkSheet)
                        .setWorkSheet(workSheet)
                        .setWorkFlow(snWorkStatus.getWorkFlow())
                        .setInputRate(wsStepGetInfo.getInputRate());
                wsStepList.add(wsStep1);
            });
            rworkerStepProcessBaseDTO.setWsStepList(wsStepList);
        }
        return rworkerSnToDoStepGetDTO;
    }

    /**
     * @param rworkerStepProcessBaseDTO 工序请求缓存区的基础信息，避免反复查询
     * @param workSheetSn               工单SN关联对象
     * @param workCellStepList          工位工序列表
     * @param subWsProductionMode       投产粒度
     * @return RworkerSnToDoStepGetDTO SN待投产信息
     */
    private RworkerSnToDoStepGetDTO findTodoStepWhenNormalWsFirstStep(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, RworkerPackageWorkSheetSnResultDTO rworkerPackageWorkSheetSnResultDTO,
                                                                      WorkSheetSn workSheetSn, List<Step> workCellStepList, boolean subWsProductionMode) {
        SubWorkSheet subWorkSheet ;
        WorkSheet workSheet ;
        if(Objects.nonNull(rworkerPackageWorkSheetSnResultDTO)){
            subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findBySerialNumberAndDeleted(rworkerPackageWorkSheetSnResultDTO.getSerialNumber(),Constants.LONG_ZERO).orElse(null):null;
            if(subWsProductionMode && Objects.isNull(subWorkSheet)){
                throw new ResponseException("error.subWorkSheetNotExist", "子工单("+rworkerPackageWorkSheetSnResultDTO.getSerialNumber()+")不存在!");
            }
            workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : workSheetRepository.findBySerialNumberAndDeleted(rworkerPackageWorkSheetSnResultDTO.getSerialNumber(), Constants.LONG_ZERO).orElse(null);
            if(!subWsProductionMode && Objects.isNull(workSheet)){
                throw new ResponseException("error.workSheetNotExist", "工单("+rworkerPackageWorkSheetSnResultDTO.getSerialNumber()+")不存在!");
            }
        }else {
            subWorkSheet = subWsProductionMode ? workSheetSn.getSubWorkSheet() : null;
            workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : workSheetSn.getWorkSheet();
        }
        if (null != subWorkSheet && (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())) {
            throw new ResponseException("error.subWorkSheetFinished", "子工单已完成!");
        }
        if (null != workSheet && (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())) {
            throw new ResponseException("error.workSheetFinished", "工单已完成!");
        }
        if (null != subWorkSheet && (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName())) {
            throw new ResponseException("error.subWorkSheetPaused", "子工单已暂停投产!");
        }
        if (null != workSheet && (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName())) {
            throw new ResponseException("error.workSheetPaused", "工单已暂停投产!");
        }
        List<WsStep> wsStepList = null;
        if (null != subWorkSheet) {
            wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        }
        if (null != workSheet && CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        if (CollectionUtils.isEmpty(wsStepList)) {
            throw new ResponseException("error.wsStepNotExist", "工单工艺快照不存在");
        }
        List<WsStep> firstOneWsStepList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).toList();
        WsStep nextTodoWsStep = firstOneWsStepList.stream().filter(wsStep -> workCellStepList.stream().map(Step::getId).toList().contains(wsStep.getStep().getId())).findFirst().orElse(null);
        if(Objects.isNull(nextTodoWsStep)){
            throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + firstOneWsStepList.get(Constants.INT_ZERO).getStep().getName() + ",当前工位不可生产该工序");
        }
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.workSheetRequestModeError", "请扫描工单进行请求待做工序");
        }
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.containerRequestModeError", "请扫描容器进行请求待做工序");
        }
        RworkerSnToDoStepGetDTO rworkerSnToDoStepGetDTO = new RworkerSnToDoStepGetDTO(nextTodoWsStep);
        rworkerSnToDoStepGetDTO.setWsStepInfoList(wsStepList.stream().map(WsStepGetInfo::new).collect(Collectors.toList())).setWorkSheetInfo(subWsProductionMode ? new RworkerToDoWsGetDTO(subWorkSheet) : new RworkerToDoWsGetDTO(workSheet));
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null != nextTodoWsStep.getWorkFlow() ? nextTodoWsStep.getWorkFlow() : workSheet.getWorkFlow();
        rworkerStepProcessBaseDTO.setWsStepList(wsStepList).setStep(nextTodoWsStep.getStep()).setWsStep(nextTodoWsStep).setSubWorkSheet(subWorkSheet).setWorkSheet(workSheet).setSubWsProductionMode(subWsProductionMode).setWorkFlow(snapshotWorkFlow);
        return rworkerSnToDoStepGetDTO;
    }

    /**
     * 验证包装Sn合法性
     *
     * @param sn
     * @param workSheet
     */
    @Override
    public BaseResultDTO validatePackageProcessSn(String sn, WorkSheet workSheet) {
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory() || workSheet.getCategory() == WsEnum.FQC_RE_WS.getCategory()) {
            Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            if (wsReworkOptional.isPresent() && wsReworkOptional.get().getOriginalWorkSheet().getCategory() != WsEnum.OFFLINE_RE_WS.getCategory()) {
                return rbaseRmpsProxy.validateProcessSn(sn, wsReworkOptional.get().getOriginalWorkSheet().getSerialNumber());
            }
        } else if (workSheet.getCategory() == WsEnum.NORMAL_WS.getCategory()) {
            return rbaseRmpsProxy.validateProcessSn(sn, workSheet.getSerialNumber());
        }
        return new BaseResultDTO(Constants.OK);
    }
}
