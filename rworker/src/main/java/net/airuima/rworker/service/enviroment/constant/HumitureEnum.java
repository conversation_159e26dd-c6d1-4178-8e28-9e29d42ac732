package net.airuima.rworker.service.enviroment.constant;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 环境检测状态枚举(只有温湿度才会有三种状态(预警、合格、停线)，洁净度只有合格与不合格)
 *
 * <AUTHOR>
 * @date 2023/07/25
 */
public enum HumitureEnum {
    NOT_QUALIFIED("不合格", 0),
    QUALIFIED("合格", 1),
    WARNING("预警", 2),
    STOPPED("停线", 0);


    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型
     */
    private final int category;

    HumitureEnum(String name, int category) {
        this.name = name;
        this.category = category;
    }

    public String getName() {
        return name;
    }

    public int getCategory() {
        return category;
    }
}
