package net.airuima.rworker.service.rworker.facility.impl;

import net.airuima.constant.Constants;
import net.airuima.rbase.constant.WearingPartCategoryEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.base.wearingpart.WearingPartExchange;
import net.airuima.rbase.domain.base.wearingpart.WearingPartGroup;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.LatestStepWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart;
import net.airuima.rbase.dto.rworker.process.dto.*;
import net.airuima.rbase.dto.rworker.process.dto.general.WearingPartGroupGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.WearingPartSaveInfo;
import net.airuima.rbase.repository.base.wearingpart.WearingPartExchangeRepository;
import net.airuima.rbase.repository.base.wearingpart.WearingPartRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.wearingpart.BatchWorkDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.ContainerDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.LatestStepWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.SnWorkDetailWearingPartRepository;
import net.airuima.rbase.service.base.wearingpart.PedigreeStepWearingPartGroupService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.DateUtils;
import net.airuima.rworker.service.rworker.facility.IWearingPartService;
import net.airuima.util.MapperUtils;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WearingPartServiceImpl implements IWearingPartService {
    private static final String WEARINGPART_NOT_EXIST = "易损件不存在";
    private static final String EXCEED_MAX_USE_TIME = "ExceedMaxUseTime";
    private static final String ERR_MSG_PREFIX = "{易损件编码:";
    private static final List<Integer> wearingPartTimeCategories = Arrays.asList(1, 3, 4, 6);
    @Autowired
    private CommonService commonService;
    @Autowired
    private WearingPartExchangeRepository wearingPartExchangeRepository;
    @Autowired
    private BatchWorkDetailWearingPartRepository batchWorkDetailWearingPartRepository;
    @Autowired
    private ContainerDetailWearingPartRepository containerDetailWearingPartRepository;
    @Autowired
    private SnWorkDetailWearingPartRepository snWorkDetailWearingPartRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WearingPartRepository wearingPartRepository;
    @Autowired
    private LatestStepWearingPartRepository latestStepWearingPartRepository;
    @Autowired
    private PedigreeStepWearingPartGroupService pedigreeStepWearingPartGroupService;

    /**
     * 获取当前待做工序的易损件信息
     *
     * @param batchStepSaveBaseInfo 工序生产过程相关参数类
     * @return : java.util.List<net.airuima.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO.WearingPartGroupInfo> 易损件规则信息列表
     * <AUTHOR>
     * @date 2023/2/25
     **/
    @Override
    public List<WearingPartGroupGetInfo> getWearingPartInfo(RworkerStepProcessBaseDTO batchStepSaveBaseInfo) {
        //获取当前产品谱系工序易损件规则信息
        List<WearingPartGroupGetInfo> wearingPartGroupInfoList = Lists.newArrayList();
        Pedigree pedigree = Boolean.TRUE.equals(batchStepSaveBaseInfo.getSubWsProductionMode()) ? batchStepSaveBaseInfo.getSubWorkSheet().getWorkSheet().getPedigree() : batchStepSaveBaseInfo.getWorkSheet().getPedigree();
        WorkFlow workFlow = Boolean.TRUE.equals(batchStepSaveBaseInfo.getSubWsProductionMode()) ? batchStepSaveBaseInfo.getSubWorkSheet().getWorkSheet().getWorkFlow() : batchStepSaveBaseInfo.getWorkSheet().getWorkFlow();
        //获取产品谱系工序易损件规则信息
        List<PedigreeStepWearingPartGroup> pedigreeStepWearingPartGroupList = commonService.findPedigreeWorkFlowStepByWearingGroupInfo(pedigree, workFlow, batchStepSaveBaseInfo.getStep(), batchStepSaveBaseInfo.getWorkCell());
        if (CollectionUtils.isEmpty(pedigreeStepWearingPartGroupList)) {
            return wearingPartGroupInfoList;
        }
        pedigreeStepWearingPartGroupList.forEach(pedigreeStepWearingPartGroup -> {
            //获取易损件种类
            WearingPartGroup wearingPartGroup = pedigreeStepWearingPartGroup.getWearingPartGroup();
            WearingPartGroupGetInfo wearingPartGroupInfo = new WearingPartGroupGetInfo(wearingPartGroup, pedigreeStepWearingPartGroup);
            //如果易损件种类是自动记录易损件类型的话则需要根据规则ID获取最新记录使用的易损件/批次信息
            List<LatestStepWearingPart> latestStepWearingPartList = wearingPartGroup.getAutoWorkRecord() ? latestStepWearingPartRepository.findLatestStepWearingPartAndWearingPartBatch(pedigreeStepWearingPartGroup.getId(), Constants.LONG_ZERO) : new ArrayList<>();
            if(!CollectionUtils.isEmpty(latestStepWearingPartList)){
                List<WearingPart> availableWearingParts = this.getAvailableWearingPart(latestStepWearingPartList.stream().map(LatestStepWearingPart::getWearingPart).toList());
                if(CollectionUtils.isEmpty(availableWearingParts)){
                    latestStepWearingPartList = new ArrayList<>();
                }else {
                    List<Long> availableWearingPartIds =  availableWearingParts.stream().map(WearingPart::getId).toList();
                    List<LatestStepWearingPart> unAvailableLatestStepWearingParts = latestStepWearingPartList.stream().filter(latestStepWearingPart -> !availableWearingPartIds.contains(latestStepWearingPart.getWearingPart().getId())).toList();
                    if(!CollectionUtils.isEmpty(unAvailableLatestStepWearingParts)){
                        latestStepWearingPartList.removeAll(unAvailableLatestStepWearingParts);
                    }
                }
            }
            //获取工序在该易损件种类对应的最新易损件使用记录
            if (!CollectionUtils.isEmpty(latestStepWearingPartList)) {
                wearingPartGroupInfo.setLatestWearingPartUseInfoList(MapperUtils.mapAll(latestStepWearingPartList.stream().map(LatestStepWearingPart::getWearingPart).toList(), WearingPartGroupGetInfo.WearingPartInfo.class));
            }
            //获取易损件类型下易损件集合
            List<WearingPart> wearingPartList = wearingPartRepository.findByWearingPartGroupIdAndStatusLessThanAndDeleted(wearingPartGroup.getId(), WearingPartCategoryEnum.SCRAP.getCategory(), Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(wearingPartList)) {
                //返回可用易损件
                wearingPartList = this.getAvailableWearingPart(wearingPartList);
                if (!CollectionUtils.isEmpty(wearingPartList)) {
                    Map<String, List<WearingPart>> wearingPartCodeGroup = wearingPartList.stream().collect(Collectors.groupingBy(WearingPart::getCode));
                    wearingPartList = wearingPartCodeGroup.values().stream()
                            .flatMap(List::stream)
                            .sorted(Comparator.comparing(WearingPart::getSerialNumber, Comparator.nullsLast(String::compareTo)))
                            .toList();
                }
                wearingPartGroupInfo.setWearingPartInfoList(MapperUtils.mapAll(wearingPartList, WearingPartGroupGetInfo.WearingPartInfo.class));
            }
            //获取当前易损件替代关系集合
            List<WearingPartExchange> wearingPartExchangeList = wearingPartExchangeRepository.findByOriginWearingPartGroupIdAndDeleted(wearingPartGroup.getId(), Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(wearingPartExchangeList)) {
                //获取替代易损件种类集合
                List<WearingPartGroup> wearingPartGroupList = wearingPartExchangeList.stream().map(WearingPartExchange::getExchangeWearingPartGroup).distinct().toList();
                //获取替代易损件集合
                List<WearingPart> exchangeWearingPartList = wearingPartRepository.findByWearingPartGroupIdInAndStatusLessThanAndDeleted(wearingPartGroupList.stream().map(WearingPartGroup::getId).collect(Collectors.toList()), WearingPartCategoryEnum.SCRAP.getCategory(), Constants.LONG_ZERO);
                if (!CollectionUtils.isEmpty(exchangeWearingPartList)) {
                    //返回可用易损件
                    exchangeWearingPartList = this.getAvailableWearingPart(exchangeWearingPartList);
                    //Map<易损件种类ID，易损件集合>
                    Map<Long, List<WearingPart>> wearingPartMap = exchangeWearingPartList.stream().collect(Collectors.groupingBy(i -> i.getWearingPartGroup().getId()));
                    //组装易损件种类集合
                    List<WearingPartGroupGetInfo> wearingPartGroupInfoListAll = wearingPartGroupList.stream().map(i -> {
                        //易损件种类Info
                        WearingPartGroupGetInfo exchangeWearingPartGroupInfo = new WearingPartGroupGetInfo(i);
                        //易损件Info
                        if (wearingPartMap.containsKey(exchangeWearingPartGroupInfo.getId())) {
                            List<WearingPart> exchangeWaringPartList = wearingPartMap.get(exchangeWearingPartGroupInfo.getId());
                            if (!CollectionUtils.isEmpty(exchangeWaringPartList)) {
                                Map<String, List<WearingPart>> wearingPartCodeGroup = exchangeWaringPartList.stream().collect(Collectors.groupingBy(WearingPart::getCode));
                                exchangeWaringPartList = wearingPartCodeGroup.values().stream()
                                        .flatMap(List::stream)
                                        .sorted(Comparator.comparing(WearingPart::getSerialNumber, Comparator.nullsLast(String::compareTo)))
                                        .toList();
                            }
                            exchangeWearingPartGroupInfo.setWearingPartInfoList(MapperUtils.mapAll(exchangeWaringPartList, WearingPartGroupGetInfo.WearingPartInfo.class));
                        }
                        return exchangeWearingPartGroupInfo;
                    }).collect(Collectors.toList());
                    wearingPartGroupInfo.setWearingPartGroupExchangeInfoList(wearingPartGroupInfoListAll);
                }
            }
            wearingPartGroupInfoList.add(wearingPartGroupInfo);
        });

        return wearingPartGroupInfoList;
    }

    /**
     * 返回可用易损件
     *
     * @param wearingPartList 待验证易损件列表
     * @return java.util.List<net.airuima.domain.base.wearingpart.WearingPart>
     */
    public List<WearingPart> getAvailableWearingPart(List<WearingPart> wearingPartList) {
        wearingPartList = wearingPartList.stream().filter(i -> {
            if (i.getStatus() == WearingPartCategoryEnum.SCRAP.getCategory()) {
                return false;
            }
            //如果为自动且有重置次数，则直接返回
            if (i.getResetWay() == WearingPartCategoryEnum.AUTO.getCategory() && i.getMaxResetNumber() > i.getAccumulateResetNumber()) {
                return true;
            }
            //0：次数
            else if (i.getCategory() == WearingPartCategoryEnum.FREQUENCY.getCategory()) {
                return i.getMaxUseNumber() > i.getAccumulateUseNumber();
            }
            //1：时长
            else if (i.getCategory() == WearingPartCategoryEnum.DURATION.getCategory()) {
                return i.getMaxUseTime() > i.getAccumulateUseTime();
            }
            //2：有效期
            else if (i.getCategory() == WearingPartCategoryEnum.VALIDITY.getCategory()) {
                return LocalDateTime.now().isBefore(i.getExpireDate());
            }
            //3：次数+时长
            else if (i.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY.getCategory()) {
                return i.getMaxUseNumber() > i.getAccumulateUseNumber() && i.getMaxUseTime() > i.getAccumulateUseTime();
            }
            //4：时长+有效期
            else if (i.getCategory() == WearingPartCategoryEnum.DURATION_VALIDITY.getCategory()) {
                return i.getMaxUseTime() > i.getAccumulateUseTime() && LocalDateTime.now().isBefore(i.getExpireDate());
            }
            //5：次数+有效期
            else if (i.getCategory() == WearingPartCategoryEnum.FREQUENCY_VALIDITY.getCategory()) {
                return i.getMaxUseNumber() > i.getAccumulateUseNumber() && LocalDateTime.now().isBefore(i.getExpireDate());
            }
            //6：次数+时长+有效期
            else if (i.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY_VALIDITY.getCategory()) {
                return i.getMaxUseNumber() > i.getAccumulateUseNumber() && i.getMaxUseTime() > i.getAccumulateUseTime() && LocalDateTime.now().isBefore(i.getExpireDate());
            }
            return false;
        }).collect(Collectors.toList());
        return wearingPartList;
    }

    /**
     * 保存批量工序生产详情的易损件信息
     *
     * @param batchWorkDetail                批量工序生产详情
     * @param rworkerBatchStepSaveRequestDTO 易损件参数
     * <AUTHOR>
     * @date 2021-06-23
     */
    @Override
    public void saveBatchWorkDetailWearingPart(BatchWorkDetail batchWorkDetail, RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO) {
        if (ValidateUtils.isValid(rworkerBatchStepSaveRequestDTO.getWearingPartInfoList())) {
            List<WearingPart> wearingPartList = new ArrayList<>();
            for (WearingPartSaveInfo wearingPartInfo : rworkerBatchStepSaveRequestDTO.getWearingPartInfoList()) {
                WearingPart wearingPart = wearingPartRepository.findByIdAndDeleted(wearingPartInfo.getId(), Constants.LONG_ZERO).orElse(null);
                if (ObjectUtils.isEmpty(wearingPart)) {
                    throw new ResponseException("error.WearingPartNotExist", WEARINGPART_NOT_EXIST);
                }
                // 重置易损件参数，扣减使用数量, 时间, 重置数量
                resetWearingPart(wearingPartInfo, wearingPart, rworkerBatchStepSaveRequestDTO.getStartTime(), rworkerBatchStepSaveRequestDTO.getEndTime());
                //保存工单详情的易损件信息
                BatchWorkDetailWearingPart batchWorkDetailWearingPart = batchWorkDetailWearingPartRepository.findByBatchWorkDetailIdAndWearingPartIdAndDeleted(batchWorkDetail.getId(), wearingPartInfo.getId(), Constants.LONG_ZERO).orElseGet(BatchWorkDetailWearingPart::new);
                if (null == batchWorkDetailWearingPart.getId()) {
                    batchWorkDetailWearingPart.setWearingPart(new WearingPart(wearingPartInfo.getId()));
                    batchWorkDetailWearingPart.setBatchWorkDetail(batchWorkDetail);
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) && wearingPartInfo.getAbatementTime()>Constants.INT_ZERO) {
                    batchWorkDetailWearingPart.setDuration(batchWorkDetailWearingPart.getDuration() + wearingPartInfo.getAbatementTime());
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementNumber())) {
                    batchWorkDetailWearingPart.setTimes(batchWorkDetailWearingPart.getTimes() + wearingPartInfo.getAbatementNumber());
                }
                if ((ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO) && wearingPartTimeCategories.contains(wearingPart.getCategory())) {
                    int currentAccumulateUseTime = Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(rworkerBatchStepSaveRequestDTO.getStartTime(), rworkerBatchStepSaveRequestDTO.getEndTime())));
                    currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
                    batchWorkDetailWearingPart.setDuration(batchWorkDetailWearingPart.getDuration() + currentAccumulateUseTime);
                }
                batchWorkDetailWearingPartRepository.save(batchWorkDetailWearingPart);
                wearingPartList.add(wearingPart);
            }
            //若有易损件超期或者报废则不保存最新使用易损件情况
            if(wearingPartList.stream().anyMatch(wearingPart -> wearingPart.getStatus() == WearingPartCategoryEnum.EXCEED.getCategory() || wearingPart.getStatus() == WearingPartCategoryEnum.SCRAP.getCategory())){
                this.deleteLatestStepWearingPart(rworkerBatchStepSaveRequestDTO.getWearingPartInfoList().stream().map(WearingPartSaveInfo::getRuleId).toList());
            }
        }
    }

    /**
     * 保存容器生产详情的易损件信息
     *
     * @param containerDetail                    容器生产详情
     * @param rworkerContainerStepSaveRequestDTO 易损件参数
     * <AUTHOR>
     * @date 2021-06-23
     */
    @Override
    public void saveContainerWorkDetailWearingPart(ContainerDetail containerDetail, List<WearingPartSaveInfo> wearingPartInfoList, RworkerContainerStepSaveRequestDTO rworkerContainerStepSaveRequestDTO) {
        if (ValidateUtils.isValid(wearingPartInfoList)) {
            List<WearingPart> wearingPartList = new ArrayList<>();
            for (WearingPartSaveInfo wearingPartInfo : wearingPartInfoList) {
                WearingPart wearingPart = wearingPartRepository.findByIdAndDeleted(wearingPartInfo.getId(), Constants.LONG_ZERO).orElse(null);
                if (ObjectUtils.isEmpty(wearingPart)) {
                    throw new ResponseException("error.WearingPartNotFound", WEARINGPART_NOT_EXIST);
                }
                // 重置易损件参数，扣减使用数量, 时间, 重置数量
                resetWearingPart(wearingPartInfo, wearingPart, rworkerContainerStepSaveRequestDTO.getStartTime(), rworkerContainerStepSaveRequestDTO.getEndTime());
                //保存容器详情的易损件信息
                ContainerDetailWearingPart containerDetailWearingPart = containerDetailWearingPartRepository.findByContainerDetailIdAndWearingPartIdAndDeleted(containerDetail.getId(), wearingPartInfo.getId(), Constants.LONG_ZERO).orElseGet(ContainerDetailWearingPart::new);
                if (null == containerDetailWearingPart.getId()) {
                    containerDetailWearingPart.setWearingPart(new WearingPart(wearingPartInfo.getId()));
                    containerDetailWearingPart.setContainerDetail(containerDetail);
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) && wearingPartInfo.getAbatementTime()>Constants.INT_ZERO) {
                    containerDetailWearingPart.setDuration(containerDetailWearingPart.getDuration() + wearingPartInfo.getAbatementTime());
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementNumber())) {
                    containerDetailWearingPart.setTimes(containerDetailWearingPart.getTimes() + wearingPartInfo.getAbatementNumber());
                }
                if ((ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO) && wearingPartTimeCategories.contains(wearingPart.getCategory())) {
                    int currentAccumulateUseTime = Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(rworkerContainerStepSaveRequestDTO.getStartTime(), rworkerContainerStepSaveRequestDTO.getEndTime())));
                    currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
                    containerDetailWearingPart.setDuration(containerDetailWearingPart.getDuration() + currentAccumulateUseTime);
                }
                containerDetailWearingPartRepository.save(containerDetailWearingPart);
                //保存工单详情的易损件信息
                BatchWorkDetailWearingPart batchWorkDetailWearingPart = batchWorkDetailWearingPartRepository.findByBatchWorkDetailIdAndWearingPartIdAndDeleted(containerDetail.getBatchWorkDetail().getId(), wearingPartInfo.getId(), Constants.LONG_ZERO).orElseGet(BatchWorkDetailWearingPart::new);
                if (null == batchWorkDetailWearingPart.getId()) {
                    batchWorkDetailWearingPart.setWearingPart(new WearingPart(wearingPartInfo.getId()));
                    batchWorkDetailWearingPart.setBatchWorkDetail(containerDetail.getBatchWorkDetail());
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) && wearingPartInfo.getAbatementTime()>Constants.INT_ZERO) {
                    batchWorkDetailWearingPart.setDuration(batchWorkDetailWearingPart.getDuration() + wearingPartInfo.getAbatementTime());
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementNumber())) {
                    batchWorkDetailWearingPart.setTimes(batchWorkDetailWearingPart.getTimes() + wearingPartInfo.getAbatementNumber());
                }
                if ((ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO) && wearingPartTimeCategories.contains(wearingPart.getCategory())) {
                    int currentAccumulateUseTime = Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(rworkerContainerStepSaveRequestDTO.getStartTime(), rworkerContainerStepSaveRequestDTO.getEndTime())));
                    currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
                    batchWorkDetailWearingPart.setDuration(batchWorkDetailWearingPart.getDuration() + currentAccumulateUseTime);
                }
                batchWorkDetailWearingPartRepository.save(batchWorkDetailWearingPart);
                wearingPartList.add(wearingPart);
            }
            //若有易损件超期或者报废则不保存最新使用易损件情况
            if(wearingPartList.stream().anyMatch(wearingPart -> wearingPart.getStatus() == WearingPartCategoryEnum.EXCEED.getCategory() || wearingPart.getStatus() == WearingPartCategoryEnum.SCRAP.getCategory())){
                this.deleteLatestStepWearingPart(wearingPartInfoList.stream().map(WearingPartSaveInfo::getRuleId).toList());
            }
        }
    }

    /**
     * 保存SN生产详情的易损件信息
     *
     * @param snWorkDetail                SN生产详情
     * @param wearingPartInfoList         易损件参数列表
     * @param rworkerSnStepSaveRequestDTO SN请求待保存参数
     */
    @Override
    public void saveSnWorkDetailWearingPart(SnWorkDetail snWorkDetail, BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail, List<WearingPartSaveInfo> wearingPartInfoList, RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO) {
        if (!ValidateUtils.isValid(wearingPartInfoList)) {
            return;
        }
        List<WearingPart> wearingPartList = new ArrayList<>();
        wearingPartInfoList.forEach(wearingPartInfo -> {
            WearingPart wearingPart = wearingPartRepository.findByIdAndDeleted(wearingPartInfo.getId(), Constants.LONG_ZERO).orElse(null);
            if (ObjectUtils.isEmpty(wearingPart)) {
                throw new ResponseException("error.WearingPartNotFound", WEARINGPART_NOT_EXIST);
            }
            // 重置易损件参数，扣减使用数量, 时间, 重置数量
            resetWearingPart(wearingPartInfo, wearingPart, rworkerSnStepSaveRequestDTO.getStartTime(), rworkerSnStepSaveRequestDTO.getEndTime());
            //保存SN工作详情易损件信息
            SnWorkDetailWearingPart snWorkDetailWearingPart = new SnWorkDetailWearingPart();
            if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) && wearingPartInfo.getAbatementTime()>Constants.INT_ZERO) {
                snWorkDetailWearingPart.setDuration(snWorkDetailWearingPart.getDuration() + wearingPartInfo.getAbatementTime());
            }
            if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementNumber())) {
                snWorkDetailWearingPart.setTimes(snWorkDetailWearingPart.getTimes() + wearingPartInfo.getAbatementNumber());
            }
            if ((ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO) && wearingPartTimeCategories.contains(wearingPart.getCategory())) {
                int currentAccumulateUseTime = Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(rworkerSnStepSaveRequestDTO.getStartTime(), rworkerSnStepSaveRequestDTO.getEndTime())));
                currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
                snWorkDetailWearingPart.setDuration(snWorkDetailWearingPart.getDuration() + currentAccumulateUseTime);
            }
            snWorkDetailWearingPart.setSnWorkDetail(snWorkDetail).setWearingPart(wearingPart).setDeleted(Constants.LONG_ZERO);
            snWorkDetailWearingPartRepository.save(snWorkDetailWearingPart);
            //如果容器详情不为空则更新容器详情易损件信息
            if (null != containerDetail) {
                ContainerDetailWearingPart containerDetailWearingPart = containerDetailWearingPartRepository.findByContainerDetailIdAndWearingPartIdAndDeleted(containerDetail.getId(), wearingPartInfo.getId(), Constants.LONG_ZERO).orElseGet(ContainerDetailWearingPart::new);
                if (null == containerDetailWearingPart.getId()) {
                    containerDetailWearingPart.setWearingPart(new WearingPart(wearingPartInfo.getId())).setContainerDetail(containerDetail).setDeleted(Constants.LONG_ZERO);
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) && wearingPartInfo.getAbatementTime()>Constants.INT_ZERO) {
                    containerDetailWearingPart.setDuration(containerDetailWearingPart.getDuration() + wearingPartInfo.getAbatementTime());
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementNumber())) {
                    containerDetailWearingPart.setTimes(containerDetailWearingPart.getTimes() + wearingPartInfo.getAbatementNumber());
                }
                if ((ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO) && wearingPartTimeCategories.contains(wearingPart.getCategory())) {
                    int currentAccumulateUseTime = Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(rworkerSnStepSaveRequestDTO.getStartTime(), rworkerSnStepSaveRequestDTO.getEndTime())));
                    currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
                    containerDetailWearingPart.setDuration(containerDetailWearingPart.getDuration() + currentAccumulateUseTime);
                }
                containerDetailWearingPartRepository.save(containerDetailWearingPart);
            }
            //保存工单详情的易损件信息
            if (null != batchWorkDetail) {
                BatchWorkDetailWearingPart batchWorkDetailWearingPart = batchWorkDetailWearingPartRepository.findByBatchWorkDetailIdAndWearingPartIdAndDeleted(batchWorkDetail.getId(), wearingPartInfo.getId(), Constants.LONG_ZERO).orElseGet(BatchWorkDetailWearingPart::new);
                if (null == batchWorkDetailWearingPart.getId()) {
                    batchWorkDetailWearingPart.setWearingPart(new WearingPart(wearingPartInfo.getId())).setBatchWorkDetail(batchWorkDetail).setDeleted(Constants.LONG_ZERO);
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) && wearingPartInfo.getAbatementTime()>Constants.INT_ZERO) {
                    batchWorkDetailWearingPart.setDuration(batchWorkDetailWearingPart.getDuration() + wearingPartInfo.getAbatementTime());
                }
                if (!ObjectUtils.isEmpty(wearingPartInfo.getAbatementNumber())) {
                    batchWorkDetailWearingPart.setTimes(batchWorkDetailWearingPart.getTimes() + wearingPartInfo.getAbatementNumber());
                }
                if ((ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO) && wearingPartTimeCategories.contains(wearingPart.getCategory())) {
                    int currentAccumulateUseTime = Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(rworkerSnStepSaveRequestDTO.getStartTime(), rworkerSnStepSaveRequestDTO.getEndTime())));
                    currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
                    batchWorkDetailWearingPart.setDuration(batchWorkDetailWearingPart.getDuration() + currentAccumulateUseTime);
                }
                wearingPartList.add(wearingPart);
                batchWorkDetailWearingPartRepository.save(batchWorkDetailWearingPart);
            }
        });
        //若有易损件超期或者报废则不保存最新使用易损件情况
        if(wearingPartList.stream().anyMatch(wearingPart -> wearingPart.getStatus() == WearingPartCategoryEnum.EXCEED.getCategory() || wearingPart.getStatus() == WearingPartCategoryEnum.SCRAP.getCategory())){
            this.deleteLatestStepWearingPart(wearingPartInfoList.stream().map(WearingPartSaveInfo::getRuleId).toList());
        }
    }

    /**
     * 保存工序易损件规则对应的最新使用易损件信息
     *
     * @param wearingPartSaveInfoList 待保存易损件参数列表
     */
    @Override
    public void saveLatestStepWearingPart(List<WearingPartSaveInfo> wearingPartSaveInfoList) {
        List<WearingPartSaveInfo> wearingPartRuleIdExistInfoList = wearingPartSaveInfoList.stream().filter(wearingPartSaveInfo -> Objects.nonNull(wearingPartSaveInfo.getRuleId())).toList();
        if (!CollectionUtils.isEmpty(wearingPartRuleIdExistInfoList)) {
            Set<Long> ruleIdList = wearingPartRuleIdExistInfoList.stream().map(WearingPartSaveInfo::getRuleId).collect(Collectors.toSet());
            latestStepWearingPartRepository.deleteByPedigreeStepWearingPartGroupIdIn(ruleIdList.stream().toList());
        }
        //工序最新使用易损件列表
        List<LatestStepWearingPart> latestStepWearingPartList = new ArrayList<>();
        wearingPartRuleIdExistInfoList.forEach(wearingPartSaveInfo -> {
            if (wearingPartSaveInfo.getAutoWorkRecord() && Objects.nonNull(wearingPartSaveInfo.getRuleId())) {
                latestStepWearingPartList.add(new LatestStepWearingPart(new PedigreeStepWearingPartGroup(wearingPartSaveInfo.getRuleId()), new WearingPart(wearingPartSaveInfo.getId())));
            }
        });
        if (!CollectionUtils.isEmpty(latestStepWearingPartList)) {
            latestStepWearingPartRepository.saveAll(latestStepWearingPartList);
        }

    }

    /**
     * 根据规则ID列表删除所有最新使用的易损件列表
     *
     * @param ruleIds 规则ID列表
     */
    @Override
    public void deleteLatestStepWearingPart(List<Long> ruleIds) {
        latestStepWearingPartRepository.deleteByPedigreeStepWearingPartGroupIdIn(ruleIds);
    }

    /**
     * 重置易损件参数，扣减使用数量, 时间, 重置数量
     *
     * @param wearingPartInfo 易损件
     * @param startTime       工序开始时间
     * @param endTime         工序结束时间
     * <AUTHOR>
     * @date 2023/2/28
     **/
    public void resetWearingPart(WearingPartSaveInfo wearingPartInfo, WearingPart wearingPart, LocalDateTime startTime, LocalDateTime endTime) {
        if (!ObjectUtils.isEmpty(wearingPart)) {
            //如果易损件状态为超期或者报废(之前部分工单导致), 则报错
            if (wearingPart.getStatus() == WearingPartCategoryEnum.EXCEED.getCategory() || wearingPart.getStatus() == WearingPartCategoryEnum.SCRAP.getCategory()) {
                return;
            }
            // 修改易损件的状态为在用
            wearingPart.setStatus(WearingPartCategoryEnum.INUSE.getCategory());
            // 易损件重置方式：手动
            if (wearingPart.getResetWay() == WearingPartCategoryEnum.MANUAL.getCategory()) {
                //TODO 可改为功能Key的形式
                saveWearingPartByResetWayManual(wearingPart, wearingPartInfo, startTime, endTime);
            }
            // 易损件重置方式：自动
            else if (wearingPart.getResetWay() == WearingPartCategoryEnum.AUTO.getCategory()) {
                //TODO 可改为功能Key的形式
                saveWearingPartByResetWayAuto(wearingPart, wearingPartInfo, startTime, endTime);
            }
        }
    }

    /**
     * 易损件重置方式：手动
     *
     * @param wearingPart     易损件对象
     * @param wearingPartInfo 易损件参数
     * @param startTime       工序开启时间
     * @param endTime         工序结束时间
     * <AUTHOR>
     * @date 2023/2/25
     **/
    public void saveWearingPartByResetWayManual(WearingPart wearingPart, WearingPartSaveInfo wearingPartInfo, LocalDateTime startTime, LocalDateTime endTime) {
        boolean wearingPartExceed = false;
        //易损件管控类型为0：次数
        if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY.getCategory()) {
            saveWearingPartByResetWayManualNumber(wearingPart, wearingPartInfo);
            wearingPartExceed = wearingPart.getAccumulateUseNumber() >= wearingPart.getMaxUseNumber();
        }
        //易损件管控类型为1：时长
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION.getCategory()) {
            saveWearingPartByResetWayManualTime(wearingPart, wearingPartInfo, startTime, endTime);
            wearingPartExceed = wearingPart.getAccumulateUseTime() >= wearingPart.getMaxUseTime();
        }
        //易损件管控类型为2：有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.VALIDITY.getCategory()) {
            saveWearingPartByResetWayManualExpireDate(wearingPart, wearingPartInfo);
            wearingPartExceed = wearingPart.getExpireDate().isBefore(LocalDateTime.now());
        }
        // 易损件管控类型为3：次数+时长
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY.getCategory()) {
            saveWearingPartByResetWayManualNumber(wearingPart, wearingPartInfo);
            saveWearingPartByResetWayManualTime(wearingPart, wearingPartInfo, startTime, endTime);
            wearingPartExceed = wearingPart.getAccumulateUseNumber() >= wearingPart.getMaxUseNumber() || wearingPart.getAccumulateUseTime() >= wearingPart.getMaxUseTime();
        }
        // 易损件管控类型为4：时长+有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_VALIDITY.getCategory()) {
            saveWearingPartByResetWayManualTime(wearingPart, wearingPartInfo, startTime, endTime);
            saveWearingPartByResetWayManualExpireDate(wearingPart, wearingPartInfo);
            wearingPartExceed = wearingPart.getAccumulateUseTime() >= wearingPart.getMaxUseTime() || wearingPart.getExpireDate().isBefore(LocalDateTime.now());
        }
        //易损件管控类型为5：次数+有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY_VALIDITY.getCategory()) {
            saveWearingPartByResetWayManualNumber(wearingPart, wearingPartInfo);
            saveWearingPartByResetWayManualExpireDate(wearingPart, wearingPartInfo);
            wearingPartExceed = wearingPart.getAccumulateUseNumber() >= wearingPart.getMaxUseNumber() || wearingPart.getExpireDate().isBefore(LocalDateTime.now());
        }
        //易损件管控类型为6：次数+时长+有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY_VALIDITY.getCategory()) {
            saveWearingPartByResetWayManualNumber(wearingPart, wearingPartInfo);
            saveWearingPartByResetWayManualTime(wearingPart, wearingPartInfo, startTime, endTime);
            saveWearingPartByResetWayManualExpireDate(wearingPart, wearingPartInfo);
            wearingPartExceed = wearingPart.getAccumulateUseNumber() >= wearingPart.getMaxUseNumber() || wearingPart.getExpireDate().isBefore(LocalDateTime.now()) || wearingPart.getAccumulateUseTime() >= wearingPart.getMaxUseTime();
        }
        //累计重置次数(大于)最大重置次数并且任何组合条件之一出现超出最大限定期限时,易损件状态变为报废 ：3
        if (wearingPart.getAccumulateResetNumber() >= wearingPart.getMaxResetNumber() && wearingPartExceed) {
            wearingPart.setStatus(WearingPartCategoryEnum.SCRAP.getCategory());
        }
        wearingPartRepository.save(wearingPart);
    }

    /**
     * 易损件重置方式：手动，次数
     *
     * @param wearingPart     易损件对象
     * @param wearingPartInfo 易损件参数
     * <AUTHOR>
     * @date 2023/3/3
     **/
    public void saveWearingPartByResetWayManualNumber(WearingPart wearingPart, WearingPartSaveInfo wearingPartInfo) {
        // 更新累计使用次数,若累计使用次数超过最大次数时则更新累计使用次数为最大使用次数，否则累加
        if (wearingPart.getResetWay() == WearingPartCategoryEnum.MANUAL.getCategory() && wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber()) {
            wearingPart.setAccumulateUseNumber(wearingPart.getMaxUseNumber());
        }else {
            wearingPart.setAccumulateUseNumber(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber());
        }
    }

    /**
     * 易损件重置方式：手动，时长
     *
     * @param wearingPart     易损件对象
     * @param wearingPartInfo 易损件参数
     * @param startTime       开始时间
     * @param endTime         结束时间
     * <AUTHOR>
     * @date 2023/3/3
     **/
    public void saveWearingPartByResetWayManualTime(WearingPart wearingPart, WearingPartSaveInfo wearingPartInfo, LocalDateTime startTime, LocalDateTime endTime) {
        int currentAccumulateUseTime = wearingPart.getAccumulateUseTime();
        if(ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO){
            int currentUseTime = Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(startTime, endTime)));
            currentUseTime = currentUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentUseTime;
            currentAccumulateUseTime = currentAccumulateUseTime + currentUseTime;
        }else {
            currentAccumulateUseTime = currentAccumulateUseTime + wearingPartInfo.getAbatementTime();
        }
        //累计使用时间大于最大使用时间 状态变为超期：2
        if (currentAccumulateUseTime > wearingPart.getMaxUseTime()) {
            wearingPart.setStatus(WearingPartCategoryEnum.EXCEED.getCategory());
        }
        wearingPart.setAccumulateUseTime(Math.min(currentAccumulateUseTime, wearingPart.getMaxUseTime()));
    }

    /**
     * 易损件重置方式：手动，有效期
     *
     * @param wearingPart     易损件对象
     * @param wearingPartInfo 易损件参数
     * <AUTHOR>
     * @date 2023/3/3
     **/
    public void saveWearingPartByResetWayManualExpireDate(WearingPart wearingPart, WearingPartSaveInfo wearingPartInfo) {
        //不在有效期内 状态变为超期：2
        if (wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
            wearingPart.setStatus(WearingPartCategoryEnum.EXCEED.getCategory());
        }
    }

    /**
     * 可能发生重置即可进行该递归方法(重置次数>=0)
     *
     * @param wearingPart  待验证易损件
     * @param number       次数扣减数
     * @param time         时间扣减数
     * @param isExpireDate 是否判断有效期
     * <AUTHOR>
     * @date 2023/3/3
     **/
    public void resetWearingPartByNumberAndTimeAndExpireDate(WearingPart wearingPart, Integer number, Integer time, Boolean isExpireDate) {
        //累计使用次数
        int accumulateUseNumber = wearingPart.getAccumulateUseNumber();
        //累计使用时长
        int accumulateUseTime = wearingPart.getAccumulateUseTime();

        //1. 重置易损件使用次数（可能同时重置时长及有效期）
        if (number > 0) {
            //若次数待扣减数>0，且待扣减数>当前轮次可扣减数，则进行重置
            if (number > (wearingPart.getMaxUseNumber() - accumulateUseNumber)) {
                //1. 次数重置
                if (wearingPart.getAccumulateResetNumber() >= wearingPart.getMaxResetNumber()) {
                    //若当前累计重置次数大于最大重置次数, 则直接报错
                   return;
                }
                //若待扣减数>最大使用次数，则累计使用次数=最大使用次数-(最大使用次数-累计使用次数)；若待扣减数<=最大使用次数，则累计使用次数=待扣减数-(最大使用次数-累计使用次数)
                accumulateUseNumber = (number > wearingPart.getMaxUseNumber() ? wearingPart.getMaxUseNumber() : number) - (wearingPart.getMaxUseNumber() - accumulateUseNumber);
                //若待扣减数>最大使用次数，则待扣减数=待扣减数-最大使用次数；若待扣减数<最大使用次数，则待扣减数归0(当次重置直接扣完)
                number = (number > wearingPart.getMaxUseNumber()) ? (number - wearingPart.getMaxUseNumber()) : 0;
                //重置次数+1
                wearingPart.setAccumulateResetNumber(wearingPart.getAccumulateResetNumber() + 1);
                //2. 重置次数后，时长也需要同步重置
                if (time > 0 && time > (wearingPart.getMaxUseTime() - accumulateUseTime)) {
                    //若待扣减数>最大使用时间，则累计使用时间=最大使用时间-(最大使用时间-累计使用时间)；若待扣减数<=最大使用时间，则累计使用时间=待扣减数-(最大使用时间-累计使用时间)
                    accumulateUseTime = (time > wearingPart.getMaxUseTime() ? wearingPart.getMaxUseTime() : time) - (wearingPart.getMaxUseTime() - accumulateUseTime);
                    //若待扣减数>最大使用时间，则待扣减数=待扣减数-最大使用时间；若待扣减数<最大使用时间，则待扣减数归0(当次重置直接扣完)
                    time = (time > wearingPart.getMaxUseTime()) ? (time - wearingPart.getMaxUseTime()) : 0;
                }
                //若时长待扣减数>0，且待扣减数<当前轮次可扣减数，则直接相加
                else if (time > 0 && time <= (wearingPart.getMaxUseTime() - accumulateUseTime)) {
                    //累计扣减数 = 累计扣减数 + 待扣减数
                    accumulateUseTime = accumulateUseTime + time;
                    //待扣减数归0(当次重置直接扣完)
                    time = 0;
                }
                wearingPart.setAccumulateUseTime(accumulateUseTime);

                //3. 重置次数后，有效期也需要同步重置
                if (!ObjectUtils.isEmpty(wearingPart.getExpireDate()) && wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                    wearingPart.setExpireDate(LocalDateTime.now().plus(wearingPart.getWearingPartGroup().getEffectiveDay(), ChronoUnit.DAYS));
                    //有效期只需要重置一次即可
                    isExpireDate = Boolean.FALSE;
                }
            }
            //若待扣减数>0，且待扣减数<当前轮次可扣减数，则直接累加，不需要重置
            else if (number <= wearingPart.getMaxUseNumber() - accumulateUseNumber) {
                //累计扣减数 = 累计扣减数 + 待扣减数
                accumulateUseNumber = accumulateUseNumber + number;
                //待扣减数归0(当次重置直接扣完)
                number = 0;
            }
            wearingPart.setAccumulateUseNumber(accumulateUseNumber);
        }
        //2. 重置易损件使用时长（可能同时重置有效期）
        if (number == 0 && time > 0) {
            //若时长待扣减数>0，且待扣减数>当前轮次可扣减数，则进行重置
            if (time > wearingPart.getMaxUseTime() - accumulateUseTime) {
                //1. 时长重置
                if (wearingPart.getAccumulateResetNumber() >= wearingPart.getMaxResetNumber()) {
                    //若重置后，累计重置时间大于最大重置时间, 则直接报错
                    return;
                }
                //若待扣减数>最大使用时间，则累计使用时间=最大使用时间-(最大使用时间-累计使用时间)；若待扣减数<=最大使用时间，则累计使用时间=待扣减数-(最大使用时间-累计使用时间)
                accumulateUseTime = (time > wearingPart.getMaxUseTime() ? wearingPart.getMaxUseTime() : time) - (wearingPart.getMaxUseTime() - accumulateUseTime);
                //若待扣减数>最大使用时间，则待扣减数=待扣减数-最大使用时间；若待扣减数<最大使用时间，则待扣减数归0(当次重置直接扣完)
                time = (time > wearingPart.getMaxUseTime()) ? (time - wearingPart.getMaxUseTime()) : 0;
                //重置次数+1
                wearingPart.setAccumulateResetNumber(wearingPart.getAccumulateResetNumber() + 1);
                //2. 有效期重置
                if (!ObjectUtils.isEmpty(wearingPart.getExpireDate()) && wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                    wearingPart.setExpireDate(LocalDateTime.now().plus(wearingPart.getWearingPartGroup().getEffectiveDay(), ChronoUnit.DAYS));
                    //有效期只需要重置一次即可
                    isExpireDate = Boolean.FALSE;
                }
            }
            //若待扣减数>0，且待扣减数<当前轮次可扣减数，则直接相加
            else if (time <= wearingPart.getMaxUseTime() - accumulateUseTime) {
                //累计扣减数 = 累计扣减数 + 待扣减数
                accumulateUseTime = accumulateUseTime + time;
                //待扣减数归0(当次重置直接扣完)
                time = 0;
            }
            wearingPart.setAccumulateUseTime(accumulateUseTime);
        }
        //3. 次数和时间扣减数扣完后，重置易损件有效期
        if (Boolean.TRUE.equals(number == 0 && time == 0 && isExpireDate && !ObjectUtils.isEmpty(wearingPart.getExpireDate())) && wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
            if (wearingPart.getAccumulateResetNumber() >= wearingPart.getMaxResetNumber()) {
                return;
            }
            //更新失效期，以及重置次数
            LocalDateTime expireDate = LocalDateTime.now().plus(wearingPart.getWearingPartGroup().getEffectiveDay(), ChronoUnit.DAYS);
            wearingPart.setExpireDate(expireDate).setAccumulateResetNumber(wearingPart.getAccumulateResetNumber() + Constants.INT_ONE);
        }
        //4. 如果当前扣减需要多重重置
        if (number > 0 || time > 0) {
            resetWearingPartByNumberAndTimeAndExpireDate(wearingPart, number, time, isExpireDate);
        }
    }

    /**
     * 易损件重置方式：自动
     *
     * @param wearingPart     易损件对象
     * @param wearingPartInfo 易损件参数
     * @param startTime       工序开启时间
     * @param endTime         工序结束时间
     * @return : void
     * <AUTHOR>
     * @date 2023/2/25
     **/
    public void saveWearingPartByResetWayAuto(WearingPart wearingPart, WearingPartSaveInfo wearingPartInfo, LocalDateTime startTime, LocalDateTime endTime) {
        boolean wearingPartExceed = false;
        // 易损件管控类型为0：次数
        if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY.getCategory()) {
            resetWearingPartByNumberAndTimeAndExpireDate(wearingPart, wearingPartInfo.getAbatementNumber(), Constants.INT_ZERO, Boolean.FALSE);
            wearingPartExceed = wearingPart.getAccumulateUseNumber() >= wearingPart.getMaxUseNumber();
        }
        // 易损件管控类型为1：时长
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION.getCategory()) {
            int currentAccumulateUseTime = ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO ? Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(startTime, endTime))) : wearingPartInfo.getAbatementTime();
            currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
            resetWearingPartByNumberAndTimeAndExpireDate(wearingPart, Constants.INT_ZERO, currentAccumulateUseTime, Boolean.FALSE);
            wearingPartExceed = wearingPart.getAccumulateUseTime() >= wearingPart.getMaxUseTime();
        }
        // 易损件管控类型为2：有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.VALIDITY.getCategory()) {
            resetWearingPartByNumberAndTimeAndExpireDate(wearingPart, Constants.INT_ZERO, Constants.INT_ZERO, Boolean.TRUE);
            wearingPartExceed = wearingPart.getExpireDate().isBefore(LocalDateTime.now());
        }
        // 易损件管控类型为3：次数+时长
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY.getCategory()) {
            int currentAccumulateUseTime = ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO ? Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(startTime, endTime))) : wearingPartInfo.getAbatementTime();
            currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
            resetWearingPartByNumberAndTimeAndExpireDate(wearingPart, wearingPartInfo.getAbatementNumber(), currentAccumulateUseTime, Boolean.FALSE);
            wearingPartExceed = wearingPart.getAccumulateUseNumber() >= wearingPart.getMaxUseNumber() || wearingPart.getAccumulateUseTime() >= wearingPart.getMaxUseTime();
        }
        // 易损件管控类型为4：时长+有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_VALIDITY.getCategory()) {
            int currentAccumulateUseTime = ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO ? Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(startTime, endTime))) : wearingPartInfo.getAbatementTime();
            currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
            resetWearingPartByNumberAndTimeAndExpireDate(wearingPart, Constants.INT_ZERO, currentAccumulateUseTime, Boolean.TRUE);
            wearingPartExceed = wearingPart.getAccumulateUseTime() >= wearingPart.getMaxUseTime() || wearingPart.getExpireDate().isBefore(LocalDateTime.now());
        }
        // 易损件管控类型为5：次数+有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY_VALIDITY.getCategory()) {
            resetWearingPartByNumberAndTimeAndExpireDate(wearingPart, wearingPartInfo.getAbatementNumber(), Constants.INT_ZERO, Boolean.TRUE);
            wearingPartExceed = wearingPart.getAccumulateUseNumber() >= wearingPart.getMaxUseNumber() || wearingPart.getExpireDate().isBefore(LocalDateTime.now());
        }
        // 易损件管控类型为6：时长+次数+有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY_VALIDITY.getCategory()) {
            int currentAccumulateUseTime = ObjectUtils.isEmpty(wearingPartInfo.getAbatementTime()) || wearingPartInfo.getAbatementTime()<=Constants.INT_ZERO  ? Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(startTime, endTime))) : wearingPartInfo.getAbatementTime();
            currentAccumulateUseTime = currentAccumulateUseTime == Constants.INT_ZERO ? Constants.INT_ONE:currentAccumulateUseTime;
            resetWearingPartByNumberAndTimeAndExpireDate(wearingPart, wearingPartInfo.getAbatementNumber(), currentAccumulateUseTime, Boolean.TRUE);
            wearingPartExceed = wearingPart.getAccumulateUseNumber() >= wearingPart.getMaxUseNumber() || wearingPart.getExpireDate().isBefore(LocalDateTime.now()) || wearingPart.getAccumulateUseTime() >= wearingPart.getMaxUseTime();
        }
        //累计重置次数(大于)最大重置次数并且任何组合条件之一出现超出最大限定期限时,易损件状态变为报废 ：3
        if (wearingPart.getAccumulateResetNumber() >= wearingPart.getMaxResetNumber() && wearingPartExceed) {
            wearingPart.setStatus(WearingPartCategoryEnum.SCRAP.getCategory());
        }
        wearingPartRepository.save(wearingPart);
    }

    /**
     * 提前预警易损件
     *
     * @param wearingPart 易损件信息
     * @return net.airuima.dto.client.ClientGetWearingPartInfoDTO
     */
    public RworkerValidWearingPartInfoDTO proactiveManualModelValidate(RworkerValidWearingPartInfoDTO rworkerValidWearingPartInfoDTO, WearingPart wearingPart) {
        // 易损件管控类型为0：次数
        if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY.getCategory()) {
            //如果是 手动重置且次数不够，则报错
            exceedMaxUseNumber(wearingPart, Constants.INT_ONE);
            rworkerValidWearingPartInfoDTO.setControlCategory(WearingPartCategoryEnum.FREQUENCY.getCategory()).setResidueNumber(wearingPart.getMaxUseNumber() - wearingPart.getAccumulateUseNumber());
        }
        // 易损件管控类型为1：时长
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION.getCategory()) {
            //如果是 手动重置且使用时间不够，则报错
            exceedMaxUseTime(wearingPart);
            rworkerValidWearingPartInfoDTO.setControlCategory(WearingPartCategoryEnum.DURATION.getCategory()).setResidueTime(wearingPart.getMaxUseTime() - wearingPart.getAccumulateUseTime());
        }
        // 易损件管控类型为2：有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.VALIDITY.getCategory()) {
            //如果是 手动重置且不在有效期内，则报错
            exceedExpireDate(wearingPart);
            rworkerValidWearingPartInfoDTO.setControlCategory(WearingPartCategoryEnum.VALIDITY.getCategory()).setResidueDay(DateUtils.formatTime(Duration.between(LocalDateTime.now(), wearingPart.getExpireDate()).getSeconds() * 1000));
        }
        // 易损件管控类型为3：时长+次数
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY.getCategory()) {
            //如果是 手动重置且次数不够，则报错
            exceedMaxUseNumber(wearingPart, Constants.INT_ONE);
            //如果是 手动重置且可使用时长不够，则报错
            exceedMaxUseTime(wearingPart);
            rworkerValidWearingPartInfoDTO.setControlCategory(WearingPartCategoryEnum.DURATION_FREQUENCY.getCategory()).setResidueTime(wearingPart.getMaxUseTime() - wearingPart.getAccumulateUseTime()).setResidueNumber(wearingPart.getMaxUseNumber() - wearingPart.getAccumulateUseNumber());
        }
        // 易损件管控类型为4：时长+有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_VALIDITY.getCategory()) {
            //如果是 手动重置且使用时间不够，则报错
            exceedMaxUseTime(wearingPart);
            //如果是 手动重置且不在有效期内，则报错
            exceedExpireDate(wearingPart);
            rworkerValidWearingPartInfoDTO.setControlCategory(WearingPartCategoryEnum.DURATION_VALIDITY.getCategory()).setResidueTime(wearingPart.getMaxUseTime() - wearingPart.getAccumulateUseTime()).setResidueDay(DateUtils.formatTime(Duration.between(LocalDateTime.now(), wearingPart.getExpireDate()).getSeconds() * 1000));
        }
        // 易损件管控类型为5：次数+有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY_VALIDITY.getCategory()) {
            //如果是 手动重置且次数不够，则报错
            exceedMaxUseNumber(wearingPart, Constants.INT_ONE);
            //如果是 手动重置且不在有效期内，则报错
            exceedExpireDate(wearingPart);
            rworkerValidWearingPartInfoDTO.setControlCategory(WearingPartCategoryEnum.FREQUENCY_VALIDITY.getCategory()).setResidueNumber(wearingPart.getMaxUseNumber() - wearingPart.getAccumulateUseNumber()).setResidueDay(DateUtils.formatTime(Duration.between(LocalDateTime.now(), wearingPart.getExpireDate()).getSeconds() * 1000));
        }
        // 易损件管控类型为6：时长+次数+有效期
        else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY_VALIDITY.getCategory()) {
            //如果是 手动重置且使用时间不够，则报错
            exceedMaxUseTime(wearingPart);
            //如果是 手动重置且次数不够，则报错
            exceedMaxUseNumber(wearingPart, Constants.INT_ONE);
            //如果是 手动重置且不在有效期内，则报错
            exceedExpireDate(wearingPart);
            rworkerValidWearingPartInfoDTO.setControlCategory(WearingPartCategoryEnum.DURATION_FREQUENCY_VALIDITY.getCategory()).setResidueTime(wearingPart.getMaxUseTime() - wearingPart.getAccumulateUseTime()).setResidueNumber(wearingPart.getMaxUseNumber() - wearingPart.getAccumulateUseNumber()).setResidueDay(DateUtils.formatTime(Duration.between(LocalDateTime.now(), wearingPart.getExpireDate()).getSeconds() * 1000));
        }
        return rworkerValidWearingPartInfoDTO;
    }

    /**
     * 如果是 手动重置且次数不够，则报错
     *
     * @param wearingPart 易损件
     * @param number      扣减数
     * <AUTHOR>
     * @date 2023/2/28
     **/
    public void exceedMaxUseNumber(WearingPart wearingPart, Integer number) {
        if (wearingPart.getResetWay() == WearingPartCategoryEnum.MANUAL.getCategory() && wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber() + number) {
            throw new ResponseException("ExceedMaxUseNumber", ERR_MSG_PREFIX + wearingPart.getCode() + "累计使用次数(大于)最大使用次数");
        }
    }

    /**
     * 如果是 手动重置且使用时间不够，则报错
     *
     * @param wearingPart 易损件
     * <AUTHOR>
     * @date 2023/2/28
     **/
    public void exceedMaxUseTime(WearingPart wearingPart) {
        if (wearingPart.getResetWay() == WearingPartCategoryEnum.MANUAL.getCategory() && wearingPart.getAccumulateUseTime() > wearingPart.getMaxUseTime()) {
            throw new ResponseException(EXCEED_MAX_USE_TIME, ERR_MSG_PREFIX + wearingPart.getCode() + "累计使用时间(大于)最大使用时间");
        }
    }

    /**
     * 如果是 手动重置且不在有效期内，则报错
     *
     * @param wearingPart 易损件
     * <AUTHOR>
     * @date 2023/2/28
     **/
    public void exceedExpireDate(WearingPart wearingPart) {
        if (wearingPart.getResetWay() == WearingPartCategoryEnum.MANUAL.getCategory() && wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
            throw new ResponseException("error.ExceedExpireDate", ERR_MSG_PREFIX + wearingPart.getCode() + "不在有效期内");
        }
    }
}
