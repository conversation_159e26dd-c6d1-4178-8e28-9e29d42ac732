package net.airuima.rworker.service.rworker.facility.impl;

import net.airuima.rbase.proxy.rfms.RbaseFacilityProxy;
import net.airuima.rworker.service.rworker.facility.IFacilityInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/5/10
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class FacilityInspectionServiceImpl implements IFacilityInspectionService {

    @Autowired
    private RbaseFacilityProxy rbaseFacilityProxy;

    /**
     * 验证设备基础状态
     * @param facilityIdList 设备ID列表
     */
    @Override
    public void validateFacilityBaseStatus(List<Long> facilityIdList) {
        if(CollectionUtils.isEmpty(facilityIdList)){
            return;
        }
        rbaseFacilityProxy.validateFacilityBaseStatus(facilityIdList);
    }

    /**
     * 验证设备点检等状态是否合规
     *
     * @param facilityIdList 设备ID列表
     */
    @Override
    public void validateFacilityPointInspection(List<Long> facilityIdList) {
        if(CollectionUtils.isEmpty(facilityIdList)){
            return;
        }
        rbaseFacilityProxy.validateFacilityPointInspect(facilityIdList);
    }

    /**
     * 验证设备巡检等状态是否合规
     *
     * @param facilityIdList 设备ID列表
     */
    @Override
    public void validateFacilityPatrolInspection(List<Long> facilityIdList) {
        if(CollectionUtils.isEmpty(facilityIdList)){
            return;
        }
        rbaseFacilityProxy.validateFacilityPatrolInspect(facilityIdList);
    }
}
