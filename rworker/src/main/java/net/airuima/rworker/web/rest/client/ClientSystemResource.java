package net.airuima.rworker.web.rest.client;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.constant.Constants;
import net.airuima.util.ResponseContent;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork系统相关Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "Rworker-Client系统相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/client/systems")
public class ClientSystemResource {

    /**
     * 获得当前服务器时间
     *
     * @return Map<String, String>
     */
    @Operation(summary= "获得当前服务器时间")
    @PostMapping("/system-time")
    public ResponseEntity<ResponseContent<Map<String, String>>> getSystemTime() {
        try {
            Map<String, String> map = new HashMap<>(Constants.INITIAL_CAPACITY);
            map.put("systemTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            return ResponseContent.isOk(map);
        } catch (Exception e) {
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

}
