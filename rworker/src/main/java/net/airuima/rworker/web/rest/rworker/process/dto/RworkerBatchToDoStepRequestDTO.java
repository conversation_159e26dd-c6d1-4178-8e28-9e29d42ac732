package net.airuima.rworker.web.rest.rworker.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
@Schema(description = "Rworker-Web请求批量待做工序参数DTO")
public class RworkerBatchToDoStepRequestDTO implements Serializable {

    /**
     * 员工ID
     */
    @Schema(description = "员工ID", required = true)
    private Long staffId;

    /**
     * 工位id
     */
    @Schema(description = "工位ID", required = true)
    private Long workCellId;

    /**
     * 投产工单id
     */
    @Schema(description = "投产工单id", required = true)
    private Long productWorkSheetId;

    public Long getStaffId() {
        return staffId;
    }

    public RworkerBatchToDoStepRequestDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerBatchToDoStepRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerBatchToDoStepRequestDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }
}
