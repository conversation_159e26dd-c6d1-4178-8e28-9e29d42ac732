package net.airuima.rworker.web.rest.rworker.person.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/20
 */
@Schema(description = "Rworker-Web员工登陆请求参数DTO")
public class StaffLoginDTO implements Serializable {

    /**
     * 员工密码
     */
    @Schema(description = "员工编码", required = true)
    private String code;

    /**
     * 员工密码
     */
    @Schema(description = "员工密码", required = false)
    private String password;

    /**
     * 登录类型 true:密码登陆，false:非密码登陆
     */
    @Schema(description = "登录类型 true:密码登陆，false:非密码登陆 ", required = false)
    private Boolean passWordLogin;


    public Boolean getPassWordLogin() {
        return passWordLogin;
    }

    public StaffLoginDTO setPassWordLogin(Boolean passWordLogin) {
        this.passWordLogin = passWordLogin;
        return this;
    }

    public String getCode() {
        return code;
    }

    public StaffLoginDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getPassword() {
        return password;
    }

    public StaffLoginDTO setPassword(String password) {
        this.password = password;
        return this;
    }
}
