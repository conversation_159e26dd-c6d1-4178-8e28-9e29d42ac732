package net.airuima.rworker.web.rest.client;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.dto.client.ClientStaffDTO;
import net.airuima.rworker.service.client.api.IClientStaffService;
import net.airuima.util.ResponseContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork员工相关Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "Rworker-Client员工相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/client/staff")
public class ClientStaffResource {

    @Autowired
    private IClientStaffService[] iClientStaffServices;

    /**
     * 通过员工号获取员工及工位信息
     *
     * @param clientStaffDto 员工信息DTO
     * @return
     */
    @Operation(summary= "通过员工号获取员工及工位信息")
    @PostMapping("/staff-info")
    public ResponseEntity<ResponseContent<ClientStaffDTO>> getStaffInfo(@RequestBody ClientStaffDTO clientStaffDto) {
        try {
            return ResponseContent.isOk(iClientStaffServices[0].getStaffInfo(clientStaffDto));
        } catch (Exception e) {
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 通过员工ID与工位ID验证脱岗时长合法性
     * @param staffId 员工ID
     * @param workCellId 工位ID
     * @return Boolean
     */
    @Operation(summary = ("通过员工ID与工位ID验证脱岗时长合法性"))
    @GetMapping("/validateJobOffTime")
    public ResponseEntity<ResponseContent<Boolean>> validateJobOffTime(@RequestParam("staffId") Long staffId, @RequestParam("workCellId") Long workCellId) {
        try {
            return ResponseContent.isOk(iClientStaffServices[0].validateJobOffTime(staffId, workCellId));
        } catch (Exception e) {
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

}
