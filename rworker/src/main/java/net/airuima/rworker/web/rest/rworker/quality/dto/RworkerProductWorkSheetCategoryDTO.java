package net.airuima.rworker.web.rest.rworker.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/25
 */
@Schema(description = "获取质检方案")
public class RworkerProductWorkSheetCategoryDTO {
    @Schema(description = "任务ID")
    private Long taskId;

    /**
     * 投产工单编码
     */
    @Schema(description = "投产工单编码")
    private String serialNumber;

    /**
     * 类型(0,首检;1,巡检)
     */
    @Schema(description = "类型(0,首检;1,巡检)")
    private Integer category;

    /**
     * 工位id
     */
    @Schema(description = "工位id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workCellId;

    /**
     * 工序id
     */
    @Schema(description = "工序id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stepId;

    /**
     * 检查项目id
     */
    @Schema(description = "检查项目id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long varietyId;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    private String containerCode;

    public Long getTaskId() {
        return taskId;
    }

    public RworkerProductWorkSheetCategoryDTO setTaskId(Long taskId) {
        this.taskId = taskId;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public RworkerProductWorkSheetCategoryDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public RworkerProductWorkSheetCategoryDTO setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerProductWorkSheetCategoryDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerProductWorkSheetCategoryDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public RworkerProductWorkSheetCategoryDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public RworkerProductWorkSheetCategoryDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }
}
