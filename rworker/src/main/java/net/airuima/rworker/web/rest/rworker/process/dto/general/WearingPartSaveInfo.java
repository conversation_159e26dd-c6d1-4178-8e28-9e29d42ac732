package net.airuima.rworker.web.rest.rworker.process.dto.general;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工序保存对应易损件信息
 * <AUTHOR>
 * @date 2023/4/10
 */
@Schema(description = "工序保存对应易损件信息")
public class WearingPartSaveInfo implements Serializable {

    /**
     * 易损件ID
     */
    @Schema(description = "易损件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 扣减次数
     */
    @Schema(description = "扣减次数")
    private int abatementNumber;

    /**
     * 扣减时长
     */
    @Schema(description = "扣减时长")
    private int abatementTime;

    public Long getId() {
        return id;
    }

    public WearingPartSaveInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public int getAbatementNumber() {
        return abatementNumber;
    }

    public WearingPartSaveInfo setAbatementNumber(int abatementNumber) {
        this.abatementNumber = abatementNumber;
        return this;
    }

    public int getAbatementTime() {
        return abatementTime;
    }

    public WearingPartSaveInfo setAbatementTime(int abatementTime) {
        this.abatementTime = abatementTime;
        return this;
    }
}
