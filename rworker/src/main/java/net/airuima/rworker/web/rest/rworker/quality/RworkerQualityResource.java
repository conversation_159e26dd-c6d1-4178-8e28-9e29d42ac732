package net.airuima.rworker.web.rest.rworker.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistory;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.rworker.quality.dto.*;
import net.airuima.rworker.service.rworker.quality.IQualityService;
import net.airuima.rworker.web.rest.rworker.quality.dto.RworkerCancelTaskDTO;
import net.airuima.rworker.web.rest.rworker.quality.dto.RworkerQualityCacheSaveDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/30
 */
@Tag(name = "RWorker-Web首检-巡检-抽检相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/rworker/qualities")
public class RworkerQualityResource {
    private static final String ERROR = "error";
    @Autowired
    private IQualityService[] qualityServices;

    /**
     * 通过检测工位、投产工单获取可检测的生产工位及对应工序列表
     *
     * @param rworkerCheckWorkCellStepDto 请求参数
     * <AUTHOR>
     * @date 2023/1/31
     */
    @Operation(summary = "通过检测工位、投产工单获取可检测的生产工位及对应工序列表")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/to-check-work-cell-step-info")
    public ResponseEntity<ResponseData<RworkerToCheckWorkCellStepGetDTO>> getToInspectedWorkCellAndStepInfo(@RequestBody RworkerCheckWorkCellStepDTO rworkerCheckWorkCellStepDto) {
        try {
            return ResponseData.ok(qualityServices[0].getToInspectedWorkCellAndStepInfo(rworkerCheckWorkCellStepDto));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过投产工单及工序获取检测规则及检测项目信息
     *
     * @param rworkerCheckItemRequestDto 请求参数
     * <AUTHOR>
     * @date 2023/1/31
     */
    @Operation(summary = "通过投产工单及工序获取检测规则及检测项目信息")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/check-rule-info")
    public ResponseEntity<ResponseData<RworkerCheckRuleInfoGetDTO>> getCheckRuleInfo(@RequestBody RworkerCheckItemRequestDTO rworkerCheckItemRequestDto) {
        try {
            return ResponseData.ok(qualityServices[0].getCheckRuleInfo(rworkerCheckItemRequestDto));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 通过投产工单  获取可检测的工序对应的工位列表
     *
     * @param serialNumber 投产工单
     * <AUTHOR>
     */
    @Operation(summary = "通过投产工单获取可检测的工序对应的工位列表")
    @PreAuthorize("@sc.checkSecurity()")
    @GetMapping("/inspection/work-cell-step/{serialNumber}")
    public ResponseEntity<ResponseData<List<RworkerInspectionWorkCellStepDTO>>> inspectionWorkCellStep(@PathVariable(value = "serialNumber") String serialNumber) {
        try {
            return ResponseData.ok(qualityServices[0].inspectionWorkCellStep(serialNumber));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 通过 投产工单编码+检测类型+工位+工序 获取质检方案
     *
     * @param rworkerProductWorkSheetCategoryDto
     * <AUTHOR>
     * @date 2023/4/26
     */
    @Operation(summary = "获取质检方案")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/qualityInspectionPlan")
    public ResponseEntity<ResponseData<RworkerQualityInspectionPlanDTO>> qualityInspectionPlan(@RequestBody RworkerProductWorkSheetCategoryDTO rworkerProductWorkSheetCategoryDto) {
        try {
            return ResponseData.ok(qualityServices[0].qualityInspectionPlan(rworkerProductWorkSheetCategoryDto));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 保存首检巡检 抽检终检检测记录
     *
     * @param
     * <AUTHOR>
     * @date 2023/4/27
     */
    @Operation(summary = "保存首检巡检 抽检终检检测记录")
    @PreAuthorize("@sc.checkSecurity()")
    @PreventRepeatSubmit
    @PostMapping("/inspection/records")
    public ResponseEntity<ResponseData<Void>> saveFirstInspectionRecord(@Valid @RequestBody RworkerInspectionResultDTO rworkerInspectionResultDto) {
        try {
            qualityServices[0].saveInspectionRecord(rworkerInspectionResultDto);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 验证当前工单，工位 是否需要进行首检及巡检
     *
     * @param rworkerCheckProcessInspectionDtoList
     * @return org.springframework.http.ResponseEntity<java.lang.String>
     * <AUTHOR>
     * @date 2023/1/31
     */
    @Operation(summary = "通过工位、投产工单验证是否需要进行首检及巡检")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/check-process-Inspection")
    public ResponseEntity<ResponseData<Void>> checkProcessInspection(@RequestBody List<RworkerCheckProcessInspectionDTO> rworkerCheckProcessInspectionDtoList) {
        try {
            BaseDTO baseDto = qualityServices[0].checkProcessInspection(rworkerCheckProcessInspectionDtoList);
            if (baseDto.getStatus().equals(Constants.KO)) {
                throw new ResponseException("error.CheckProcessError", baseDto.getMessage());
            }
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * @param rworkerHumitureSaveRequestDTOList 待保存温湿度检测数据参数列表
     * @return BaseResultDTO
     */
    @Operation(summary = "保存温湿度检测数据")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/humiture/records")
    public ResponseEntity<ResponseData<BaseResultDTO>> saveHumiture(@RequestBody List<RworkerHumitureSaveRequestDTO> rworkerHumitureSaveRequestDTOList) {
        return ResponseData.ok(qualityServices[0].saveHumitureRecord(rworkerHumitureSaveRequestDTOList));
    }

    /**
     * 待做的来料检验单
     *
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < net.airuima.rbase.domain.procedure.quality.IqcCheckHistory>> 来料检验列表
     */
    @Operation(summary = "待做的来料检验单")
    @GetMapping("/iqc/todo")
    @PreAuthorize("@sc.checkSecurity()")
    public ResponseEntity<ResponseData<List<IqcCheckHistory>>> todo() {
        try {
            List<IqcCheckHistory> iqcCheckHistoryList = qualityServices[0].getIqcTodo();
            return ResponseData.ok(iqcCheckHistoryList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 放行待检任务
     *
     * @param rworkerCancelTaskDto Rworker质检放行DTO
     */
    @Operation(summary = "放行待检任务")
    @PutMapping("/cancel")
    @PreAuthorize("@sc.checkSecurity()")
    public ResponseEntity<ResponseData<Void>> cancelTask(@RequestBody RworkerCancelTaskDTO rworkerCancelTaskDto) {
        try {
            qualityServices[0].cancelTask(rworkerCancelTaskDto);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 新增或者更新质检缓存
     * @param rworkerQualityCacheSaveDTO 新增或者更新质检缓存参数DTO
     */
    @Operation(summary = "新增或者更新质检缓存")
    @PostMapping("/cache")
    @PreAuthorize("@sc.checkSecurity()")
    public ResponseEntity<ResponseData<Void>> cache(@RequestBody RworkerQualityCacheSaveDTO rworkerQualityCacheSaveDTO){
        try{
            qualityServices[0].saveQualityInspectCache(rworkerQualityCacheSaveDTO);
            return ResponseData.ok();
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

}
