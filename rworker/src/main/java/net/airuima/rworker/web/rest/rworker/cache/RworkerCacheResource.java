package net.airuima.rworker.web.rest.rworker.cache;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rworker.domain.RworkerCache;
import net.airuima.rworker.service.rworker.cache.RworkerCacheService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker缓存Resource
 * <AUTHOR>
 * @date 2023/11/15
 */
@Tag(name = "Rworker缓存Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/rworker-caches")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("RworkerCache")
public class RworkerCacheResource extends ProtectBaseResource<RworkerCache> {

    private final RworkerCacheService rworkerCacheService;

    public RworkerCacheResource(RworkerCacheService rworkerCacheService) {
        this.rworkerCacheService = rworkerCacheService;
        this.mapUri = "/api/rworker-caches";
    }


    /**
     * 通过ID删除缓存
     * @param id ID
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    public ResponseEntity<Void> delete(@PathVariable("id") Long id) {
        rworkerCacheService.deleteCacheById(id);
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, id.toString())).build();
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority,"生产缓存");
    }

}
