package net.airuima.rworker.web.rest.rworker.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 容器请求工序生产参数DTO
 * <AUTHOR>
 * @date 2023/1/31
 */
@Schema(description = "容器请求工序生产参数DTO")
public class RworkerContainerToDoStepRequestDTO implements Serializable {

    /**
     * 员工ID
     */
    @Schema(description = "员工ID", required = true)
    private Long staffId;

    /**
     * 工位id
     */
    @Schema(description = "工位ID", required = true)
    private Long workCellId;

    /**
     * 请求生产的容器编码
     */
    @Schema(description = "请求生产的容器编码", required = true)
    private String requestContainerCode;

    public Long getStaffId() {
        return staffId;
    }

    public RworkerContainerToDoStepRequestDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerContainerToDoStepRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public String getRequestContainerCode() {
        return requestContainerCode;
    }

    public RworkerContainerToDoStepRequestDTO setRequestContainerCode(String requestContainerCode) {
        this.requestContainerCode = requestContainerCode;
        return this;
    }
}
