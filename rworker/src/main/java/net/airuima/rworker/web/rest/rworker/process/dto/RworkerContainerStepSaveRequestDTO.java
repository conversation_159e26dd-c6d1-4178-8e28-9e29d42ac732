package net.airuima.rworker.web.rest.rworker.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.AgeingHistoryInfoDTO;
import net.airuima.rbase.dto.client.BakeHistoryInfoDTO;
import net.airuima.rbase.dto.client.CycleBakeHistoryInfoDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.MaterialSaveInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.UnqualifiedItemSaveInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.WearingPartSaveInfo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/2/2
 */
@Schema(description = "Rworker-Web保存容器工序参数DTO")
public class RworkerContainerStepSaveRequestDTO implements Serializable {

    /**
     * 工位id
     */
    @Schema(description = "工位ID", required = true)
    private Long workCellId;

    /**
     * 投产工单ID
     */
    @Schema(description = "投产工单ID", required = true)
    private Long productWorkSheetId;

    /**
     * 当前操作人id
     */
    @Schema(description = "操作人ID")
    private Long staffId;

    /**
     * 工序Id
     */
    @Schema(description = "工序Id")
    private Long stepId;

    /**
     * 工序容器投产总数
     */
    @Schema(description = "工序容器投产总数")
    private Integer number;

    /**
     * 工序容器合格总数
     */
    @Schema(description = "工序容器合格总数")
    private Integer qualifiedNumber;

    /**
     * 工序容器不合格总数
     */
    @Schema(description = "工序容器不合格总数")
    private Integer unqualifiedNumber;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序开始时间")
    private LocalDateTime startTime;

    /**
     * 工序完成日期
     */
    @Schema(description = "工序完成时间")
    private LocalDateTime endTime;

    /**
     * 设备ID列表
     */
    @Schema(description = "设备ID列表")
    private List<Long> facilityIdList;

    /**
     * 容器不良项目信息汇总列表
     */
    @Schema(description = "容器不良项目信息汇总列表")
    private List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList;

    /**
     * 容器物料信息汇总列表
     */
    @Schema(description = "容器物料信息汇总列表")
    private List<MaterialSaveInfo> materialInfoList;

    /**
     * 新绑定的容器生产信息列表
     */
    @Schema(description = "新绑定的容器生产信息列表")
    private List<BingContainerInfo> bingContainerInfoList;

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerContainerStepSaveRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }


    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerContainerStepSaveRequestDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public RworkerContainerStepSaveRequestDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerContainerStepSaveRequestDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public RworkerContainerStepSaveRequestDTO setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public List<Long> getFacilityIdList() {
        return facilityIdList;
    }

    public RworkerContainerStepSaveRequestDTO setFacilityIdList(List<Long> facilityIdList) {
        this.facilityIdList = facilityIdList;
        return this;
    }

    public List<BingContainerInfo> getBingContainerInfoList() {
        return bingContainerInfoList;
    }

    public RworkerContainerStepSaveRequestDTO setBingContainerInfoList(List<BingContainerInfo> bingContainerInfoList) {
        this.bingContainerInfoList = bingContainerInfoList;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public RworkerContainerStepSaveRequestDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public RworkerContainerStepSaveRequestDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public RworkerContainerStepSaveRequestDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public List<UnqualifiedItemSaveInfo> getUnqualifiedItemInfoList() {
        return unqualifiedItemInfoList;
    }

    public RworkerContainerStepSaveRequestDTO setUnqualifiedItemInfoList(List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList) {
        this.unqualifiedItemInfoList = unqualifiedItemInfoList;
        return this;
    }

    public List<MaterialSaveInfo> getMaterialInfoList() {
        return materialInfoList;
    }

    public RworkerContainerStepSaveRequestDTO setMaterialInfoList(List<MaterialSaveInfo> materialInfoList) {
        this.materialInfoList = materialInfoList;
        return this;
    }

    /**
     * 新绑定的容器生产信息
     */
    @Schema(description = "新绑定的容器生产信息")
    public static class BingContainerInfo implements Serializable{

        /**
         * 新绑定的容器ID
         */
        @Schema(description = "新绑定的容器ID")
        private Long bindContainerId;

        /**
         * 请求的容器Id列表
         */
        @Schema(description = "请求的容器Id列表")
        private List<RequestContainerInfo> requestContainerInfoList;

        /**
         * 工序投产数
         */
        @Schema(description = "工序投产数")
        private Integer number;

        /**
         * 工序合格数量
         */
        @Schema(description = "工序合格数")
        private Integer qualifiedNumber;

        /**
         * 工序不合格数量
         */
        @Schema(description = "工序不合格数")
        private Integer unqualifiedNumber;

        /**
         * 容器生产不良项目信息列表
         */
        @Schema(description = "容器生产不良项目信息列表")
        private List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList;

        /**
         * 容器生产物料信息列表
         */
        @Schema(description = "容器生产物料信息列表")
        private List<MaterialSaveInfo> materialInfoList;

        /**
         * 动态数据信息
         */
        @Schema(description = "动态数据信息")
        private StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto;

        /**
         * 易损件信息
         */
        @Schema(description = "易损件信息集合")
        private List<WearingPartSaveInfo> wearingPartInfoList;

        /**
         * 烘烤信息
         */
        @Schema(description = "烘烤信息集合")
        private List<BakeHistoryInfoDTO> bakeHistoryInfoList;

        /**
         * 温循信息
         */
        @Schema(description = "温循信息集合")
        private List<CycleBakeHistoryInfoDTO> cycleBakeHistoryInfoList;

        /**
         * 老化信息
         */
        @Schema(description = "老化信息集合")
        private List<AgeingHistoryInfoDTO> ageingHistoryInfoList;

        public List<BakeHistoryInfoDTO> getBakeHistoryInfoList() {
            return bakeHistoryInfoList;
        }

        public BingContainerInfo setBakeHistoryInfoList(List<BakeHistoryInfoDTO> bakeHistoryInfoList) {
            this.bakeHistoryInfoList = bakeHistoryInfoList;
            return this;
        }

        public List<CycleBakeHistoryInfoDTO> getCycleBakeHistoryInfoList() {
            return cycleBakeHistoryInfoList;
        }

        public BingContainerInfo setCycleBakeHistoryInfoList(List<CycleBakeHistoryInfoDTO> cycleBakeHistoryInfoList) {
            this.cycleBakeHistoryInfoList = cycleBakeHistoryInfoList;
            return this;
        }

        public List<AgeingHistoryInfoDTO> getAgeingHistoryInfoList() {
            return ageingHistoryInfoList;
        }

        public BingContainerInfo setAgeingHistoryInfoList(List<AgeingHistoryInfoDTO> ageingHistoryInfoList) {
            this.ageingHistoryInfoList = ageingHistoryInfoList;
            return this;
        }

        public List<RequestContainerInfo> getRequestContainerInfoList() {
            return requestContainerInfoList;
        }

        public BingContainerInfo setRequestContainerInfoList(List<RequestContainerInfo> requestContainerInfoList) {
            this.requestContainerInfoList = requestContainerInfoList;
            return this;
        }

        public Long getBindContainerId() {
            return bindContainerId;
        }

        public List<WearingPartSaveInfo> getWearingPartInfoList() {
            return wearingPartInfoList;
        }

        public void setWearingPartInfoList(List<WearingPartSaveInfo> wearingPartInfoList) {
            this.wearingPartInfoList = wearingPartInfoList;
        }

        public BingContainerInfo setBindContainerId(Long bindContainerId) {
            this.bindContainerId = bindContainerId;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public BingContainerInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public BingContainerInfo setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public Integer getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public BingContainerInfo setUnqualifiedNumber(Integer unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public List<UnqualifiedItemSaveInfo> getUnqualifiedItemInfoList() {
            return unqualifiedItemInfoList;
        }

        public BingContainerInfo setUnqualifiedItemInfoList(List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList) {
            this.unqualifiedItemInfoList = unqualifiedItemInfoList;
            return this;
        }

        public List<MaterialSaveInfo> getMaterialInfoList() {
            return materialInfoList;
        }

        public BingContainerInfo setMaterialInfoList(List<MaterialSaveInfo> materialInfoList) {
            this.materialInfoList = materialInfoList;
            return this;
        }

        public StepDynamicDataColumnGetDTO getStepDynamicDataColumnGetDto() {
            return stepDynamicDataColumnGetDto;
        }

        public BingContainerInfo setStepDynamicDataColumnGetDto(StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto) {
            this.stepDynamicDataColumnGetDto = stepDynamicDataColumnGetDto;
            return this;
        }

        /**
         * 请求的容器信息
         */
        @Schema(description = "请求的容器信息")
        public static class RequestContainerInfo implements Serializable{
            /**
             * 请求工序的容器ID
             */
            @Schema(description = "请求工序的容器ID")
            private Long id;

            /**
             * 在新容器中投入的数量
             */
            @Schema(description = "在新容器中投入的数量")
            private Integer number;

            public Long getId() {
                return id;
            }

            public RequestContainerInfo setId(Long id) {
                this.id = id;
                return this;
            }

            public Integer getNumber() {
                return number;
            }

            public RequestContainerInfo setNumber(Integer number) {
                this.number = number;
                return this;
            }
        }
    }
}
