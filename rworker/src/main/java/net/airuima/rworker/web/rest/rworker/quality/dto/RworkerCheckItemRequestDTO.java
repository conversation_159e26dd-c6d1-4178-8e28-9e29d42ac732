package net.airuima.rworker.web.rest.rworker.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/30
 */
@Schema(description = "Rworker-Web请求验证首中末QC检请求参数DTO")
public class RworkerCheckItemRequestDTO implements Serializable {

    /**
     * 类型(0,首检;1,巡检)
     */
    @Schema(description = "类型(0,首检;1,巡检)")
    private Integer category;


    /**
     * 工位ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "工位ID")
    private Long workCellId;

    /**
     * 投产工单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "投产工单ID")
    private Long productWorkSheetId;

    /**
     * 工序ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "工序ID")
    private Long stepId;


    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerCheckItemRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerCheckItemRequestDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public RworkerCheckItemRequestDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerCheckItemRequestDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }
}
