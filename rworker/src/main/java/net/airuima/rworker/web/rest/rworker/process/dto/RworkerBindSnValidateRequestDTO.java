package net.airuima.rworker.web.rest.rworker.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *验证待绑定SN合规性参数DTO
 * <AUTHOR>
 * @date 2023/4/27
 */
@Schema(description = "验证待绑定SN合规性参数DTO")
public class RworkerBindSnValidateRequestDTO {

    /**
     * 投产粒度工单ID
     */
    @Schema(description = "投产粒度工单ID")
    private Long productWorkSheetId;

    /**
     * 投产工序ID
     */
    @Schema(description = "投产工序ID")
    private Long stepId;

    /**
     * 待绑定SN
     */
    @Schema(description = "待绑定SN")
    private String sn;

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerBindSnValidateRequestDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerBindSnValidateRequestDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public RworkerBindSnValidateRequestDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }
}
