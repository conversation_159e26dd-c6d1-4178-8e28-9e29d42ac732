package net.airuima.rworker.web.rest.rworker.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.AgeingHistoryInfoDTO;
import net.airuima.rbase.dto.client.BakeHistoryInfoDTO;
import net.airuima.rbase.dto.client.CycleBakeHistoryInfoDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.MaterialSaveInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.UnqualifiedItemSaveInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.WearingPartSaveInfo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/23
 */
@Schema(description = "Rworker-Web保存批量工序参数DTO")
public class RworkerBatchStepSaveRequestDTO implements Serializable {
    /**
     * 工位id
     */
    @Schema(description = "工位ID", required = true)
    private Long workCellId;

    /**
     * 投产工单id
     */
    @Schema(description = "投产工单id", required = true)
    private Long productWorkSheetId;

    /**
     * 当前操作人id
     */
    @Schema(description = "操作人ID")
    private Long staffId;

    /**
     * 工序Id
     */
    @Schema(description = "工序Id")
    private Long stepId;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序开始时间")
    private LocalDateTime startTime;

    /**
     * 工序完成日期
     */
    @Schema(description = "工序完成时间")
    private LocalDateTime endTime;

    /**
     * 工序投产数
     */
    @Schema(description = "工序投产数")
    private Integer number;

    /**
     * 工序合格数量
     */
    @Schema(description = "工序合格数")
    private Integer qualifiedNumber;

    /**
     * 工序不合格数量
     */
    @Schema(description = "工序不合格数")
    private Integer unqualifiedNumber;

    /**
     * 不良项目信息列表
     */
    @Schema(description = "不良项目信息列表")
    private List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList;

    /**
     * 物料信息列表
     */
    @Schema(description = "物料信息列表")
    private List<MaterialSaveInfo> materialInfoList;

    /**
     * 设备ID列表
     */
    @Schema(description = "设备ID列表")
    private List<Long> facilityIdList;

    /**
     * 动态数据信息
     */
    @Schema(description = "动态数据信息")
    private StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto;

    /**
     * 易损件信息
     */
    @Schema(description = "易损件信息集合")
    private List<WearingPartSaveInfo> wearingPartInfoList;

    /**
     * 烘烤历史信息集合
     */
    @Schema(description = "烘烤历史信息集合")
    private BakeHistoryInfoDTO bakeHistoryInfo;

    /**
     * 温循历史信息集合
     */
    @Schema(description = "温循历史信息集合")
    private CycleBakeHistoryInfoDTO cycleBakeHistoryInfo;

    /**
     * 老化历史信息集合
     */
    @Schema(description = "老化历史信息集合")
    private AgeingHistoryInfoDTO ageingHistoryInfo;


    public BakeHistoryInfoDTO getBakeHistoryInfo() {
        return bakeHistoryInfo;
    }

    public RworkerBatchStepSaveRequestDTO setBakeHistoryInfo(BakeHistoryInfoDTO bakeHistoryInfo) {
        this.bakeHistoryInfo = bakeHistoryInfo;
        return this;
    }

    public CycleBakeHistoryInfoDTO getCycleBakeHistoryInfo() {
        return cycleBakeHistoryInfo;
    }

    public RworkerBatchStepSaveRequestDTO setCycleBakeHistoryInfo(CycleBakeHistoryInfoDTO cycleBakeHistoryInfo) {
        this.cycleBakeHistoryInfo = cycleBakeHistoryInfo;
        return this;
    }

    public AgeingHistoryInfoDTO getAgeingHistoryInfo() {
        return ageingHistoryInfo;
    }

    public RworkerBatchStepSaveRequestDTO setAgeingHistoryInfo(AgeingHistoryInfoDTO ageingHistoryInfo) {
        this.ageingHistoryInfo = ageingHistoryInfo;
        return this;
    }

    public StepDynamicDataColumnGetDTO getStepDynamicDataColumnGetDto() {
        return stepDynamicDataColumnGetDto;
    }

    public RworkerBatchStepSaveRequestDTO setStepDynamicDataColumnGetDto(StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto) {
        this.stepDynamicDataColumnGetDto = stepDynamicDataColumnGetDto;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerBatchStepSaveRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerBatchStepSaveRequestDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public RworkerBatchStepSaveRequestDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerBatchStepSaveRequestDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public RworkerBatchStepSaveRequestDTO setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public RworkerBatchStepSaveRequestDTO setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public RworkerBatchStepSaveRequestDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public RworkerBatchStepSaveRequestDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public RworkerBatchStepSaveRequestDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public List<UnqualifiedItemSaveInfo> getUnqualifiedItemInfoList() {
        return unqualifiedItemInfoList;
    }

    public RworkerBatchStepSaveRequestDTO setUnqualifiedItemInfoList(List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList) {
        this.unqualifiedItemInfoList = unqualifiedItemInfoList;
        return this;
    }

    public List<MaterialSaveInfo> getMaterialInfoList() {
        return materialInfoList;
    }

    public RworkerBatchStepSaveRequestDTO setMaterialInfoList(List<MaterialSaveInfo> materialInfoList) {
        this.materialInfoList = materialInfoList;
        return this;
    }

    public List<Long> getFacilityIdList() {
        return facilityIdList;
    }

    public RworkerBatchStepSaveRequestDTO setFacilityIdList(List<Long> facilityIdList) {
        this.facilityIdList = facilityIdList;
        return this;
    }

    public List<WearingPartSaveInfo> getWearingPartInfoList() {
        return wearingPartInfoList;
    }

    public void setWearingPartInfoList(List<WearingPartSaveInfo> wearingPartInfoList) {
        this.wearingPartInfoList = wearingPartInfoList;
    }
}
