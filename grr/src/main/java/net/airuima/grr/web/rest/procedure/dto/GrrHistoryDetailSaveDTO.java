package net.airuima.grr.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/25
 */
@Schema(description = "保存修改GRR检测项目数据DTO")
public class GrrHistoryDetailSaveDTO {
    @Schema(description = "GRR测试数据明细ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 测试值
     */
    @Schema(description = "测试值")
    private Double number;

    /**
     * GRR
     */
    @Schema(description = "GRR")
    private Double grr;

    /**
     * NDC
     */
    @Schema(description = "NDC")
    private Double ndc;

    public Long getId() {
        return id;
    }

    public GrrHistoryDetailSaveDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public GrrHistoryDetailSaveDTO setNumber(Double number) {
        this.number = number;
        return this;
    }

    public Double getGrr() {
        return grr;
    }

    public GrrHistoryDetailSaveDTO setGrr(Double grr) {
        this.grr = grr;
        return this;
    }

    public Double getNdc() {
        return ndc;
    }

    public GrrHistoryDetailSaveDTO setNdc(Double ndc) {
        this.ndc = ndc;
        return this;
    }
}
