package net.airuima.grr.web.rest.base;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.grr.domain.base.GrrCycleConfig;
import net.airuima.grr.service.base.GrrCycleConfigService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位校准周期配置表Resource
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Tag(name = "台位GRR配置表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/grr-cycle-configs")
@AuthorityRegion("GRR分析")
@FuncInterceptor("GRR")
public class GrrCycleConfigResource extends ProtectBaseResource<GrrCycleConfig> {

    private final GrrCycleConfigService grrCycleConfigService;

    public GrrCycleConfigResource(GrrCycleConfigService grrCycleConfigService) {
        this.grrCycleConfigService = grrCycleConfigService;
        this.mapUri = "/api/grr-cycle-configs";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(entityName, authority, "台位有效期配置");
    }
}
