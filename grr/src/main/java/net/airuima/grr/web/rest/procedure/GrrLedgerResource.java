package net.airuima.grr.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.grr.domain.procedure.GrrLedger;
import net.airuima.grr.service.procedure.GrrLedgerService;
import net.airuima.grr.web.rest.procedure.dto.GrrLedgerGetDTO;
import net.airuima.query.QueryCondition;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.DateTimeUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/25
 */
@Tag(name = "台位GRR台账Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/grr-ledgers")
@AuthorityRegion("GRR分析")
@FuncInterceptor("GRR")
@AuthSkip("ICUD")
public class GrrLedgerResource extends ProtectBaseResource<GrrLedger> {
    private final GrrLedgerService grrLedgerService;
    private final String TIP_DAY_FIELD_NAME = "tipDay";
    private final String EXPIRE_TIME_FILED_NAME = "expireTime";

    public GrrLedgerResource(GrrLedgerService grrLedgerService) {
        this.grrLedgerService = grrLedgerService;
        this.mapUri = "api/grr-ledgers";
    }

    @Override
    public ResponseEntity<List<GrrLedger>> searchQuery(Pageable pageable, @RequestBody List<QueryCondition> qcs, HttpServletRequest request) {
        if(ValidateUtils.isValid(qcs)){
            Optional<QueryCondition>queryConditionOptional = qcs.stream().filter(queryCondition -> queryCondition.getFieldName().equals(TIP_DAY_FIELD_NAME)).findFirst();
            queryConditionOptional.ifPresent(queryCondition -> {
                if(null != queryCondition.getFieldValue() && StringUtils.isNotBlank(String.valueOf(queryCondition.getFieldValue()))){
                    QueryCondition queryCondition1 = new QueryCondition();
                    queryCondition1.setFieldName(EXPIRE_TIME_FILED_NAME);
                    queryCondition1.setFieldValue(DateTimeUtil.localDateTime2String(LocalDateTime.now().plusDays(Long.parseLong(queryCondition.getFieldValue().toString()))));
                    queryCondition1.setOperator("NLTES");
                    queryCondition1.setIgnoreCase(Boolean.FALSE);
                    qcs.add(queryCondition1);
                }
                qcs.remove(queryCondition);
            });
        }
        return super.searchQuery(pageable, qcs, request);
    }

    /**
     * 通过台账ID获取明细数据
     * @param id GRR台账ID
     * @return List<GrrLedgerGetDTO>
     */
    @Operation(summary = "通过台账ID获取检测项GRR及NDC明细记录")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/detail/{id}")
    public ResponseEntity<ResponseData<List<GrrLedgerGetDTO>>> getGrrLedgerDetail(@PathVariable("id") Long id){
        return ResponseData.ok(grrLedgerService.getDetailById(id));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览GRR台账";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建GRR台账";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改GRR台账";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除GRR台账";
        }else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入GRR台账";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出GRR台账";
        }
        return "";
    }
}
