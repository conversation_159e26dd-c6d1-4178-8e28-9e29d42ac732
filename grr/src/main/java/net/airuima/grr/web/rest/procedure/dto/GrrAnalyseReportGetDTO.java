package net.airuima.grr.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.config.DoubleSerializer;
import net.airuima.config.annotation.DoubleSerialize;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/25
 */
@Schema(description = "GRR分析报告数据DTO")
public class GrrAnalyseReportGetDTO {
    /**
     * 台位编码
     */
    @Schema(description = "台位编码")
    private String workCellCode;

    /**
     * 检测项目编码
     */
    @Schema(description = "检测项目编码")
    private String checkItemCode;

    /**
     * 子模块
     */
    @Schema(description = "子模块")
    private String subModule;

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    private LocalDateTime testTime;

    /**
     * GRR
     */
    @Schema(description = "GRR")
    @JsonSerialize(using = DoubleSerializer.class)
    @DoubleSerialize(scale = 3)
    private Double grr;

    /**
     * NDC
     */
    @Schema(description = "NDC")
    @JsonSerialize(using = DoubleSerializer.class)
    @DoubleSerialize(scale = 3)
    private Double ndc;

    /**
     * GRR检测测试数据明细列表
     */
    @Schema(description = "GRR检测测试数据明细列表")
    private List<GrrHistoryDetailInfo> grrHistoryDetailInfoList;

    public String getWorkCellCode() {
        return workCellCode;
    }

    public GrrAnalyseReportGetDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getCheckItemCode() {
        return checkItemCode;
    }

    public GrrAnalyseReportGetDTO setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }

    public String getSubModule() {
        return subModule;
    }

    public GrrAnalyseReportGetDTO setSubModule(String subModule) {
        this.subModule = subModule;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public GrrAnalyseReportGetDTO setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public Double getGrr() {
        return grr;
    }

    public GrrAnalyseReportGetDTO setGrr(Double grr) {
        this.grr = grr;
        return this;
    }

    public Double getNdc() {
        return ndc;
    }

    public GrrAnalyseReportGetDTO setNdc(Double ndc) {
        this.ndc = ndc;
        return this;
    }

    public List<GrrHistoryDetailInfo> getGrrHistoryDetailInfoList() {
        return grrHistoryDetailInfoList;
    }

    public GrrAnalyseReportGetDTO setGrrHistoryDetailInfoList(List<GrrHistoryDetailInfo> grrHistoryDetailInfoList) {
        this.grrHistoryDetailInfoList = grrHistoryDetailInfoList;
        return this;
    }

    @Schema(description = "GRR检测测试数据明细")
    public static class GrrHistoryDetailInfo{
        /**
         * 标准件编码
         */
        @Schema(description = "标准件编码")
        private String sn;

        /**
         * 员工测试数据列表
         */
        @Schema(description = "员工测试数据列表")
        private List<TestDataInfo> testDataInfoList ;

        public String getSn() {
            return sn;
        }

        public GrrHistoryDetailInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }


        public List<TestDataInfo> getTestDataInfoList() {
            return testDataInfoList;
        }

        public GrrHistoryDetailInfo setTestDataInfoList(List<TestDataInfo> testDataInfoList) {
            this.testDataInfoList = testDataInfoList;
            return this;
        }

        @Schema(description = "员工测试数据")
        public static class TestDataInfo{

            /**
             * 员工编号
             */
            @Schema(description = "员工编号")
            private String tester;

            /**
             * 测试次数
             */
            @Schema(description = "测试次数")
            private Integer testTimes;

            /**
             * 测试值
             */
            @Schema(description = "测试值")
            @JsonSerialize(using = DoubleSerializer.class)
            @DoubleSerialize(scale = 3)
            private Double number;

            public String getTester() {
                return tester;
            }

            public TestDataInfo setTester(String tester) {
                this.tester = tester;
                return this;
            }

            public Integer getTestTimes() {
                return testTimes;
            }

            public TestDataInfo setTestTimes(Integer testTimes) {
                this.testTimes = testTimes;
                return this;
            }

            public Double getNumber() {
                return number;
            }

            public TestDataInfo setNumber(Double number) {
                this.number = number;
                return this;
            }
        }


    }
}
