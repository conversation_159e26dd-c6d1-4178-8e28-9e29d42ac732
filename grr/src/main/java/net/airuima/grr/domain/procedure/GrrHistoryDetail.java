package net.airuima.grr.domain.procedure;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位GRR测试历史明细Domain
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Schema(name = "台位GRR测试历史明细(GrrHistoryDetail)", description = "台位GRR测试历史明细")
@Entity
@Table(name = "procedure_grr_history_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class GrrHistoryDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * GRR历史ID
     */
    @NotNull
    @Schema(description = "GRR历史ID", required = true)
    @ManyToOne
    @JoinColumn(name = "history_id", nullable = false)
    private GrrHistory grrHistory;

    /**
     * 测试人
     */
    @NotNull
    @Schema(description = "测试人", required = true)
    @Column(name = "tester", nullable = false)
    private String tester;

    /**
     * SN
     */
    @NotNull
    @Schema(description = "SN", required = true)
    @Column(name = "sn", nullable = false)
    private String sn;

    /**
     * 检测项目编码
     */
    @Schema(description = "检测项目编码")
    @Column(name = "check_item_code")
    private String checkItemCode;

    /**
     * 检测项目单位
     */
    @Schema(description = "检测项目单位")
    @Column(name = "check_item_unit")
    private String checkItemUnit;

    /**
     * 子模块
     */
    @Schema(description = "子模块")
    @Column(name = "sub_module")
    private String subModule;

    /**
     * 测试次数
     */
    @Schema(description = "测试次数")
    @Column(name = "times")
    private int times;

    /**
     * 测试值
     */
    @Schema(description = "测试值")
    @Column(name = "number")
    private double number;

    /**
     * GRR
     */
    @Schema(description = "GRR")
    @Column(name = "grr")
    private double grr;

    /**
     * NDC
     */
    @Schema(description = "NDC")
    @Column(name = "ndc")
    private double ndc;

    public GrrHistoryDetail() {

    }

    public GrrHistoryDetail(String checkItemCode, String subModule) {
        this.checkItemCode = checkItemCode;
        this.subModule = subModule;
    }

    public GrrHistory getGrrHistory() {
        return grrHistory;
    }

    public GrrHistoryDetail setGrrHistory(GrrHistory grrHistory) {
        this.grrHistory = grrHistory;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public GrrHistoryDetail setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public GrrHistoryDetail setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getCheckItemCode() {
        return checkItemCode;
    }

    public GrrHistoryDetail setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }

    public String getCheckItemUnit() {
        return checkItemUnit;
    }

    public GrrHistoryDetail setCheckItemUnit(String checkItemUnit) {
        this.checkItemUnit = checkItemUnit;
        return this;
    }

    public String getSubModule() {
        return subModule;
    }

    public GrrHistoryDetail setSubModule(String subModule) {
        this.subModule = subModule;
        return this;
    }

    public int getTimes() {
        return times;
    }

    public GrrHistoryDetail setTimes(int times) {
        this.times = times;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public GrrHistoryDetail setNumber(double number) {
        this.number = number;
        return this;
    }

    public double getGrr() {
        return grr;
    }

    public GrrHistoryDetail setGrr(double grr) {
        this.grr = grr;
        return this;
    }

    public double getNdc() {
        return ndc;
    }

    public GrrHistoryDetail setNdc(double ndc) {
        this.ndc = ndc;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GrrHistoryDetail grrHistoryDetail = (GrrHistoryDetail) o;
        if (grrHistoryDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), grrHistoryDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
