package net.airuima.grr.repository.base;

import net.airuima.grr.domain.base.GrrCycleConfig;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位校准周期配置表Repository
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Repository
public interface GrrCycleConfigRepository extends LogicDeleteableRepository<GrrCycleConfig>,
        JpaSpecificationExecutor<GrrCycleConfig>, JpaRepository<GrrCycleConfig, Long> {

    /**
     * 通过台位编码获取台位GRR周期
     * @param workCellCode 台位编码
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.grr.GrrCycleConfig> 台位GRR配置
     */
    Optional<GrrCycleConfig> findByWorkCellCodeAndDeleted(String workCellCode, Long deleted);
}
