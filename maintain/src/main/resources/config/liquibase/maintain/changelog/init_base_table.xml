<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <changeSet author="RMPC (generated)" id="1713521050316-18">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="base_maintain_case"/>
            </not>
        </preConditions>
        <createTable remarks="维修分析方案" tableName="base_maintain_case">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="维修分析方案编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="维修分析方案名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="产品谱系维修方案" tableName="base_pedigree_maintain_case">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="client_id" remarks="客户代码id" type="BIGINT"/>
            <column name="maintain_case_id" remarks="维修分析方案id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_flow_id" remarks="工艺路线id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="rework_category" remarks="返修流程（0：返修工艺路线，1：原工艺路线）" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable remarks="维修分析记录表" tableName="procedure_maintain_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="maintain_case_id" type="BIGINT"/>
            <column name="sn_work_status_id" remarks="sn状态Id" type="BIGINT"/>
            <column name="container_detail_id" remarks="容器详情id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="number" remarks="维修数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单Id" type="BIGINT"/>
            <column name="work_sheet_id" remarks="工单Id" type="BIGINT"/>
            <column name="start_date" remarks="维修开始时间" type="timestamp"/>
            <column name="end_date" remarks="维修结束时间" type="timestamp"/>
            <column defaultValueNumeric="-1" name="result" remarks="处理方式：-1:未处理,0：报废，1：返工" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="status" remarks="最新维修状态：0：待分析，1：待维修;2:完成" type="TINYINT(3)"/>
            <column name="ws_rework_id" remarks="在线返修单和正常单关联信息" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="unqualified_item_id" remarks="不良项Id" type="BIGINT"/>
            <column name="step_id" remarks="不良产生工序Id" type="BIGINT"/>
            <column name="reason" remarks="维修原因" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="rework_category" remarks="返修流程(0:返修工艺路线, 1:原工艺路线)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="rework_step_id" remarks="开始返工的工序ID" type="BIGINT"/>
            <column defaultValue="0" name="is_replace_material" remarks="是否替换料(0:否;1:是)" type="BIT(1)"/>
        </createTable>
        <createTable remarks="维修分析历史详情表" tableName="procedure_maintain_history_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="history_id" remarks="维修分析历史id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="staff_id" remarks="操作员工id" type="BIGINT"/>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column name="record_date" remarks="处理时间" type="timestamp"/>
            <column defaultValueNumeric="0" name="stage" remarks="阶段：0：分析，1:维修" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="退补料清单" tableName="procedure_maintain_material_exchange">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="maintain_history_id" remarks="维修历史记录id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="replace_material_id" remarks="替换物料Id" type="BIGINT"/>
            <column name="return_batch" remarks="退物料批次" type="VARCHAR(255)"/>
            <column name="replace_batch" remarks="替换料批次" type="VARCHAR(255)"/>
            <column name="return_material_id" remarks="退料Id" type="BIGINT"/>
            <column defaultValueNumeric="0.0" name="number" remarks="数量" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_maintain_case_unique_index" tableName="base_maintain_case"/>

        <addUniqueConstraint columnNames="pedigree_id, client_id, maintain_case_id, work_flow_id, deleted" constraintName="base_pedigree_maintain_case_unique_index" tableName="base_pedigree_maintain_case"/>

        <createIndex associatedWith="" indexName="base_maintain_case_code_index" tableName="base_maintain_case">
            <column name="code"/>
        </createIndex>

        <createIndex associatedWith="" indexName="base_maintain_case_name_index" tableName="base_maintain_case">
            <column name="name"/>
        </createIndex>

        <createIndex associatedWith="" indexName="base_pedigree_maintain_case_client_index" tableName="base_pedigree_maintain_case">
            <column name="client_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="base_pedigree_maintain_case_index" tableName="base_pedigree_maintain_case">
            <column name="maintain_case_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="base_pedigree_maintain_case_pedigree_index" tableName="base_pedigree_maintain_case">
            <column name="pedigree_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="base_pedigree_work_flow_index" tableName="base_pedigree_maintain_case">
            <column name="work_flow_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_container_detail_id_index" tableName="procedure_maintain_history">
            <column name="container_detail_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_detail_history_index" tableName="procedure_maintain_history_detail">
            <column name="history_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_detail_staff_index" tableName="procedure_maintain_history_detail">
            <column name="staff_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_maintain_case_id_index" tableName="procedure_maintain_history">
            <column name="maintain_case_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_rework_step_id_index" tableName="procedure_maintain_history">
            <column name="rework_step_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_sn_work_status_id_index" tableName="procedure_maintain_history">
            <column name="sn_work_status_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_step_id_index" tableName="procedure_maintain_history">
            <column name="step_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_sub_work_sheet_id_index" tableName="procedure_maintain_history">
            <column name="sub_work_sheet_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_unqualified_item_id_index" tableName="procedure_maintain_history">
            <column name="unqualified_item_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_work_sheet_id_index" tableName="procedure_maintain_history">
            <column name="work_sheet_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_history_ws_rework_index" tableName="procedure_maintain_history">
            <column name="ws_rework_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_material_exchange_maintain_history_id_index" tableName="procedure_maintain_material_exchange">
            <column name="maintain_history_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_material_exchange_replace_batch_index" tableName="procedure_maintain_material_exchange">
            <column name="replace_batch"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_material_exchange_replace_material_id_index" tableName="procedure_maintain_material_exchange">
            <column name="replace_material_id"/>
        </createIndex>

        <createIndex associatedWith="" indexName="procedure_maintain_material_exchange_return_batch_index" tableName="procedure_maintain_material_exchange">
            <column name="return_batch"/>
        </createIndex>
        <createIndex associatedWith="" indexName="procedure_maintain_material_exchange_return_material_id_index" tableName="procedure_maintain_material_exchange">
            <column name="return_material_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
