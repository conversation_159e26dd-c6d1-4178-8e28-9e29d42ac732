package net.airuima.maintain.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.maintain.domain.procedure.MaintainHistory;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/27
 */
@Repository
public interface MaintainHistoryRepository extends LogicDeleteableRepository<MaintainHistory>,
        EntityGraphJpaSpecificationExecutor<MaintainHistory>, EntityGraphJpaRepository<MaintainHistory, Long> {

    /**
     * 根据容器编号以及完成状态获取 维修分析历史
     * @param containerCodes 容器编号列表
     * @param status 完成状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/9
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<MaintainHistory> findByContainerDetailContainerCodeInAndStatusNotAndDeleted(List<String> containerCodes,Integer status,Long deleted);

    /**
     * 根据sn以及完成状态 获取维修分析历史
     * @param snList sn列表
     * @param status 完成状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/9
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<MaintainHistory> findBySnWorkStatusSnInAndStatusNotAndDeleted(List<String> snList,Integer status,Long deleted);




    /**
     * 通过sn工作详情主键id 以及 维修状态不为完成状态 获取维修历史信息
     * @param snWorkStatusId sn工作详情主键id
     * @param status  完成状态 2
     * @param deleted  逻辑删除
     * <AUTHOR>
     * @date  2022/10/14
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findBySnWorkStatusIdAndStatusNotAndDeleted(Long snWorkStatusId,Integer status,Long deleted);

    /**
     * 通过sn 以及 维修状态不为完成状态 获取维修历史信息
     * @param sn sn工作详情主键id
     * @param status  完成状态 2
     * @param deleted  逻辑删除
     * <AUTHOR>
     * @date  2022/10/14
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1BySnWorkStatusSnAndStatusNotAndDeleted(String sn,Integer status,Long deleted);

    /**
     * 通过容器详情主键id 以及 维修状态不为完成状态 获取维修历史信息
     * @param containerDetailId 容器详情主键id
     * @param status 完成状态 2
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/14
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    List<MaintainHistory> findByContainerDetailIdAndStatusNotAndDeleted(Long containerDetailId,Integer status,Long deleted);

    /**
     * 根据sn 以及最新变化状态获取唯一维修分析历史信息
     * @param sn sn
     * @param status 最新生产状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/17
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findBySnWorkStatusSnAndStatusAndDeleted(String sn,Integer status,Long deleted);

    /**
     * 根据工单id以及状态获取唯一维修分析历史信息
     * @param workSheetId  工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1ByWorkSheetIdAndStatusAndDeleted(Long workSheetId,Integer status,Long deleted);

    /**
     * 根据子工单id以及状态获取唯一维修分析历史信息
     * @param subWorkSheetId 子工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1BySubWorkSheetIdAndStatusAndDeleted(Long subWorkSheetId,Integer status,Long deleted);

    /**
     * 根据容器 以及最新变化状态获取唯一维修分析历史信息
     * @param containerCode 容器
     * @param status 最新生产状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/17
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findByContainerDetailContainerCodeAndStatusAndDeleted(String containerCode,Integer status,Long deleted);

    /**
     * 根据sn列表以及状态获取多调整 维修分析历史记录
     * @param snList sn列表
     * @param status 状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/17
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    List<MaintainHistory> findBySnWorkStatusSnInAndStatusAndDeleted(List<String> snList,Integer status,Long deleted);

    /**
     * 根据容器编码列表以及状态获取多调整 维修分析历史记录
     * @param containerCodes 容器列表
     * @param status 状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/17
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    List<MaintainHistory> findByContainerDetailContainerCodeInAndStatusAndDeleted(List<String> containerCodes,Integer status,Long deleted);

    /**
     * 根据工单号列表以及状态获取维修分析历史记录
     * @param sheetSerialNumbers 工单号集合
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    List<MaintainHistory> findByWorkSheetSerialNumberInAndStatusAndDeleted(List<String> sheetSerialNumbers,Integer status,Long deleted);

    /**
     * 根据子工单号列表以及状态获取维修分析历史记录
     * @param subSheetSerialNumbers 子工单号列表
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    List<MaintainHistory> findBySubWorkSheetSerialNumberInAndStatusAndDeleted(List<String> subSheetSerialNumbers,Integer status,Long deleted);


    /**
     * 通过子工单主键ID获取子工单为单位开出的最新的一条维修分析历史记录
     * <AUTHOR>
     * @param subWorkSheetId 子工单主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     * @date 2023/3/14
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1BySubWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(Long subWorkSheetId,Long deleted);

    /**
     *
     *  通过子工单主键ID、状态获取子工单为单位开出的最新的一条维修分析历史记录
     * @param subWorkSheetId 子工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1BySubWorkSheetIdAndStatusNotAndDeleted(Long subWorkSheetId,Integer status,Long deleted);


    /**
     *
     *  通过工单主键ID、状态获取工单为单位开出的最新的一条维修分析历史记录
     * @param workSheetId 工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1ByWorkSheetIdAndStatusNotAndDeleted(Long workSheetId,Integer status,Long deleted);

    /**
     * 通过子工单主键ID集合获取子工单为单位开出的最新的维修分析历史记录
     * @param subWorkSheetIds 子工单主键ID集合
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     * <AUTHOR>
     * @date 2023/3/28
     */
    @DataFilter(isSkip = true)
    @Query(value = "select history1.* from procedure_maintain_history history1 where history1.id in " +
            "(select max(history.id) as id from procedure_maintain_history history where history.sub_work_sheet_id in ?1 " +
            "and history.sn_work_status_id is null and history.container_detail_id is null and history.status=0 and history.deleted =0 group by history.sub_work_sheet_id )", nativeQuery = true)
    List<MaintainHistory> findBySubWorkSheetIdInAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeleted(List<Long> subWorkSheetIds);

    /**
     *
     * 通过返工单主键ID、容器主键ID获取SN生产状态不为空的维修历史记录列表
     * @param reWorkSheetId 返工单主键ID
     * @param containerId 容器主键ID
     * @param deleted  逻辑删除
     * @r* @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
//    @DataFilter(isSkip = true)
//    List<MaintainHistory> findByWsReworkReworkWorkSheetIdAndContainerDetailContainerIdAndSnWorkStatusIsNotNullAndDeleted(Long reWorkSheetId,Long containerId,Long deleted);

    /**
     * 通过工单主键ID获取工单为单位开出的最新的一条维修分析历史记录
     * <AUTHOR>
     * @param workSheetId 工单主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     * @date 2023/3/14
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1ByWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(Long workSheetId,Long deleted);

    /**
     * 据工单主键ID和状态查询第一条 维修分析记录
     * @param workSheetId 工单ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1ByWorkSheetIdAndStatusLessThanAndDeleted(Long workSheetId,int status,Long deleted);


    /**
     * 根据子工单主键ID和状态查询第一条 维修分析记录
     * @param subWorkSheetId 子工单主键ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1BySubWorkSheetIdAndStatusLessThanAndDeleted(Long subWorkSheetId,int status,Long deleted);


    /**
     * 通过工单主键ID集合获取工单为单位开出的最新的维修分析历史记录
     * @param workSheetIds 工单主键ID集合
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     * <AUTHOR>
     * @date 2023/3/28
     */
    @DataFilter(isSkip = true)
    @Query(value = "select history1.* from procedure_maintain_history history1 where history1.id in " +
            "(select max(history.id) as id from procedure_maintain_history history where history.work_sheet_id in ?1 " +
            "and history.sn_work_status_id is null and history.container_detail_id is null and history.status=0 and history.deleted =0 group by history.work_sheet_id )", nativeQuery = true)
    List<MaintainHistory> findByWorkSheetIdInAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeleted(List<Long> workSheetIds);

    /**
     * 通过容器主键ID获取容器为单位开出的最新的一条维修分析历史记录
     * <AUTHOR>
     * @param containerId 容器主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     * @date 2023/3/14
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<MaintainHistory> findTop1ByContainerDetailContainerIdAndDeletedOrderByIdDesc(Long containerId,Long deleted);

    /**
     * 通过容器ID获取容器集合为单位开出的最新的维修分析历史记录
     * @param containerIds 容器主键ID集合
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     * <AUTHOR>
     * @date 2023/3/28
     */
    @DataFilter(isSkip = true)
    @Query(value = "select history1.* from procedure_maintain_history history1 where history1.id in " +
            "(select max(history.id) as id from procedure_maintain_history history,procedure_container_detail conDetail where history.container_detail_id = conDetail.id and conDetail.container_id in ?1 " +
            "and history.status=0 and history.deleted=0 group by conDetail.container_id )", nativeQuery = true)
    List<MaintainHistory> findByContainerDetailContainerIdInAndDeleted(List<Long> containerIds);


    /**
     *
     * 通过ID列表获取维修分析历史列表
     * @param idList 维修分析历史记录主键ID列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    List<MaintainHistory> findByIdInAndDeleted(List<Long> idList,Long deleted);

    /**
     * 通过SN生产状态主键ID获取SN为单位开出的最新的一条维修分析历史记录
     * <AUTHOR>
     * @param snWorkStatusId SN生产状态I主键D
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     * @date 2023/3/14
     */
    @DataFilter(isSkip = true)
    Optional<MaintainHistory> findTop1BySnWorkStatusIdAndDeletedOrderByIdDesc(Long snWorkStatusId,Long deleted);

    /**
     * 通过SN生产状态主键ID集合获取SN为单位开出的最新的维修分析历史记录
     * @param snWorkStatusIds SN生产状态主键ID集合
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     * <AUTHOR>
     * @date 2023/3/28
     */
    @DataFilter(isSkip = true)
    @Query(value = "select history1.* from procedure_maintain_history history1 where history1.id in " +
            "(select max(history.id) as id from procedure_maintain_history history where history.sn_work_status_id in ?1 and history.status=0 and history.deleted=0 group by history.sn_work_status_id )", nativeQuery = true)
    List<MaintainHistory> findBySnWorkStatusIdInInAndDeleted(List<Long> snWorkStatusIds);

    /**
     * 根据子工单工序处理状态获取维修分析历史
     * @param snWorkStatusId 子工单主键id
     * @param stepIds 工序主键id列表
     * @param result 处理状态
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    List<MaintainHistory> findBySubWorkSheetIdAndStepIdInAndResultAndDeleted(Long snWorkStatusId,List<Long> stepIds,int result,Long deleted);

    /**
     * 通过子工单ID、SN生产状态ID、返工单ID、工序ID获取维修分析记录
     * @param subWorkSheetId 子工单ID
     * @param snWorkStatusId SN生产状态ID
     * @param reworkSheetId 返工单ID
     * @param stepId 工序ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
//    MaintainHistory findTop1BySubWorkSheetIdAndSnWorkStatusIdAndWsReworkReworkWorkSheetIdAndStepIdAndDeleted(Long subWorkSheetId,Long snWorkStatusId,Long reworkSheetId,Long stepId,Long deleted);


    /**
     * 通过子工单ID、SN生产状态ID、返工单ID、工序ID获取维修分析记录
     * @param workSheetId 工单ID
     * @param snWorkStatusId SN生产状态ID
     * @param reworkSheetId 返工单ID
     * @param stepId 工序ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
//    MaintainHistory findTop1ByWorkSheetIdAndSnWorkStatusIdAndWsReworkReworkWorkSheetIdAndStepIdAndDeleted(Long workSheetId,Long snWorkStatusId,Long reworkSheetId,Long stepId,Long deleted);

    /**
     * 通过子工单ID获取待维修分析任务个数
     * @param subWorkSheetId 子工单ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return 个数
     */
    long countBySubWorkSheetIdAndStatusLessThanAndDeleted(long subWorkSheetId,int status,long deleted);


    /**
     * 通过工单ID获取待维修分析任务个数
     * @param workSheetId 工单ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return 个数
     */
    long countByWorkSheetIdAndStatusLessThanAndDeleted(long workSheetId,int status,long deleted);

    /**
     * 通过子工单ID和不良产生工序id获取待维修分析任务个数
     * @param subWorkSheetId 子工单ID
     * @param stepId 工序ID
     * @param status 状态
     * @param deleted 逻辑删除
     * @return 个数
     */
    long countBySubWorkSheetIdAndStepIdAndStatusLessThanAndDeleted(long subWorkSheetId,long stepId,int status,long deleted);


    /**
     * 通过工单ID和不良产生工序id获取待维修分析任务个数
     * @param workSheetId 工单ID
     * @param stepId 工序ID
     * @param deleted 逻辑删除
     * @return 个数
     */
    long countByWorkSheetIdAndStepIdAndStatusLessThanAndDeleted(long workSheetId,long stepId,int status,long deleted);
}
