package net.airuima.maintain.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.maintain.domain.procedure.MaintainHistory;
import net.airuima.maintain.domain.procedure.MaintainHistoryDetail;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/12
 */
@Repository
public interface MaintainHistoryDetailRepository extends LogicDeleteableRepository<MaintainHistoryDetail>,
        EntityGraphJpaSpecificationExecutor<MaintainHistoryDetail>, EntityGraphJpaRepository<MaintainHistoryDetail, Long> {

    /**
     * 根据维修分析历史主键id 获取维修历史过程详情数据列表（有序）
     *
     * @param maintainHistoryId 维修历史主键id
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistoryDetail> 维修分析记录详情列表
     * <AUTHOR>
     * @date 2022/10/18
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<MaintainHistoryDetail> findByMaintainHistoryIdAndDeletedOrderByCreatedDateDesc(Long maintainHistoryId, Long deleted);

    @DataFilter(isSkip = true)
    @FetchMethod
    List<MaintainHistoryDetail> findByMaintainHistoryIdAndResultAndDeleted(Long maintainHistoryId, Integer result, Long deleted);


    /**
     * 通过返工单主键ID、容器主键ID获取SN生产状态不为空的维修历史记录列表
     *
     * @param reWorkSheetId 返工单主键ID
     * @param containerId   容器主键ID
     * @param deleted       逻辑删除
     * @r* @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @DataFilter(isSkip = true)
    @Query("""
                    select mm.maintainHistory from MaintainHistoryDetail mm where 
                    mm.wsRework.reworkWorkSheet.id = ?1 
                    and mm.maintainHistory.containerDetail.container.id = ?2
                    and mm.maintainHistory.snWorkStatus is not null 
                    and mm.result = ?3
                    and mm.deleted = ?4
            """)
    List<MaintainHistory> findByWsReworkReworkWorkSheetIdAndContainerDetailContainerIdAndSnWorkStatusIsNotNullAndDeleted(Long reWorkSheetId, Long containerId, Integer result, Long deleted);

    /**
     * 通过子工单ID、SN生产状态ID、返工单ID、工序ID获取维修分析记录
     *
     * @param subWorkSheetId 子工单ID
     * @param snWorkStatusId SN生产状态ID
     * @param reworkSheetId  返工单ID
     * @param stepId         工序ID
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    Optional<MaintainHistoryDetail> findTop1ByMaintainHistorySubWorkSheetIdAndMaintainHistorySnWorkStatusIdAndWsReworkReworkWorkSheetIdAndMaintainHistoryStepIdAndDeleted(Long subWorkSheetId, Long snWorkStatusId, Long reworkSheetId, Long stepId, Long deleted);


    /**
     * 通过子工单ID、SN生产状态ID、返工单ID、工序ID获取维修分析记录
     *
     * @param workSheetId    工单ID
     * @param snWorkStatusId SN生产状态ID
     * @param reworkSheetId  返工单ID
     * @param stepId         工序ID
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    Optional<MaintainHistoryDetail> findTop1ByMaintainHistoryWorkSheetIdAndMaintainHistorySnWorkStatusIdAndWsReworkReworkWorkSheetIdAndMaintainHistoryStepIdAndDeleted(Long workSheetId, Long snWorkStatusId, Long reworkSheetId, Long stepId, Long deleted);


    /**
     * 获取 子工单对应 工序 已生成返工单的记录
     *
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @param deleted        逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    List<MaintainHistoryDetail> findByMaintainHistorySubWorkSheetIdAndMaintainHistoryStepIdAndDeletedAndWsReworkIsNotNull(Long subWorkSheetId, Long stepId, Long deleted);


    /**
     * 获取 工单对应 工序 已生成返工单的记录
     *
     * @param workSheetId 工单id
     * @param stepId      工序id
     * @param deleted     逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    List<MaintainHistoryDetail> findByMaintainHistoryWorkSheetIdAndMaintainHistoryStepIdAndDeletedAndWsReworkIsNotNull(Long workSheetId, Long stepId, Long deleted);


    /**
     * 获取容器详情id获取已生成的返工单记录
     *
     * @param containDetailId 容器详情id
     * @param deleted         逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    List<MaintainHistoryDetail> findByMaintainHistoryContainerDetailIdAndDeletedAndWsReworkIsNotNull(Long containDetailId, Long deleted);


    /**
     * 获取 子工单对应 工序 sn 已生成返工单的记录
     *
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @param sn             sn
     * @param deleted        逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    @Query("""
                    select md from MaintainHistoryDetail md where md.maintainHistory.snWorkStatus.sn = ?3 
                    and md.maintainHistory.subWorkSheet.id = ?1 
                    and md.maintainHistory.step.id = ?2
                    and md.deleted = ?4 
                    and md.wsRework is not null 
            """)
    List<MaintainHistoryDetail> findBySubWorkSheetIdAndStepIdAndSnAndDeletedAndWsReworkIsNotNull(Long subWorkSheetId, Long stepId, String sn, Long deleted);

    /**
     * 获取 工单对应 工序 sn 已生成返工单的记录
     *
     * @param workSheetId 工单id
     * @param stepId      工序id
     * @param sn          sn
     * @param deleted     逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    @Query("""
                    select md from MaintainHistoryDetail md where md.maintainHistory.snWorkStatus.sn = ?3 
                    and md.maintainHistory.workSheet.id = ?1 
                    and md.maintainHistory.step.id = ?2
                    and md.deleted = ?4 
                    and md.wsRework is not null 
            """)
    List<MaintainHistoryDetail> findByWorkSheetIdAndStepIdAndSnAndDeletedAndWsReworkIsNotNull(Long workSheetId, Long stepId, String sn, Long deleted);


    /**
     * 获取容器id获取维修分析详情
     *
     * @param containerId 容器id
     * @param deleted         逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    List<MaintainHistoryDetail> findByMaintainHistoryContainerDetailContainerIdAndDeleted(Long containerId, Long deleted);

    /**
     * 获取sn状态id获取维修分析详情
     *
     * @param snWorkStatusId sn状态id
     * @param deleted         逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    List<MaintainHistoryDetail> findByMaintainHistorySnWorkStatusIdAndDeleted(Long snWorkStatusId, Long deleted);
}
