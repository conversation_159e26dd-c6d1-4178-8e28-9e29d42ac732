package net.airuima.maintain.service.procedure.maintain;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.maintain.domain.base.MaintainCase;
import net.airuima.maintain.domain.procedure.MaintainHistory;
import net.airuima.maintain.repository.base.MaintainCaseRepository;
import net.airuima.maintain.service.procedure.MaintainService;
import net.airuima.maintain.web.rest.procedure.dto.MaintainAnalyseInfoDTO;
import net.airuima.maintain.web.rest.procedure.dto.SaveMaterialAnalyseDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SingleMaintainAnalyseServiceImpl implements MaintainService {

    @Autowired
    private PClientMaintainService clientMaintainService;
    @Autowired
    private MaintainCaseRepository maintainCaseRepository;

    /**
     * 获取待维修信息阶段信息
     * @param maintainAnalyseInfoDto 维修分析信息
     * <AUTHOR>
     * @date  2022/10/14
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainAnalyseInfoDTO 维修分析信息
     */
    @Override
    public MaintainAnalyseInfoDTO getReworkInfo(MaintainAnalyseInfoDTO maintainAnalyseInfoDto) {
        if (Constants.KO.equals(maintainAnalyseInfoDto.getStatus())){
            return maintainAnalyseInfoDto;
        }
        //获取维修方案
        List<MaintainAnalyseInfoDTO.MaintainCaseInfo> maintainCases =
                clientMaintainService.getMaintainCase(maintainAnalyseInfoDto.getSubWorkSheet());

        if (!ValidateUtils.isValid(maintainCases)){
            return new MaintainAnalyseInfoDTO(new BaseClientDTO(Constants.KO,"未配置维修方案"));
        }
        maintainAnalyseInfoDto.setMaintainCaseInfos(maintainCases).setStatus(Constants.OK);
        //获取领料替换料信息 (可能没有领料信息)
        maintainAnalyseInfoDto.setMaterialInfo(clientMaintainService.getMaterialInfo(maintainAnalyseInfoDto.getSubWorkSheet()));
        //获取对应的返修类型 -》 若工艺路线返修需Rworker 自行调用其他接口，工序返修则当前返回
        maintainAnalyseInfoDto.setWorkFlowStepType(Constants.INT_ZERO);
        return maintainAnalyseInfoDto;
    }

    /**
     * 保存维修阶段信息
     * @param saveMaterialAnalyseDtoList 维修分析记录信息列表
     * <AUTHOR>
     * @date  2022/10/14
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO  结果信息
     */
    @Override
    public BaseClientDTO saveReworkInfo(List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList) {
        //验证当前snOr容器是否处理待分析
        BaseClientDTO baseClientDto = clientMaintainService.validateSaveMaterialAnalyse(saveMaterialAnalyseDtoList, Constants.FALSE);
        if (Constants.KO.equals(baseClientDto.getStatus())){
            return baseClientDto;
        }
        //验证填入的维修方案(获取未报废的记录信息)
        List<MaintainCase> maintainCaseList = clientMaintainService.validateMaintainCase(saveMaterialAnalyseDtoList, baseClientDto);
        if (Constants.KO.equals(baseClientDto.getStatus())){
            return baseClientDto;
        }
        //验证维修分析选着的工艺路线或者工序是否合法
         baseClientDto = clientMaintainService.validateMaintainWorkFlowOrStep(saveMaterialAnalyseDtoList);
        if (Constants.KO.equals(baseClientDto.getStatus())){
            return baseClientDto;
        }
        //保存维修历史信息
        List<MaintainHistory> maintainHistoryList = clientMaintainService.saveMaintainHistory(saveMaterialAnalyseDtoList, maintainCaseList, Constants.INT_TWO, Constants.INT_ONE);
        //过滤掉报废的维修历史信息
        maintainHistoryList = maintainHistoryList.stream().filter(maintainHistory -> maintainHistory.getResult() == Constants.INT_ONE).collect(Collectors.toList());
        //保存在线返修单
        if (ValidateUtils.isValid(maintainHistoryList)){
            baseClientDto = clientMaintainService.saveMaintainReWork(saveMaterialAnalyseDtoList, maintainHistoryList, saveMaterialAnalyseDtoList.get(Constants.INT_ZERO).getMaintainType());
        }
        //返回维修分析生成的在线返修单号
        if (Constants.OK.equals(baseClientDto.getStatus())){
            baseClientDto.setMessage(clientMaintainService.returnReWorkSerialNumber(maintainHistoryList));
        }
        return baseClientDto;
    }
}
