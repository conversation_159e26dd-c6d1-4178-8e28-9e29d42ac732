package net.airuima.maintain.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.maintain.domain.procedure.MaintainHistoryDetail;
import net.airuima.maintain.repository.procedure.MaintainHistoryDetailRepository;
import net.airuima.maintain.web.rest.procedure.dto.MaintainHistoryDetailDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;

import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MaintainHistoryDetailService extends CommonJpaService<MaintainHistoryDetail> {

    private final String MAINTAIN_HISTORY_DETAIL_ENTITY_GRAPH = "maintainHistoryDetailEntityGraph";
    private final MaintainHistoryDetailRepository maintainHistoryDetailRepository;

    public MaintainHistoryDetailService(MaintainHistoryDetailRepository maintainHistoryDetailRepository) {
        this.maintainHistoryDetailRepository = maintainHistoryDetailRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<MaintainHistoryDetail> find(Specification<MaintainHistoryDetail> spec, Pageable pageable) {
        return maintainHistoryDetailRepository.findAll(spec,pageable,new NamedEntityGraph(MAINTAIN_HISTORY_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<MaintainHistoryDetail> find(Specification<MaintainHistoryDetail> spec) {
        return maintainHistoryDetailRepository.findAll(spec,new NamedEntityGraph(MAINTAIN_HISTORY_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<MaintainHistoryDetail> findAll(Pageable pageable) {
        return maintainHistoryDetailRepository.findAll(pageable,new NamedEntityGraph(MAINTAIN_HISTORY_DETAIL_ENTITY_GRAPH));
    }

    /**
     * 通过维修记录id 获取维修记录详情列表
      * @param maintainHistoryId 维修记录id
     * <AUTHOR>
     * @date  2022/10/18
     */
    @Transactional(readOnly = true)
    public List<MaintainHistoryDetailDTO> findMaintainHistoryDetails(Long maintainHistoryId) {
        List<MaintainHistoryDetail> maintainHistoryDetailList = maintainHistoryDetailRepository.findByMaintainHistoryIdAndDeletedOrderByCreatedDateDesc(maintainHistoryId, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(maintainHistoryDetailList)){
            return maintainHistoryDetailList.stream().map(MaintainHistoryDetailDTO::new).sorted(Comparator.comparing(MaintainHistoryDetailDTO::getResult)).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public MaintainHistoryDetail saveInstance(MaintainHistoryDetail maintainHistoryDetail){
        return this.save(maintainHistoryDetail);
    }


    public List<MaintainHistoryDetail> batchSaveInstance(List<MaintainHistoryDetail> maintainHistoryDetailList){
        return this.save(maintainHistoryDetailList);
    }
}
