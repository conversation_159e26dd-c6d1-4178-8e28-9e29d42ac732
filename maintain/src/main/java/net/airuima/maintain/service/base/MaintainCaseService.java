package net.airuima.maintain.service.base;

import net.airuima.maintain.domain.base.MaintainCase;
import net.airuima.maintain.repository.base.MaintainCaseRepository;
import net.airuima.rbase.constant.Constants;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MaintainCaseService extends CommonJpaService<MaintainCase> {

    private MaintainCaseRepository maintainCaseRepository;

    public MaintainCaseService(MaintainCaseRepository maintainCaseRepository) {
        this.maintainCaseRepository = maintainCaseRepository;
    }


    @Override
    @Transactional(readOnly = true)
    public Page<MaintainCase> find(Specification<MaintainCase> spec, Pageable pageable) {
        return maintainCaseRepository.findAll(spec,pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MaintainCase> find(Specification<MaintainCase> spec) {
        return maintainCaseRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MaintainCase> findAll(Pageable pageable) {
        return maintainCaseRepository.findAll(pageable);
    }

    /**
     * 根据（名称/编码）和 是否启用获取 维分析方案
     * @param text 名称/编码
     * @param isEnable 是否启用
     * <AUTHOR>
     * @date  2022/10/18
     */
    public List<MaintainCase> findByCodeOrName(String text, Integer size, Boolean isEnable) {
        Page<MaintainCase> maintainCasePage = maintainCaseRepository.findByNameOrCode(text, PageRequest.of(Constants.INT_ZERO, size));
        List<MaintainCase> maintainCases = Optional.ofNullable(maintainCasePage).map(Slice::getContent).orElse(null);
        if (maintainCases != null && !maintainCases.isEmpty() ){
            return null != isEnable ? maintainCases.stream().filter(maintainCase -> isEnable == maintainCase.getIsEnable()).collect(Collectors.toList()) : maintainCases;
        }
        return maintainCases;
    }
}
