package net.airuima.maintain.service.procedure.maintain;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.maintain.domain.base.MaintainCase;
import net.airuima.maintain.domain.procedure.MaintainHistory;
import net.airuima.maintain.domain.procedure.MaintainHistoryDetail;
import net.airuima.maintain.repository.procedure.MaintainHistoryDetailRepository;
import net.airuima.maintain.repository.procedure.MaintainHistoryRepository;
import net.airuima.maintain.service.procedure.MaintainService;
import net.airuima.maintain.web.rest.procedure.dto.MaintainAnalyseInfoDTO;
import net.airuima.maintain.web.rest.procedure.dto.SaveMaterialAnalyseDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;

import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;

import net.airuima.rbase.util.ValidateUtils;

import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/12
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class MaintainAnalyseServiceImpl implements MaintainService {
    private static final String ERR_MSG = ")不处于待维修状态";
    private final MaintainHistoryRepository maintainHistoryRepository;


    private final MaintainHistoryDetailRepository maintainHistoryDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;

    @Autowired
    private PClientMaintainService clientMaintainService;
    public MaintainAnalyseServiceImpl(MaintainHistoryRepository maintainHistoryRepository, MaintainHistoryDetailRepository maintainHistoryDetailRepository) {
        this.maintainHistoryRepository = maintainHistoryRepository;
        this.maintainHistoryDetailRepository = maintainHistoryDetailRepository;
    }


    /**
     * 获取待分析阶段信息
     *
      * @param maintainAnalyseInfoDto
     * <AUTHOR>
     * @date  2022/10/14
     * @return MaintainAnalyseInfoDTO
     */
    @Override
    public MaintainAnalyseInfoDTO getAnalysisInfo(MaintainAnalyseInfoDTO maintainAnalyseInfoDto) {
        if (Constants.KO.equals(maintainAnalyseInfoDto.getStatus())){
            return maintainAnalyseInfoDto;
        }
        //获取维修方案
        List<MaintainAnalyseInfoDTO.MaintainCaseInfo> maintainCases =
                clientMaintainService.getMaintainCase(maintainAnalyseInfoDto.getSubWorkSheet());

        if (!ValidateUtils.isValid(maintainCases)){
            return new MaintainAnalyseInfoDTO(new BaseClientDTO(Constants.KO,"未配置维修方案"));
        }
        maintainAnalyseInfoDto.setMaintainCaseInfos(maintainCases);
        //获取领料替换料信息 (可能没有领料信息)
        List<MaintainAnalyseInfoDTO.MaterialInfo> materialInfos = clientMaintainService.getMaterialInfo(maintainAnalyseInfoDto.getSubWorkSheet());
        maintainAnalyseInfoDto.setMaterialInfo(materialInfos).setStatus(Constants.OK);
        return maintainAnalyseInfoDto;
    }

    /**
     * 获取待维修信息阶段信息
     * @param maintainAnalyseInfoDto 维修分析信息
     * <AUTHOR>
     * @date  2022/10/14
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainAnalyseInfoDTO 维修分析信息
     */
    @Override
    public MaintainAnalyseInfoDTO getReworkInfo(MaintainAnalyseInfoDTO maintainAnalyseInfoDto) {
        if (Constants.KO.equals(maintainAnalyseInfoDto.getStatus())){
            return maintainAnalyseInfoDto;
        }
        //验证当前维修记录是否为待维修
        if (maintainAnalyseInfoDto.getMaintainType() == Constants.INT_ZERO){
        //sn
            Optional<MaintainHistory> maintainHistory = maintainHistoryRepository.findBySnWorkStatusSnAndStatusAndDeleted(maintainAnalyseInfoDto.getSn(), Constants.INT_ONE, Constants.LONG_ZERO);
            if (!maintainHistory.isPresent()){
                return new MaintainAnalyseInfoDTO(new BaseClientDTO(Constants.KO,"SN("+maintainAnalyseInfoDto.getSn()+")不处于待维修"));
            }
        }
        if (maintainAnalyseInfoDto.getMaintainType() == Constants.INT_ONE){
        //容器
            Optional<MaintainHistory> maintainHistory = maintainHistoryRepository.findByContainerDetailContainerCodeAndStatusAndDeleted(maintainAnalyseInfoDto.getContainerCode(), Constants.INT_ONE, Constants.LONG_ZERO);
            if (!maintainHistory.isPresent()){
                return new MaintainAnalyseInfoDTO(new BaseClientDTO(Constants.KO,"容器("+maintainAnalyseInfoDto.getContainerCode()+")不处于待维修"));
            }
        }
        //获取对应的返修工艺路线或者工序
         return clientMaintainService.getPedigreeMaintainWorkFlowStep(maintainAnalyseInfoDto);
    }

    /**
     * 保存分析阶段信息
     * @param saveMaterialAnalyseDtoList 维修分析记录信息列表
     * <AUTHOR>
     * @date  2022/10/14
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO  结果信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseClientDTO saveAnalysisInfo(List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList) {

        //验证当前snOr容器是否处理待分析
        BaseClientDTO baseClientDto = clientMaintainService.validateSaveMaterialAnalyse(saveMaterialAnalyseDtoList, Constants.FALSE);
        if (Constants.KO.equals(baseClientDto.getStatus())){
            return baseClientDto;
        }
        //验证填入的维修方案(获取未报废的记录信息)
        List<MaintainCase> maintainCaseList = clientMaintainService.validateMaintainCase(saveMaterialAnalyseDtoList, baseClientDto);
        if (Constants.KO.equals(baseClientDto.getStatus())){
            return baseClientDto;
        }
        //保存维修历史，(未报废)更新维修记录为待维修，历史详情记录为分析
        clientMaintainService.saveMaintainHistory(saveMaterialAnalyseDtoList,maintainCaseList,Constants.INT_ONE,Constants.INT_ZERO);
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 保存维修阶段信息
     * @param saveMaterialAnalyseDtoList 维修分析记录信息列表
     * <AUTHOR>
     * @date  2022/10/14
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO  结果信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseClientDTO saveReworkInfo(List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList) {
        //验证当前snOr容器是否处理待分析
        BaseClientDTO baseClientDto = clientMaintainService.validateSaveMaterialAnalyse(saveMaterialAnalyseDtoList, Constants.TRUE);
        if (Constants.KO.equals(baseClientDto.getStatus())){
            return baseClientDto;
        }
        List<String> snList = saveMaterialAnalyseDtoList.stream().filter(saveMaterialAnalyseDto -> saveMaterialAnalyseDto.getMaintainType() == Constants.INT_ZERO && ValidateUtils.isValid(saveMaterialAnalyseDto.getSn()))
                .map(SaveMaterialAnalyseDTO::getSn).collect(Collectors.toList());
        if (ValidateUtils.isValid(snList)){
            List<MaintainHistory> maintainHistoryList = maintainHistoryRepository.findBySnWorkStatusSnInAndStatusAndDeleted(snList, Constants.INT_ONE, Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(maintainHistoryList) && snList.size() != maintainHistoryList.size()){
                if (ValidateUtils.isValid(maintainHistoryList)){
                    String unMaintainHistoryList = snList.stream().filter(sn -> !maintainHistoryList.stream().map(maintainHistory -> maintainHistory.getSnWorkStatus().getSn()).anyMatch(snStatus -> snStatus.equals(sn)))
                            .collect(Collectors.joining(Constants.STR_COMMA));
                    return new BaseClientDTO(Constants.KO,"SN("+unMaintainHistoryList+ERR_MSG);
                }else {
                    return new BaseClientDTO(Constants.KO,"SN("+snList.stream().collect(Collectors.joining(Constants.STR_COMMA))+ERR_MSG);
                }
            }
            //生成在线返修单
            baseClientDto = clientMaintainService.saveMaintainReWork(saveMaterialAnalyseDtoList,maintainHistoryList,Constants.INT_ZERO);
            if (Constants.KO.equals(baseClientDto.getStatus())){
                return baseClientDto;
            }else {
                //返回维修分析生成的在线返修单号
                baseClientDto.setMessage(clientMaintainService.returnReWorkSerialNumber(maintainHistoryList));
            }
            //修改对应维修历史详情记录
            this.saveMaintainHistoryDetails(saveMaterialAnalyseDtoList,maintainHistoryList,Constants.INT_ONE);
        }
        List<String> containerCodes = saveMaterialAnalyseDtoList.stream().filter(saveMaterialAnalyseDto -> ValidateUtils.isValid(saveMaterialAnalyseDto.getContainerCode()))
                .map(SaveMaterialAnalyseDTO::getContainerCode).collect(Collectors.toList());

        if (ValidateUtils.isValid(containerCodes)){
            List<MaintainHistory> maintainHistoryList = maintainHistoryRepository.findByContainerDetailContainerCodeInAndStatusAndDeleted(containerCodes, Constants.INT_ONE, Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(maintainHistoryList) && containerCodes.size() != maintainHistoryList.size()){
                if (ValidateUtils.isValid(maintainHistoryList)){
                    String unMaintainHistoryList = containerCodes.stream().filter(containerCode -> !maintainHistoryList.stream().map(maintainHistory -> maintainHistory.getContainerDetail().getContainerCode()).anyMatch(code -> code.equals(containerCode)))
                            .collect(Collectors.joining(Constants.STR_COMMA));
                    return new BaseClientDTO(Constants.KO,"容器("+unMaintainHistoryList+ERR_MSG);
                }else {
                    return new BaseClientDTO(Constants.KO,"容器("+containerCodes.stream().collect(Collectors.joining(Constants.STR_COMMA))+ERR_MSG);
                }
            }
            //生成在线返修单
            baseClientDto = clientMaintainService.saveMaintainReWork(saveMaterialAnalyseDtoList,maintainHistoryList,Constants.INT_ONE);
            if (Constants.KO.equals(baseClientDto.getStatus())){
                return baseClientDto;
            }else {
                //返回维修分析生成的在线返修单号
                baseClientDto.setMessage(clientMaintainService.returnReWorkSerialNumber(maintainHistoryList));
            }
            //修改对应维修历史详情记录
            this.saveMaintainHistoryDetails(saveMaterialAnalyseDtoList, maintainHistoryList, Constants.INT_ONE);
            //维修完成后调整容器详情状态正常，以及解绑
            maintainHistoryList.forEach(maintainHistory -> {
                ContainerDetail containerDetail = maintainHistory.getContainerDetail();
                containerDetail.setMaintainStatus(Constants.INT_ZERO).setStatus(Constants.INT_ZERO);
                containerDetailRepository.save(containerDetail);
            });
        }
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 保存维修分析详情
     * @param saveMaterialAnalyseDtoList 维修分析 信息
     * @param maintainHistoryList 维修分析历史
     * @param stage 保存当前详情状态
     * <AUTHOR>
     * @date  2022/10/17
     * @return void
     */
    public void saveMaintainHistoryDetails(List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList,List<MaintainHistory> maintainHistoryList,int stage){
        //修改对应维修历史记录
        List<MaintainHistoryDetail> maintainHistoryDetails = Lists.newArrayList();
        maintainHistoryList.forEach(maintainHistory -> {
            Optional<SaveMaterialAnalyseDTO> analyseDTOOptional = saveMaterialAnalyseDtoList.stream().filter(saveMaterialAnalyseDto -> maintainHistory.getSnWorkStatus() != null).filter(saveMaterialAnalyseDto -> maintainHistory.getSnWorkStatus().getSn().equals(saveMaterialAnalyseDto.getSn()))
                    .findFirst();
            if (!analyseDTOOptional.isPresent()){
                 analyseDTOOptional = saveMaterialAnalyseDtoList.stream().filter(saveMaterialAnalyseDto -> maintainHistory.getContainerDetail() != null).filter(saveMaterialAnalyseDto -> maintainHistory.getContainerDetail().getContainerCode().equals(saveMaterialAnalyseDto.getContainerCode()))
                        .findFirst();
            }
            if (analyseDTOOptional.isPresent()){
                SaveMaterialAnalyseDTO analyseDto = analyseDTOOptional.get();
                MaintainHistoryDetail maintainHistoryDetail = new MaintainHistoryDetail();
                maintainHistoryDetail.setMaintainHistory(maintainHistory).setStaffId(analyseDto.getStaffId())
                        .setRecordDate(analyseDto.getRecordDate()).setStage(stage).setNote(analyseDto.getReason());
                maintainHistoryDetails.add(maintainHistoryDetail);
            }
        });
        maintainHistoryDetailRepository.saveAll(maintainHistoryDetails);
    }

    /**
     * 工单批次回退删除维修分析记录
     *
     * @param batchWorkDetail 批量工序生产详情
     * <AUTHOR>
     * @date 2023/6/14
     */
    @Override
    public void rollBackBatch(BatchWorkDetail batchWorkDetail) {
        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = subWorkSheet != null ? subWorkSheet.getWorkSheet() : batchWorkDetail.getWorkSheet();
        List<MaintainHistory> maintainHistoryList = subWorkSheet != null ?
                maintainHistoryRepository.findBySubWorkSheetIdAndStepIdInAndResultAndDeleted(subWorkSheet.getId(), Collections.singletonList(batchWorkDetail.getStep().getId()), Constants.NEGATIVE_ONE, Constants.LONG_ZERO) :
                maintainHistoryRepository.findBySubWorkSheetIdAndStepIdInAndResultAndDeleted(workSheet.getId(), Collections.singletonList(batchWorkDetail.getStep().getId()), Constants.NEGATIVE_ONE, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(maintainHistoryList)) {
            maintainHistoryRepository.logicDelete(maintainHistoryList);
        }
    }

    /**
     * 容器回退删除维修分析记录
     *
     * @param containerDetail 容器生产详情
     * <AUTHOR>
     * @date 2023/6/14
     */
    @Override
    public void rollBackContainer(ContainerDetail containerDetail) {
        List<MaintainHistory> maintainHistoryList = maintainHistoryRepository.findByContainerDetailIdAndStatusNotAndDeleted(containerDetail.getId(), Constants.INT_TWO, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(maintainHistoryList)) {
            maintainHistoryRepository.logicDelete(maintainHistoryList);
        }
    }

    /**
     * 单支回退删除维修分析记录
     *
     * @param snWorkDetail sn生产详情
     * @return void
     * <AUTHOR>
     * @date 2023/6/14
     */
    @Override
    public void rollBackSn(SnWorkDetail snWorkDetail) {
        Optional<MaintainHistory> maintainHistoryOptional = maintainHistoryRepository.findTop1BySnWorkStatusSnAndStatusNotAndDeleted(snWorkDetail.getSn(), Constants.INT_TWO, Constants.LONG_ZERO);
        maintainHistoryOptional.ifPresent(maintainHistory -> {
            maintainHistoryRepository.logicDelete(maintainHistory);
        });
    }

    /**
     * 开返工单验证是否存在维修分析，存在则提示先进行维修分析
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepIds        工序主键ID列表
     */
    @Override
    public void validReWorkMaintain(Long subWorkSheetId, List<Long> stepIds) {
        List<MaintainHistory> maintainHistories = maintainHistoryRepository.findBySubWorkSheetIdAndStepIdInAndResultAndDeleted(subWorkSheetId, stepIds, Constants.NEGATIVE_ONE, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(maintainHistories)) {
            throw new ResponseException("error.maintainAnalysisTheWorkOrderFirst", "工单请先进行维修分析处理");
        }
    }
}
