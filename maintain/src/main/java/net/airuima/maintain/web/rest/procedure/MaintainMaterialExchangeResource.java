package net.airuima.maintain.web.rest.procedure;


import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.maintain.domain.procedure.MaintainMaterialExchange;
import net.airuima.maintain.service.procedure.MaintainMaterialExchangeService;
import net.airuima.rbase.constant.Constants;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 退补料清单Resource
 * <AUTHOR>
 * @date 2022/9/27
 */
@RestController
@AppKey("RmesService")
@RequestMapping("/api/maintain-material-exchanges")
@AuthorityRegion("维修分析")
@FuncInterceptor("RepaireAnalysis")
@AuthSkip("ICUD")
public class MaintainMaterialExchangeResource extends ProtectBaseResource<MaintainMaterialExchange> {

    private MaintainMaterialExchangeService maintainMaterialExchangeService;

    public MaintainMaterialExchangeResource(MaintainMaterialExchangeService maintainMaterialExchangeService) {
        this.maintainMaterialExchangeService = maintainMaterialExchangeService;
        this.mapUri = "/api/maintain-material-exchanges";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览退补料清单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建退补料清单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改退补料清单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除退补料清单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入退补料清单";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出退补料清单";
        }
        return "";
    }
}
