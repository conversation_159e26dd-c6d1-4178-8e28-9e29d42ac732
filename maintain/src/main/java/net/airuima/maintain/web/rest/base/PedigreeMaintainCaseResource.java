package net.airuima.maintain.web.rest.base;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportParamDTO;
import net.airuima.maintain.domain.base.PedigreeMaintainCase;
import net.airuima.maintain.service.base.PedigreeMaintainCaseService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 产品谱系维修分析Resource
 *
 * <AUTHOR>
 * @date 2022/9/27
 */
@Tag(name = "产品谱系维修分析Resource")
@RestController
@RequestMapping("/api/pedigree-maintain-cases")
@AuthorityRegion("维修分析")
@FuncInterceptor("RepaireAnalysis")
public class PedigreeMaintainCaseResource extends ProtectBaseResource<PedigreeMaintainCase> {

    private PedigreeMaintainCaseService pedigreeMaintainCaseService;

    public PedigreeMaintainCaseResource(PedigreeMaintainCaseService pedigreeMaintainCaseService) {
        this.pedigreeMaintainCaseService = pedigreeMaintainCaseService;
        this.mapUri = "/api/pedigree-maintain-cases";
    }

    /**
     * 新增产品谱系维修分析
     *
     * @param entity 产品谱系维修分析
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.maintiancase.PedigreeMaintainCase>
     * <AUTHOR>
     * @date 2023/3/29
     */
    @Operation(summary = "新增产品谱系维修分析")
    @Override
    @PostMapping
    @PreventRepeatSubmit
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<PedigreeMaintainCase> create(@Parameter(schema = @Schema(implementation = PedigreeMaintainCase.class), required = true, description = "产品谱系维修分析信息") @Valid @RequestBody PedigreeMaintainCase entity) throws URISyntaxException {
        try {
            PedigreeMaintainCase result = pedigreeMaintainCaseService.createInstance(entity);
            return ResponseEntity.created(new URI(this.mapUri + "/" + result.getId())).headers(HeaderUtil.createdAlert(this.entityName, result.getId().toString())).body(result);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 更新产品谱系维修分析
     *
     * @param entity 产品谱系维修分析
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.maintiancase.PedigreeMaintainCase>
     * <AUTHOR>
     * @date 2023/3/29
     */
    @Operation(summary = "更新产品谱系维修分析")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping
    @Override
    public ResponseEntity<PedigreeMaintainCase> update(@Parameter(schema = @Schema(implementation = PedigreeMaintainCase.class), required = true, description = "产品谱系维修分析信息") @Valid @RequestBody PedigreeMaintainCase entity) throws URISyntaxException {
        try {
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, entity.getId().toString())).body(pedigreeMaintainCaseService.updateInstance(entity));
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/importTableExcel"})
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data,
                                                 @RequestParam(value = "suffix", required = false) String suffix,
                                                 @RequestParam(value = "metaColumn", required = false) String metaColumn, HttpServletResponse response) throws Exception {

        this.prepareImportParams();
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = pedigreeMaintainCaseService.importTableExcel(file);
        //获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> org.apache.commons.lang3.StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(org.apache.commons.lang3.StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }

    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "产品谱系维修分析方案");
    }
}
