package net.airuima.maintain.web.rest.base.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 产品谱系维修方案导入ExcelDTO
 * <AUTHOR>
 */
public class PedigreeMaintainCaseImportDTO {

    @Excel(name = "产品谱系名称",orderNum = "1")
    private String pedigreeName;

    @Excel(name = "产品谱系编码",orderNum = "2")
    private String pedigreeCode;

    @Excel(name = "维修分析方案名称",orderNum = "3")
    private String maintainCaseName;

    @Excel(name = "维修分析方案编码",orderNum = "4")
    private String maintainCaseCode;

    @Excel(name = "返修流程",replace = {"返修工艺路线_0","原工艺路线_1"},orderNum = "5")
    private Integer reworkCategory;

    @Excel(name = "工艺路线名称",orderNum = "6")
    private String workFlowName;

    @Excel(name = "工艺路线编码",orderNum = "7")
    private String workFlowCode;

    @Excel(name = "客户名称",orderNum = "8")
    private String clientName;

    @Excel(name = "客户编码",orderNum = "9")
    private String clientCode;


    @Excel(name = "是否启用",replace = {"是_true","否_false"},orderNum = "10")
    private Boolean isEnable = Boolean.TRUE;

    public String getPedigreeName() {
        return pedigreeName;
    }

    public PedigreeMaintainCaseImportDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public PedigreeMaintainCaseImportDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getMaintainCaseName() {
        return maintainCaseName;
    }

    public PedigreeMaintainCaseImportDTO setMaintainCaseName(String maintainCaseName) {
        this.maintainCaseName = maintainCaseName;
        return this;
    }

    public String getMaintainCaseCode() {
        return maintainCaseCode;
    }

    public PedigreeMaintainCaseImportDTO setMaintainCaseCode(String maintainCaseCode) {
        this.maintainCaseCode = maintainCaseCode;
        return this;
    }

    public Integer getReworkCategory() {
        return reworkCategory;
    }

    public PedigreeMaintainCaseImportDTO setReworkCategory(Integer reworkCategory) {
        this.reworkCategory = reworkCategory;
        return this;
    }

    public String getWorkFlowName() {
        return workFlowName;
    }

    public PedigreeMaintainCaseImportDTO setWorkFlowName(String workFlowName) {
        this.workFlowName = workFlowName;
        return this;
    }

    public String getWorkFlowCode() {
        return workFlowCode;
    }

    public PedigreeMaintainCaseImportDTO setWorkFlowCode(String workFlowCode) {
        this.workFlowCode = workFlowCode;
        return this;
    }

    public String getClientName() {
        return clientName;
    }

    public PedigreeMaintainCaseImportDTO setClientName(String clientName) {
        this.clientName = clientName;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public PedigreeMaintainCaseImportDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeMaintainCaseImportDTO setIsEnable(Boolean enable) {
        isEnable = enable;
        return this;
    }
}
