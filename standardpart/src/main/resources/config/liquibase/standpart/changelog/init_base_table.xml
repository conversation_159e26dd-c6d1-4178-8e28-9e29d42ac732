<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.6.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet author="zhuhuawu (generated)" id="1737530058005-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="base_standard_part"/>
            </not>
        </preConditions>
        <createTable remarks="标准件基础信息表" tableName="base_standard_part">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="sn" remarks="标准件SN" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="material_code" remarks="物料编码" type="VARCHAR(50)"/>
            <column name="record_date" remarks="制作日期" type="timestamp"/>
            <column name="expire_date" remarks="到期日期" type="timestamp"/>
            <column name="author" remarks="制作人" type="VARCHAR(50)"/>
            <column defaultValueNumeric="0" name="status" remarks="状态(0:正常;1:超期;2:报废)" type="TINYINT(3)"/>
            <column name="note" remarks="备注信息" type="VARCHAR(512)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="标准件有效期配置表" tableName="base_standard_part_config">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="180" name="period" remarks="有效期" type="INT"/>
            <column defaultValueNumeric="3" name="unit" remarks="有效期单位(0:年;1:月;2:周;3:天)" type="TINYINT(3)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="标准件测试历史表" tableName="procedure_standard_part_check_result">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="standard_part_id" remarks="标准件ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="test_time" type="timestamp"/>
            <column name="tester" remarks="测试人员" type="VARCHAR(50)"/>
            <column defaultValueBoolean="true" name="result" remarks="预留字段(0:不合格;1:合格)" type="BIT(1)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="is_latest" remarks="是否为最新记录" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable remarks="标准件测试数据明细" tableName="procedure_standard_part_check_result_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="check_result_id" remarks="标准件测试历史ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="tool" remarks="工具编号" type="VARCHAR(50)"/>
            <column defaultValueNumeric="1" name="times" remarks="测试次数" type="INT"/>
            <column name="check_item_code" remarks="检测项目编码" type="VARCHAR(50)"/>
            <column name="check_item_unit" remarks="检测项单位" type="VARCHAR(50)"/>
            <column name="sub_module" remarks="标准件子模块" type="VARCHAR(50)"/>
            <column name="lower_number" type="DOUBLE(13, 3)"/>
            <column name="upper_number" type="DOUBLE(13, 3)"/>
            <column name="middle_number" type="DOUBLE(13, 3)"/>
            <column name="number" type="DOUBLE(13, 3)"/>
            <column defaultValueBoolean="true" name="result" remarks="预留字段(0:不合格;1:合格)" type="BIT(1)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="pedigree_id, deleted" constraintName="base_standard_part_config_unique_index" tableName="base_standard_part_config"/>
        <addUniqueConstraint columnNames="sn, deleted" constraintName="base_standard_part_unique_index" tableName="base_standard_part"/>
        <createIndex indexName="base_standard_part_expire_date_index" tableName="base_standard_part">
            <column name="expire_date"/>
        </createIndex>
        <createIndex indexName="base_standard_part_status_index" tableName="base_standard_part">
            <column defaultValueNumeric="0" name="status"/>
        </createIndex>
        <createIndex indexName="procedure_standard_part_check_result_id_index" tableName="procedure_standard_part_check_result_detail">
            <column name="check_result_id"/>
        </createIndex>
        <createIndex indexName="procedure_standard_part_check_result_item_code_index" tableName="procedure_standard_part_check_result_detail">
            <column name="check_item_code"/>
        </createIndex>
        <createIndex indexName="procedure_standard_part_check_result_part_id_index" tableName="procedure_standard_part_check_result">
            <column name="standard_part_id"/>
        </createIndex>
        <createIndex indexName="procedure_standard_part_check_result_result_index" tableName="procedure_standard_part_check_result">
            <column defaultValueBoolean="true" name="result"/>
        </createIndex>
        <createIndex indexName="procedure_standard_part_check_result_sub_module_index" tableName="procedure_standard_part_check_result_detail">
            <column name="sub_module"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
