package net.airuima.standardpart.repository.procedure;

import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.standardpart.domain.procedure.StandardPartCheckResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件测试历史表Repository
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Repository
public interface StandardPartCheckResultRepository extends LogicDeleteableRepository<StandardPartCheckResult>,
        JpaSpecificationExecutor<StandardPartCheckResult>, JpaRepository<StandardPartCheckResult, Long> {

    /**
     * 通过标准件主键ID批量更新状态
     * @param standardPartId 标准件主键ID
     * @param isLatest 是否为最新记录(0:否;1:是)
     */
    @Modifying
    @Query("update StandardPartCheckResult set isLatest=?2 where standardPart.id=?1 and isLatest!=?2 and deleted=0 ")
    void batchUpdateStandardPartCheckResultIsLatest(Long standardPartId,Boolean isLatest);

    /**
     * 通过标准件主键ID获取最新检测历史记录
     * @param standardPartId 标准件主键ID
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.procedure.standardpart.StandardPartCheckResult  标准件测试历史
     */
    StandardPartCheckResult findTop1ByStandardPartIdAndDeletedOrderByTestTimeDescIdDesc(Long standardPartId,Long deleted);

    /**
     * 通过标准件主键ID获取最新检测数据
     * @param standardPartId 标准件主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.standardpart.StandardPartCheckResult> 标准件测试历史
     */
    @Query("select checkResult from StandardPartCheckResult checkResult where checkResult.standardPart.id=?1 and checkResult.isLatest=true and checkResult.deleted=?2")
    Optional<StandardPartCheckResult> findLatestStandardPartCheckResultByStandardPartId(Long standardPartId, Long deleted );

    /**
     * 通过标准件主键ID及测试事件获取检测数据
     * @param standardPartId 标准件主键ID
     * @param testTime 测试时间
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.procedure.standardpart.StandardPartCheckResult  标准件测试历史
     */
    StandardPartCheckResult findByStandardPartIdAndTestTimeAndDeleted(Long standardPartId, LocalDateTime testTime, Long deleted);
}
