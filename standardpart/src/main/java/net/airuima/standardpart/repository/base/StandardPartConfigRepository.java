package net.airuima.standardpart.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.standardpart.domain.base.StandardPartConfig;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件有效期配置表Repository
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Repository
public interface StandardPartConfigRepository extends LogicDeleteableRepository<StandardPartConfig>,
        EntityGraphJpaSpecificationExecutor<StandardPartConfig>, EntityGraphJpaRepository<StandardPartConfig, Long> {

    /**
     * 通过产品谱系主键ID获取标准件检测周期
     * @param pedigreeId 产品谱系主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.standardpart.StandardPartConfig> 标准件有效期配置
     */
    @DataFilter(isSkip = true)
    Optional<StandardPartConfig> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);
}
