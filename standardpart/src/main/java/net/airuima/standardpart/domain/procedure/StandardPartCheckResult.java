package net.airuima.standardpart.domain.procedure;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.standardpart.domain.base.StandardPart;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件测试历史表Domain
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Schema(name = "标准件测试历史表(StandardPartCheckResult)", description = "标准件测试历史表")
@Entity
@Table(name = "procedure_standard_part_check_result")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class StandardPartCheckResult extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标准件ID
     */
    @NotNull
    @Schema(description = "标准件ID", required = true)
    @ManyToOne
    @JoinColumn(name = "standard_part_id", nullable = false)
    private StandardPart standardPart;

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    @Column(name = "test_time")
    private LocalDateTime testTime;

    /**
     * 测试人员
     */
    @Schema(description = "测试人员")
    @Column(name = "tester")
    private String tester;

    /**
     * 是否为最新记录
     */
    @Schema(description = "是否为最新记录")
    @Column(name = "is_latest")
    private boolean isLatest;

    public StandardPart getStandardPart() {
        return standardPart;
    }

    public StandardPartCheckResult setStandardPart(StandardPart standardPart) {
        this.standardPart = standardPart;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public StandardPartCheckResult setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public StandardPartCheckResult setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public boolean getIsLatest() {
        return isLatest;
    }

    public StandardPartCheckResult setIsLatest(boolean latest) {
        isLatest = latest;
        return this;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StandardPartCheckResult standardPartCheckResult = (StandardPartCheckResult) o;
        if (standardPartCheckResult.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), standardPartCheckResult.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
