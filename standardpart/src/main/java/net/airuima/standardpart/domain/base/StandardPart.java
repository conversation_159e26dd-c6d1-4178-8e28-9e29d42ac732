package net.airuima.standardpart.domain.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.constant.Constants;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件基础信息表Domain
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Schema(name = "标准件基础信息表(StandardPart)", description = "标准件基础信息表")
@Entity
@Table(name = "base_standard_part", uniqueConstraints = {
        @UniqueConstraint(name = "base_standard_part_unique_index", columnNames = {"sn", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "标准件清单")
public class StandardPart extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标准件SN
     */
    @NotNull
    @Schema(description = "标准件SN", required = true)
    @Column(name = "sn", nullable = false)
    private String sn;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    @Column(name = "material_code")
    private String materialCode;

    /**
     * 制作日期
     */
    @Schema(description = "制作日期")
    @Column(name = "record_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recordDate;

    /**
     * 到期日期
     */
    @Schema(description = "到期日期")
    @Column(name = "expire_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireDate;

    /**
     * 制作人
     */
    @Schema(description = "制作人")
    @Column(name = "author")
    private String author;

    /**
     * 状态(0:正常;1:超期;2:报废)
     */
    @Schema(description = "状态(0:正常;1:超期;2:报废)")
    @Column(name = "status")
    private int status;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @Column(name = "note")
    private String note;


    public String getSn() {
        return sn;
    }

    public StandardPart setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public StandardPart setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public StandardPart setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public LocalDateTime getExpireDate() {
        return expireDate;
    }

    public StandardPart setExpireDate(LocalDateTime expireDate) {
        this.expireDate = expireDate;
        return this;
    }

    public String getAuthor() {
        return author;
    }

    public StandardPart setAuthor(String author) {
        this.author = author;
        return this;
    }

    public int getStatus() {
        if(this.status >= Constants.INT_TWO){
            return status;
        }
        if(null != this.expireDate && this.expireDate.isBefore(LocalDateTime.now())){
            return Constants.INT_ONE;
        }
        return Constants.INT_ZERO;
    }

    public StandardPart setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getNote() {
        return note;
    }

    public StandardPart setNote(String note) {
        this.note = note;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StandardPart standardPart = (StandardPart) o;
        if (standardPart.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), standardPart.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
