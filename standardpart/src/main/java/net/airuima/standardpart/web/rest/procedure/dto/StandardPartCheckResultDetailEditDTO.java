package net.airuima.standardpart.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.config.DoubleSerializer;
import net.airuima.config.annotation.DoubleSerialize;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/24
 */
@Schema(description = "标准件检测历史编辑获取检测项数据明细DTO")
public class StandardPartCheckResultDetailEditDTO {
    /**
     * 标准件编码
     */
    @Schema(description = "标准件编码")
    private String sn;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    private LocalDateTime testTime;

    /**
     * 标准件检测项目数据明细列表
     */
    @Schema(description = "标准件检测项目数据明细列表")
    private List<CheckResultDetailInfo> checkResultDetailInfoList;

    public String getSn() {
        return sn;
    }

    public StandardPartCheckResultDetailEditDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public StandardPartCheckResultDetailEditDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public StandardPartCheckResultDetailEditDTO setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public List<CheckResultDetailInfo> getCheckResultDetailInfoList() {
        return checkResultDetailInfoList;
    }

    public StandardPartCheckResultDetailEditDTO setCheckResultDetailInfoList(List<CheckResultDetailInfo> checkResultDetailInfoList) {
        this.checkResultDetailInfoList = checkResultDetailInfoList;
        return this;
    }

    @Schema(description = "标准件检测项目数据明细")
    public static class CheckResultDetailInfo{
        /**
         * 标准件检测历史明细ID
         */
        @Schema(description = "标准件检测历史明细ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 工具编号
         */
        @Schema(description = "工具编号")
        private String tool;

        /**
         * 测试次数
         */
        @Schema(description = "测试次数")
        private Integer times;

        /**
         * 检测项目编码
         */
        @Schema(description = "检测项目编码")
        private String checkItemCode;


        /**
         * 标准件子模块
         */
        @Schema(description = "标准件子模块")
        private String subModule;

        /**
         * 下限值
         */
        @Schema(description = "下限值")
        @JsonSerialize(using = DoubleSerializer.class)
        @DoubleSerialize(scale = 3)
        private Double lowerNumber;

        /**
         * 上限值
         */
        @Schema(description = "上限值")
        @JsonSerialize(using = DoubleSerializer.class)
        @DoubleSerialize(scale = 3)
        private Double upperNumber;

        /**
         * 中心值
         */
        @Schema(description = "中心值")
        @JsonSerialize(using = DoubleSerializer.class)
        @DoubleSerialize(scale = 3)
        private Double middleNumber;

        /**
         * 测试值
         */
        @Schema(description = "测试值")
        @JsonSerialize(using = DoubleSerializer.class)
        @DoubleSerialize(scale = 3)
        private Double number;

        /**
         * 测试人员
         */
        @Schema(description = "测试人员")
        private String tester;

        public Long getId() {
            return id;
        }

        public CheckResultDetailInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getTool() {
            return tool;
        }

        public CheckResultDetailInfo setTool(String tool) {
            this.tool = tool;
            return this;
        }

        public Integer getTimes() {
            return times;
        }

        public CheckResultDetailInfo setTimes(Integer times) {
            this.times = times;
            return this;
        }

        public String getCheckItemCode() {
            return checkItemCode;
        }

        public CheckResultDetailInfo setCheckItemCode(String checkItemCode) {
            this.checkItemCode = checkItemCode;
            return this;
        }

        public String getSubModule() {
            return subModule;
        }

        public CheckResultDetailInfo setSubModule(String subModule) {
            this.subModule = subModule;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public CheckResultDetailInfo setNumber(Double number) {
            this.number = number;
            return this;
        }

        public Double getLowerNumber() {
            return lowerNumber;
        }

        public CheckResultDetailInfo setLowerNumber(Double lowerNumber) {
            this.lowerNumber = lowerNumber;
            return this;
        }

        public Double getUpperNumber() {
            return upperNumber;
        }

        public CheckResultDetailInfo setUpperNumber(Double upperNumber) {
            this.upperNumber = upperNumber;
            return this;
        }

        public Double getMiddleNumber() {
            return middleNumber;
        }

        public CheckResultDetailInfo setMiddleNumber(Double middleNumber) {
            this.middleNumber = middleNumber;
            return this;
        }

        public String getTester() {
            return tester;
        }

        public CheckResultDetailInfo setTester(String tester) {
            this.tester = tester;
            return this;
        }
    }
}
