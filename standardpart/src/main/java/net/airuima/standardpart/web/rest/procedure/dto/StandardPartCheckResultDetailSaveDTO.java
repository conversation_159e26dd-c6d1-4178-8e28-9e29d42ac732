package net.airuima.standardpart.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/24
 */
@Schema(description = "保存修改的标准件历史明细数据DTO")
public class StandardPartCheckResultDetailSaveDTO {
    /**
     * 标准件检测历史明细ID
     */
    @Schema(description = "标准件检测历史明细ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 下限值
     */
    @Schema(description = "下限值")
    private Double lowerNumber;

    /**
     * 上限值
     */
    @Schema(description = "上限值")
    private Double upperNumber;

    /**
     * 中心值
     */
    @Schema(description = "中心值")
    private Double middleNumber;

    /**
     * 测试值
     */
    @Schema(description = "测试值")
    private Double number;

    public Long getId() {
        return id;
    }

    public StandardPartCheckResultDetailSaveDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Double getLowerNumber() {
        return lowerNumber;
    }

    public StandardPartCheckResultDetailSaveDTO setLowerNumber(Double lowerNumber) {
        this.lowerNumber = lowerNumber;
        return this;
    }

    public Double getUpperNumber() {
        return upperNumber;
    }

    public StandardPartCheckResultDetailSaveDTO setUpperNumber(Double upperNumber) {
        this.upperNumber = upperNumber;
        return this;
    }

    public Double getMiddleNumber() {
        return middleNumber;
    }

    public StandardPartCheckResultDetailSaveDTO setMiddleNumber(Double middleNumber) {
        this.middleNumber = middleNumber;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public StandardPartCheckResultDetailSaveDTO setNumber(Double number) {
        this.number = number;
        return this;
    }
}
