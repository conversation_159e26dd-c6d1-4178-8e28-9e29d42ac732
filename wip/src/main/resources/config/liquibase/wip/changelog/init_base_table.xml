<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="RMPC (generated)" id="1714041162154-1">
        <createTable remarks="线边仓" tableName="base_wip_warehouse">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="线边仓编码" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="线边仓名称" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="work_line_id" remarks="生产线ID" type="BIGINT"/>
            <column name="operator_id" remarks="负责人" type="BIGINT"/>
            <column defaultValue="1" name="is_enable" remarks="是否启用(0:否;1:是)" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-2">
        <createTable remarks="线边仓出库表（工序上料，自动扣料）" tableName="procedure_wip_deliver">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="warehouse_id" remarks="线边仓id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="origin_batch" remarks="入库批次" type="VARCHAR(255)"/>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column name="work_sheet_id" remarks="关联工单ID" type="BIGINT"/>
            <column name="number" remarks="出库数量" type="DECIMAL(14, 5)">
                <constraints nullable="false"/>
            </column>
            <column name="operator_id" remarks="出库人id" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP" name="record_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注信息" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="deliver_batch" remarks="出库(领用)批次" type="VARCHAR(255)"/>
            <column name="organization_id" remarks="领用部门" type="BIGINT"/>
            <column name="receiver_id" remarks="领用人" type="BIGINT"/>
            <column name="purposes" remarks="领用用途" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-3">
        <createTable remarks="线边库存" tableName="procedure_wip_inventory">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="warehouse_id" type="BIGINT"/>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column name="batch" remarks="批次" type="VARCHAR(255)"/>
            <column name="number" remarks="领料总数" type="DECIMAL(14, 5)">
                <constraints nullable="false"/>
            </column>
            <column name="left_number" remarks="剩余数量" type="DECIMAL(14, 5)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="arrival_date" remarks="到货日期" type="timestamp"/>
            <column name="produce_date" remarks="生产日期" type="timestamp"/>
            <column defaultValueNumeric="0" name="expire_day" remarks="保质期(天)" type="INT"/>
            <column name="expiration_date" remarks="截止有效期" type="timestamp"/>
            <column defaultValue="0" name="expired" type="BIT(1)"/>
            <column defaultValueNumeric="0.00000" name="average_dosage" remarks="前30天平均用量" type="DECIMAL(14, 5)"/>
            <column defaultValueNumeric="0.00000" name="before_day_dosage" remarks="前天用量" type="DECIMAL(14, 5)"/>
            <column defaultValueNumeric="0.00000" name="safety_stock" remarks="安全库存" type="DECIMAL(14, 5)"/>
            <column defaultValueNumeric="0" name="purchasing_cycle" remarks="采购周期（天）" type="INT"/>
            <column defaultValueNumeric="0" name="estimate_available_day" remarks="预估可用天数" type="INT"/>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-4">
        <createTable remarks="台账" tableName="procedure_wip_ledger">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date" remarks="时间" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="warehouse_id" remarks="线边仓id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column name="operator_id" remarks="操作人id" type="VARCHAR(255)"/>
            <column name="origin_batch" remarks="入库批次" type="VARCHAR(255)"/>
            <column name="operation" remarks="类型（1：工单出库；2：更改库存；3：领料入库；4:退料入库；5：盘盈入库；6：盘亏出库；7：工单入库）" type="TINYINT(3)"/>
            <column name="category" remarks="库存类型（1：线边库存；2：工单库存；）" type="TINYINT(3)"/>
            <column name="work_sheet_id" remarks="关联工单ID" type="BIGINT"/>
            <column name="number" remarks="数量" type="DECIMAL(14, 5)"/>
            <column name="origin_inventory" remarks="原始库存（变更前）" type="DECIMAL(14, 5)"/>
            <column name="latest_inventory" remarks="当前库存（变更后）" type="DECIMAL(14, 5)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="deliver_batch" remarks="出库(领用)批次" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-5">
        <createTable remarks="线边仓退料记录" tableName="procedure_wip_return">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="warehouse_id" remarks="线边仓id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="origin_batch" remarks="入库批次" type="VARCHAR(255)"/>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column name="number" remarks="退料总数" type="DECIMAL(14, 5)">
                <constraints nullable="false"/>
            </column>
            <column name="operator_id" remarks="退料人id" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP" name="record_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注信息" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="deliver_batch" remarks="出库(领用)批次" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-6">
        <createTable remarks="线边仓入库（领料记录）" tableName="procedure_wip_store">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="warehouse_id" remarks="线边仓id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="batch" remarks="批次" type="VARCHAR(255)"/>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column name="number" remarks="入库总数" type="DECIMAL(14, 5)">
                <constraints nullable="false"/>
            </column>
            <column name="operator_id" remarks="入库人id" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP" name="record_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注信息" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="arrival_date" remarks="到货日期" type="timestamp"/>
            <column name="produce_date" remarks="生产日期" type="timestamp"/>
            <column defaultValueNumeric="0" name="expire_day" remarks="保质期(天)" type="INT"/>
            <column defaultValueNumeric="0" name="purchasing_cycle" remarks="采购周期（天）" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-7">
        <createTable remarks="线边库存盘点历史" tableName="procedure_wip_verificate">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="serial_number" remarks="盘点单号" type="VARCHAR(30)"/>
            <column name="warehouse_id" remarks="线边仓id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="category" remarks="类型（1：线边库存；2：工单库存；）" type="TINYINT(3)"/>
            <column name="work_sheet_id" remarks="关联工单ID" type="BIGINT"/>
            <column name="operator_id" remarks="盘点人id" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date" remarks="盘点时间" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="note" remarks="备注信息" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-8">
        <createTable remarks="线边库存盘点单历史明细" tableName="procedure_wip_verificate_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="verificate_id" remarks="盘点单历史id" type="BIGINT"/>
            <column name="material_id" remarks="物料id" type="BIGINT"/>
            <column name="batch" remarks="批次" type="VARCHAR(255)"/>
            <column name="system_number" remarks="系统库存数量" type="DECIMAL(14, 5)">
                <constraints nullable="false"/>
            </column>
            <column name="actual_number" remarks="实盘数量" type="DECIMAL(14, 5)">
                <constraints nullable="false"/>
            </column>
            <column name="differ_number" remarks="盘亏/盘盈" type="DECIMAL(14, 5)">
                <constraints nullable="false"/>
            </column>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-9">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_wip_warehouse_unique_index" tableName="base_wip_warehouse"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-10">
        <addUniqueConstraint columnNames="work_line_id, deleted" constraintName="base_wip_warehouse_work_line_id_unique_index" tableName="base_wip_warehouse"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-11">
        <addUniqueConstraint columnNames="warehouse_id, material_id, batch, deleted" constraintName="procedure_wip_inventory_unique_index" tableName="procedure_wip_inventory"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-12">
        <addUniqueConstraint columnNames="verificate_id, material_id, batch, deleted" constraintName="procedure_wip_verificate_detail_unique_index" tableName="procedure_wip_verificate_detail"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-13">
        <createIndex associatedWith="" indexName="base_wip_warehouse_name_index" tableName="base_wip_warehouse">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-14">
        <createIndex associatedWith="" indexName="procedure_wip_deliver_deliver_batch_index" tableName="procedure_wip_deliver">
            <column name="deliver_batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-15">
        <createIndex associatedWith="" indexName="procedure_wip_deliver_material_id_index" tableName="procedure_wip_deliver">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-16">
        <createIndex associatedWith="" indexName="procedure_wip_deliver_origin_batch_index" tableName="procedure_wip_deliver">
            <column name="origin_batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-17">
        <createIndex associatedWith="" indexName="procedure_wip_deliver_warehouse_id_index" tableName="procedure_wip_deliver">
            <column name="warehouse_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-18">
        <createIndex associatedWith="" indexName="procedure_wip_deliver_warehouse_id_index" tableName="procedure_wip_store">
            <column name="warehouse_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-19">
        <createIndex associatedWith="" indexName="procedure_wip_deliver_work_sheet_id_index" tableName="procedure_wip_deliver">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-20">
        <createIndex associatedWith="" indexName="procedure_wip_inventory_batch_index" tableName="procedure_wip_inventory">
            <column name="batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-21">
        <createIndex associatedWith="" indexName="procedure_wip_inventory_material_id_index" tableName="procedure_wip_inventory">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-22">
        <createIndex associatedWith="" indexName="procedure_wip_ledger_deliver_batch_index" tableName="procedure_wip_ledger">
            <column name="deliver_batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-23">
        <createIndex associatedWith="" indexName="procedure_wip_ledger_operation_index" tableName="procedure_wip_ledger">
            <column name="operation"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-24">
        <createIndex associatedWith="" indexName="procedure_wip_ledger_origin_batch_index" tableName="procedure_wip_ledger">
            <column name="origin_batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-25">
        <createIndex associatedWith="" indexName="procedure_wip_ledger_record_date_index" tableName="procedure_wip_ledger">
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-26">
        <createIndex associatedWith="" indexName="procedure_wip_ledger_warehouse_id_index" tableName="procedure_wip_ledger">
            <column name="warehouse_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-27">
        <createIndex associatedWith="" indexName="procedure_wip_ledger_worksheetid_index" tableName="procedure_wip_ledger">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-28">
        <createIndex associatedWith="" indexName="procedure_wip_return_deliver_batch_index" tableName="procedure_wip_return">
            <column name="deliver_batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-29">
        <createIndex associatedWith="" indexName="procedure_wip_return_origin_batch_index" tableName="procedure_wip_return">
            <column name="origin_batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-30">
        <createIndex associatedWith="" indexName="procedure_wip_store_batch_index" tableName="procedure_wip_store">
            <column name="batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-31">
        <createIndex associatedWith="" indexName="procedure_wip_store_deliver_material_id_index" tableName="procedure_wip_return">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-32">
        <createIndex associatedWith="" indexName="procedure_wip_store_deliver_record_date_index" tableName="procedure_wip_return">
            <column defaultValueComputed="CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP" name="record_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-33">
        <createIndex associatedWith="" indexName="procedure_wip_store_deliver_warehouse_id_index" tableName="procedure_wip_return">
            <column name="warehouse_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-34">
        <createIndex associatedWith="" indexName="procedure_wip_store_material_id_index" tableName="procedure_wip_store">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-35">
        <createIndex associatedWith="" indexName="procedure_wip_store_record_date_index" tableName="procedure_wip_store">
            <column defaultValueComputed="CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP" name="record_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-36">
        <createIndex associatedWith="" indexName="procedure_wip_verificate_detail_batch_index" tableName="procedure_wip_verificate_detail">
            <column name="batch"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-37">
        <createIndex associatedWith="" indexName="procedure_wip_verificate_detail_materialid_index" tableName="procedure_wip_verificate_detail">
            <column name="material_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-38">
        <createIndex associatedWith="" indexName="procedure_wip_verificate_record_date_index" tableName="procedure_wip_verificate">
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-39">
        <createIndex associatedWith="" indexName="procedure_wip_verificate_serial_number_index" tableName="procedure_wip_verificate">
            <column name="serial_number"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-40">
        <createIndex associatedWith="" indexName="procedure_wip_verificate_warehouse_id_index" tableName="procedure_wip_verificate">
            <column name="warehouse_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714041162154-41">
        <createIndex associatedWith="" indexName="procedure_wip_verificate_worksheet_id_index" tableName="procedure_wip_verificate">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="202409061356" author="zorro">
        <addColumn tableName="procedure_wip_deliver">
            <column name="return_number"  remarks="退料数量" type="DECIMAL(14, 5)" defaultValueNumeric="0"/>
        </addColumn>
    </changeSet>
    <changeSet id="202502100921" author="YangS">
        <dropNotNullConstraint tableName="procedure_wip_verificate" columnName="warehouse_id" columnDataType="BIGINT" />
    </changeSet>
</databaseChangeLog>
