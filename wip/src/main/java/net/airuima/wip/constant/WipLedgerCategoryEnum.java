package net.airuima.wip.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 线边台账库存类型枚举
 * <AUTHOR>
 * @date 2023/1/11
 */
public enum WipLedgerCategoryEnum {
    /**
     * 线边库存
     */
    WIP_LEDGER_CATEGORY_INVENTORY("WIP_LEDGER_CATEGORY_INVENTORY",1,"线边库存"),
    /**
     * 工单库存
     */
    WIP_LEDGER_CATEGORY_WORK_SHEET("WIP_LEDGER_CATEGORY_WORK_SHEET",2,"工单库存"),
    /**
     * 工序辅料库存
     */
    WIP_LEDGER_CATEGORY_STEP_MATERIAL("WIP_LEDGER_CATEGORY_STEP_MATERIAL",3,"工序辅料库存");

    private String  wipLedgerCategory;

    private int category;

    private String remark;

    WipLedgerCategoryEnum(String wipLedgerCategory, int category, String remark) {
        this.wipLedgerCategory = wipLedgerCategory;
        this.category = category;
        this.remark = remark;
    }

    public String getWipLedgerCategory() {
        return wipLedgerCategory;
    }

    public WipLedgerCategoryEnum setWipLedgerCategory(String wipLedgerCategory) {
        this.wipLedgerCategory = wipLedgerCategory;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WipLedgerCategoryEnum setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public WipLedgerCategoryEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
