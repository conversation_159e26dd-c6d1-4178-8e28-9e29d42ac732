package net.airuima.wip.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WipLedgerCategoryEnum;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.wip.domain.procedure.WipDeliver;
import net.airuima.wip.domain.procedure.WipInventory;
import net.airuima.wip.domain.procedure.WipReturn;
import net.airuima.wip.dto.LedgerDTO;
import net.airuima.wip.repository.procedure.WipDeliverRepository;
import net.airuima.wip.repository.procedure.WipInventoryRepository;
import net.airuima.wip.repository.procedure.WipReturnRepository;
import net.airuima.wip.service.procedure.impl.WipLedgerServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓退料记录Service
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WipReturnService extends CommonJpaService<WipReturn> {

    private static final String WIP_RETURN_ENTITY_GRAPH = "wipReturnEntityGraph";
    private final WipReturnRepository wipReturnRepository;
    private final WipInventoryService wipInventoryService;
    @Autowired
    private WipInventoryRepository wipInventoryRepository;
    @Autowired
    private WipDeliverRepository wipDeliverRepository;
    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;

    @Autowired
    private WipLedgerServiceImpl wipLedgerService;

    public WipReturnService(WipReturnRepository wipReturnRepository, WipInventoryService wipInventoryService) {
        this.wipReturnRepository = wipReturnRepository;
        this.wipInventoryService = wipInventoryService;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipReturn> find(Specification<WipReturn> spec, Pageable pageable) {
        return wipReturnRepository.findAll(spec, pageable,new NamedEntityGraph(WIP_RETURN_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WipReturn> find(Specification<WipReturn> spec) {
        return wipReturnRepository.findAll(spec,new NamedEntityGraph(WIP_RETURN_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipReturn> findAll(Pageable pageable) {
        return wipReturnRepository.findAll(pageable,new NamedEntityGraph(WIP_RETURN_ENTITY_GRAPH));
    }

    /**
     * 新增退料入库记录，台账和库存分别加上退料数量
     * <AUTHOR>
     * @param wipReturn 退料记录
     * @return net.airuima.rbase.domain.procedure.wip.WipReturn
     **/
    public WipReturn createReturnInfo(WipReturn wipReturn) {
        wipInventoryService.validWipWarehouse(wipReturn.getWipWarehouse().getId());

        //验证当前退料批次是否合法
        //线边仓库不存在 物料+入库批次+线边仓 获取唯一记录
        WipInventory wipInventory = wipInventoryRepository.findByWipWarehouseIdAndMaterialIdAndBatchAndDeleted(wipReturn.getWipWarehouse().getId(), wipReturn.getMaterialId(), wipReturn.getOriginBatch(), Constants.LONG_ZERO);
        if (wipInventory == null){
            throw new ResponseException("error.wipInventoryIsNotExist", "线边库存记录不存在");
        }
        //验证当前领用批次号是否存在
        List<WipDeliver> wipDelivers = wipDeliverRepository.findByWipWarehouseIdAndMaterialIdAndOriginBatchAndDeliverBatchAndDeleted(wipReturn.getWipWarehouse().getId(),wipReturn.getMaterialId(),wipReturn.getOriginBatch(),wipReturn.getDeliverBatch(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wipDelivers)){
            throw new ResponseException("error.deliverBatchIsNotExist", wipReturn.getDeliverBatch()+":出库批次号不存在");
        }
        BigDecimal allReturnNumber = wipReturn.getNumber();
        //获取已存在的退料记录
        List<WipReturn> wipReturnList = wipReturnRepository.findByDeliverBatchAndOriginBatchAndMaterialIdAndWipWarehouseIdAndDeleted(wipReturn.getDeliverBatch(), wipReturn.getOriginBatch(), wipReturn.getMaterialId(), wipReturn.getWipWarehouse().getId(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(wipReturnList)){
            allReturnNumber = allReturnNumber.add(wipReturnList.stream().map(WipReturn::getNumber).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if (allReturnNumber.compareTo(wipDelivers.stream().map(WipDeliver::getNumber).reduce(BigDecimal.ZERO, BigDecimal::add)) > Constants.INT_ZERO){
            throw new ResponseException("error.exceedsTotalStock", "退料数量超过出库总数!");
        }
        // 新增退料入库记录
        wipReturn.setRecordDate(LocalDateTime.now())
                .setDeleted(Constants.LONG_ZERO);
        wipReturnRepository.save(wipReturn);

        // 新增台账记录，更新库存
        LedgerDTO ledgerDTO = new LedgerDTO();
        ledgerDTO.setWipWarehouse(wipReturn.getWipWarehouse())
                .setOriginBatch(wipReturn.getOriginBatch())
                .setDeliverBatch(wipReturn.getDeliverBatch())
                .setMaterialId(wipReturn.getMaterialId())
                .setOperation(WipLedgerOperationEnum.WIP_LEDGER_OPERATION_RETURN.getCategory())
                .setCategory(WipLedgerCategoryEnum.WIP_LEDGER_CATEGORY_INVENTORY.getCategory())
                .setChangeNumber(wipReturn.getNumber())
                .setOperatorId(wipReturn.getOperatorId());
        wipInventoryService.generateLedgerAndUpdateInventory(ledgerDTO);
        //若有工单的领料记录则优先扣除工单剩余库存
        List<WipDeliver> wipWorkSheetDeliverList = wipDelivers.stream().filter(wipDeliver -> Objects.nonNull(wipDeliver.getWorkSheet())).toList();
        BigDecimal currentLeftReduceNumber = wipReturn.getNumber();
        if(!CollectionUtils.isEmpty(wipWorkSheetDeliverList)){
            for (WipDeliver wipDeliver : wipWorkSheetDeliverList){
                if(wipDeliver.getReturnNumber().compareTo(wipDeliver.getNumber())>= Constants.INT_ZERO){
                    continue;
                }
                if(currentLeftReduceNumber.compareTo(BigDecimal.ZERO) == Constants.INT_ZERO){
                    break;
                }
                BigDecimal leftReturnNumber = wipDeliver.getNumber().subtract(wipDeliver.getReturnNumber());
                BigDecimal changeNumber = leftReturnNumber;
                //工单领料数量小于等于退料数量
                if(leftReturnNumber.compareTo(currentLeftReduceNumber)<=Constants.INT_ZERO){
                    wipDeliver.setReturnNumber(wipDeliver.getReturnNumber().add(leftReturnNumber));
                    currentLeftReduceNumber = currentLeftReduceNumber.subtract(leftReturnNumber);
                }else {
                    wipDeliver.setReturnNumber(wipDeliver.getReturnNumber().add(currentLeftReduceNumber));
                    changeNumber = currentLeftReduceNumber;
                    currentLeftReduceNumber = currentLeftReduceNumber.subtract(currentLeftReduceNumber);
                }
                WorkSheet workSheet = wipDeliver.getWorkSheet();
                // 如果用户填了工单 出库类型为工单出库  新增线边台账
                WsMaterialBatch wsMaterialBatch = wsMaterialBatchRepository.findByWorkSheetIdAndWarehouseIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), wipDeliver.getWipWarehouse().getId(), wipDeliver.getMaterialId(), wipDeliver.getOriginBatch(), Constants.LONG_ZERO);
                if(Objects.isNull(wsMaterialBatch)){
                    continue;
                }
                if(wsMaterialBatch.getLeftNumber()<changeNumber.doubleValue()){
                    throw new ResponseException("error.exceedsTotalWsMaterialBatch", "退料数量超出工单("+workSheet.getSerialNumber()+")剩余库存!");
                }
                wipDeliverRepository.save(wipDeliver);
                wipLedgerService.saveLedgerInfo(null, wsMaterialBatch, this.getLedgerDTO(wipDeliver, changeNumber.negate(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_WORK_SHEET_DELEVER.getCategory(), WipLedgerCategoryEnum.WIP_LEDGER_CATEGORY_WORK_SHEET.getCategory()).setWorkSheet(workSheet));
                wsMaterialBatch.setLeftNumber(wsMaterialBatch.getLeftNumber() - changeNumber.doubleValue())
                        .setNumber(wsMaterialBatch.getNumber() - changeNumber.doubleValue());
                wsMaterialBatchRepository.save(wsMaterialBatch);
            }
        }
        return wipReturn;
    }

    public LedgerDTO getLedgerDTO(WipDeliver deliver, BigDecimal changeNumber, Integer operation, Integer category) {
        LedgerDTO ledgerDTO = new LedgerDTO();
        ledgerDTO.setWipWarehouse(deliver.getWipWarehouse())
                .setWorkSheet(deliver.getWorkSheet())
                .setOriginBatch(deliver.getOriginBatch())
                .setDeliverBatch(deliver.getDeliverBatch())
                .setMaterialId(deliver.getMaterialId())
                .setChangeNumber(changeNumber)
                .setOperation(operation)
                .setCategory(category)
                .setOperatorId(deliver.getOperatorId());
        return ledgerDTO;
    }
}
