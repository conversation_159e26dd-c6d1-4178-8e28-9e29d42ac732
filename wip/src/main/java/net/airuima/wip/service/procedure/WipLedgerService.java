package net.airuima.wip.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.dto.StaffDTO;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.security.SecurityUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.wip.domain.procedure.WipLedger;
import net.airuima.wip.dto.LedgerDTO;
import net.airuima.wip.repository.procedure.WipLedgerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台账Service
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WipLedgerService extends CommonJpaService<WipLedger> {

    private static final String WIP_LEDGER_ENTITY_GRAPH = "wipLedgerEntityGraph";
    private final WipLedgerRepository wipLedgerRepository;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;

    public WipLedgerService(WipLedgerRepository wipLedgerRepository) {
        this.wipLedgerRepository = wipLedgerRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipLedger> find(Specification<WipLedger> spec, Pageable pageable) {
        return wipLedgerRepository.findAll(spec, pageable,new NamedEntityGraph(WIP_LEDGER_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WipLedger> find(Specification<WipLedger> spec) {
        return wipLedgerRepository.findAll(spec,new NamedEntityGraph(WIP_LEDGER_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipLedger> findAll(Pageable pageable) {
        return wipLedgerRepository.findAll(pageable,new NamedEntityGraph(WIP_LEDGER_ENTITY_GRAPH));
    }


    /**
     * 更新线边仓台账
     * <AUTHOR>
     * @param ledgerDTO 台账实体类
     * @param leftNumber 当前库存剩余数量（变更之前）
     **/
    public void saveLedger(LedgerDTO ledgerDTO, BigDecimal leftNumber){
        WipLedger wipLedger = MapperUtils.map(ledgerDTO, WipLedger.class);
        if (leftNumber.add(ledgerDTO.getChangeNumber()).compareTo(BigDecimal.ZERO) < 0){
            throw new ResponseException("error.leftNumberError", "库存剩余数量小于出库变更数量");
        }
        wipLedger.setOriginInventory(leftNumber)
                .setLatestInventory(leftNumber.add(ledgerDTO.getChangeNumber()))
                .setNumber(ledgerDTO.getChangeNumber().abs())
                .setRecordDate(LocalDateTime.now())
                .setDeleted(Constants.LONG_ZERO);
        if (ledgerDTO.getOperatorId() == null && SecurityUtils.getCurrentUserLogin().isPresent()){
            UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(net.airuima.util.SecurityUtils.getCurrentUserLogin().orElse(null));
            StaffDTO operateDto = Objects.nonNull(userDTO) ? userDTO.getStaffDTO() : new StaffDTO();
            wipLedger.setOperatorId(operateDto.getId());
        }
        wipLedgerRepository.save(wipLedger);
    }

}
