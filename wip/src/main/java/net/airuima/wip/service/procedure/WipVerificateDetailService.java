package net.airuima.wip.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.service.CommonJpaService;
import net.airuima.wip.domain.procedure.WipVerificateDetail;
import net.airuima.wip.repository.procedure.WipVerificateDetailRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存盘点单历史明细Service
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WipVerificateDetailService extends CommonJpaService<WipVerificateDetail> {

    private static final String WIP_VERIFICATE_DETAIL_ENTITY_GRAPH = "wipVerificateDetailEntityGraph";
    private final WipVerificateDetailRepository wipVerificateDetailRepository;

    public WipVerificateDetailService(WipVerificateDetailRepository wipVerificateDetailRepository) {
        this.wipVerificateDetailRepository = wipVerificateDetailRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipVerificateDetail> find(Specification<WipVerificateDetail> spec, Pageable pageable) {
        return wipVerificateDetailRepository.findAll(spec, pageable,new NamedEntityGraph(WIP_VERIFICATE_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WipVerificateDetail> find(Specification<WipVerificateDetail> spec) {
        return wipVerificateDetailRepository.findAll(spec,new NamedEntityGraph(WIP_VERIFICATE_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipVerificateDetail> findAll(Pageable pageable) {
        return wipVerificateDetailRepository.findAll(pageable,new NamedEntityGraph(WIP_VERIFICATE_DETAIL_ENTITY_GRAPH));
    }

    /**
     * 查看盘点单单明细
     * <AUTHOR>
     * @param verificateId 盘点单历史id
     * @return List<WipVerificate>
     **/
    @FetchMethod
    @Transactional(readOnly = true)
    public List<WipVerificateDetail> queryDetailByVerificateId(Long verificateId) {
        return wipVerificateDetailRepository.findByWipVerificateIdAndDeleted(verificateId, Constants.LONG_ZERO);
    }
}
