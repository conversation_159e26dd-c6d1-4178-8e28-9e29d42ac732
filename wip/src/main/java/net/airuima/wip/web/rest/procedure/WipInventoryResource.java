package net.airuima.wip.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.query.QueryCondition;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.DateTimeUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.wip.domain.procedure.WipInventory;
import net.airuima.wip.dto.InventoryDTO;
import net.airuima.wip.service.procedure.WipInventoryService;
import net.airuima.wip.web.rest.procedure.dto.*;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存Resource
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Tag(name = "线边库存Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/wip-inventories")
@AuthorityRegion("线边仓管理")
@FuncInterceptor("SideWarehouse")
@AuthSkip("CD")
public class WipInventoryResource extends ProtectBaseResource<WipInventory> {

    private final String EXPIRED = "expired";
    private final String EXPIRE_DATE = "expirationDate";

    private final WipInventoryService wipInventoryService;

    public WipInventoryResource(WipInventoryService wipInventoryService) {
        this.wipInventoryService = wipInventoryService;
        this.mapUri = "/api/wip-inventories";
    }

    /**
     * 重写查询接口
     *
     * @param pageable 分页参数
     * @param qcs      查询条件
     * @param request  HttpServletRequest
     * @deprecated
     */
    @Override
    @Deprecated
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<List<WipInventory>> searchQuery(Pageable pageable, @RequestBody List<QueryCondition> qcs, HttpServletRequest request) {
        if (ValidateUtils.isValid(qcs)) {
            Optional<QueryCondition> expiredOptional = qcs.stream().filter(queryCondition -> queryCondition.getFieldName().equals(EXPIRED)
                    && null != queryCondition.getFieldValue() && StringUtils.isNotBlank(String.valueOf(queryCondition.getFieldValue()))
                    && Boolean.parseBoolean(String.valueOf(queryCondition.getFieldValue()))).findFirst();
            expiredOptional.ifPresent(queryCondition -> {
                QueryCondition queryCondition1 = new QueryCondition();
                queryCondition1.setFieldName(EXPIRE_DATE);
                queryCondition1.setFieldValue(DateTimeUtil.localDate2String(LocalDate.now()));
                queryCondition1.setOperator("NLTES");
                queryCondition1.setIgnoreCase(Boolean.FALSE);
                qcs.add(queryCondition1);
                qcs.remove(queryCondition);
            });
            if (expiredOptional.isEmpty()) {
                Optional<QueryCondition> notExpiredOptional = qcs.stream().filter(queryCondition -> queryCondition.getFieldName().equals(EXPIRED)
                        && null != queryCondition.getFieldValue() && StringUtils.isNotBlank(String.valueOf(queryCondition.getFieldValue()))
                        && !Boolean.parseBoolean(String.valueOf(queryCondition.getFieldValue()))).findFirst();
                notExpiredOptional.ifPresent(queryCondition -> {
                    QueryCondition queryCondition1 = new QueryCondition();
                    queryCondition1.setFieldName(EXPIRE_DATE);
                    queryCondition1.setFieldValue(DateTimeUtil.localDate2String(LocalDate.now()));
                    queryCondition1.setOperator("NGTES");
                    queryCondition1.setIgnoreCase(Boolean.FALSE);
                    qcs.add(queryCondition1);
                    qcs.remove(queryCondition);
                });
            }
        }
        return super.searchQuery(pageable, qcs, request);
    }

    /**
     * 更新库存，写台账记录
     *
     * @param inventoryDTO 库存记录
     * @return WipInventory
     * <AUTHOR>
     **/
    @Operation(summary = "更改线边库存")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @PutMapping("/inventory")
    public ResponseEntity<ResponseData<WipInventory>> put(@Valid @RequestBody InventoryDTO inventoryDTO) throws URISyntaxException {
        try {
            WipInventory result = wipInventoryService.updateInventoryInfo(inventoryDTO);
            return ResponseData.ok(result);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 根据线边仓id、物料id、模糊批次查询线边库存记录
     *
     * @param warehouseId 线边仓id
     * @param materialId  物料id
     * @param batch       批次（模糊匹配）
     * @param size        数量
     * @return Page<WipInventory>
     * <AUTHOR>
     **/
    @Parameters({
            @Parameter(name = "warehouseId", description = "线边仓id", required = true),
            @Parameter(name = "materialId", description = "物料id", required = true),
            @Parameter(name = "batch", description = "批次", required = true),
            @Parameter(name = "size", description = "数量", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "根据线边仓id、物料id、批次模糊查询线边库存")
    @GetMapping("/byMaterialinfoLike")
    public ResponseEntity<ResponseData<List<WipInventory>>> queryInventoryInfoLike(@RequestParam(value = "warehouseId") Long warehouseId, @RequestParam(value = "materialId") Long materialId
            , @RequestParam(value = "batch") String batch, @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(wipInventoryService.findByWarehouseIdAndMaterialIdAndBatchContaining(warehouseId, materialId, batch, size));
    }

    /**
     * 根据线边仓id、物料id、模糊批次查询线边库存记录
     *
     * @param warehouseId 线边仓id
     * @param materialId  物料id
     * @param batch       批次（等值匹配）
     * @return Page<WipInventory>
     * <AUTHOR>
     **/
    @Parameters({
            @Parameter(name = "warehouseId", description = "线边仓id", required = true),
            @Parameter(name = "materialId", description = "物料id", required = true),
            @Parameter(name = "batch", description = "批次", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "根据线边仓id、物料id、批次模糊查询线边库存")
    @GetMapping("/byMaterialinfoEq")
    public ResponseEntity<ResponseData<WipInventory>> queryInventoryInfoEq(@RequestParam(value = "warehouseId") Long warehouseId, @RequestParam(value = "materialId") Long materialId
            , @RequestParam(value = "batch") String batch) {
        try {
            return ResponseData.ok(wipInventoryService.findByWarehouseIdAndMaterialIdAndBatch(warehouseId, materialId, batch));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 线边库存数量最低 TOP10
     *
     * @param size 数量
     * @return List<WipInventory>
     * <AUTHOR>
     **/
    @Parameters({
            @Parameter(name = "workLineId", description = "产线id", required = true),
            @Parameter(name = "size", description = "数量", required = false)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "线边库存数量最低 TOP10")
    @GetMapping("/sortByLeftNumber")
    public ResponseEntity<ResponseData<List<WipInventory>>> queryInventorySortByLeftNumber(@RequestParam(value = "workLineId") Long workLineId, @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        return ResponseData.ok(wipInventoryService.findAllSortByLeftNumber(workLineId, size));
    }

    /**
     * 根据物料id 获取线边仓
     *
     * @return WipInventoryDTO
     **/
    @Parameters({
            @Parameter(name = "materialId", description = "物料id", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "根据物料id 获取线边仓")
    @GetMapping("/material-house")
    public ResponseEntity<ResponseData<WipInventoryDTO>> queryInventoryHouseByMaterial(@RequestParam(value = "materialId") Long materialId) {
        return ResponseData.ok(wipInventoryService.queryInventoryHouseByMaterial(materialId));
    }

    /**
     * 通过线边仓回去剩余物料大于0 的线边库存物料信息
     *
     * @param id 线边仓id
     * @return List<MaterialDTO>
     * <AUTHOR>
     * @date 2023/1/29
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @Operation(summary = "通过线边仓回去剩余物料大于0 的线边库存物料信息")
    @GetMapping("/warehouse/{id}")
    public ResponseEntity<ResponseData<List<MaterialDTO>>> queryInventoryMaterialByWarehouse(@PathVariable("id") Long id) {
        return ResponseData.ok(wipInventoryService.queryInventoryMaterialByWarehouse(id));
    }

    /**
     * 通过物料名称或编码模糊查询线边仓库存列表
     *
     * @param text 物料名称或者编码
     * @param text 条数
     * @return List<WipInventoryDTO>
     * <AUTHOR>
     * @date 2023/03/20
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过物料名称或编码模糊查询线边仓库存列表")
    @GetMapping("/material-code-or-name")
    public ResponseEntity<ResponseData<List<WipInventoryDTO>>> findByMaterialCodeOrName(@RequestParam(value = "text") String text,
                                                                                        @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(wipInventoryService.findByMaterialCodeOrName(text, size));
    }

    /**
     * 通过物料id、仓位id、批次名称或者编码 获取线边仓库存批次列表
     *
     * @param batchesGetDto 获取线边库存批次信息请求dto
     * @return List<String>
     * <AUTHOR>
     * @date 2023/03/20
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过物料id、仓位id、批次名称或者编码 获取线边仓库存批次列表")
    @PostMapping("/batches")
    public ResponseEntity<ResponseData<List<String>>> getBatches(@RequestBody BatchesGetDTO batchesGetDto) {
        return ResponseData.ok(wipInventoryService.getBatches(batchesGetDto));
    }

    /**
     * 通过线边仓id 物料id 入库批次 获取唯一 线边库存信息
     *
     * @param wipMaterialBatchHouseDto 线边库存唯一性dto
     * @return net.airuima.rbase.domain.procedure.wip.WipInventory
     * <AUTHOR>
     * @date 2023/4/10
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过物料id、仓位id、批次名称或者编码 获取线边仓库存")
    @PostMapping("/material-batch-house")
    public ResponseEntity<ResponseData<WipInventory>> findByMaterialIdAndBatchAndHouseId(@RequestBody WipMaterialBatchHouseDTO wipMaterialBatchHouseDto) {
        return ResponseData.ok(wipInventoryService.findByMaterialIdAndBatchAndHouseId(wipMaterialBatchHouseDto));
    }

    /**
     * 线边仓先进先出
     * 获取系统配置的扫描模式：
     * 0: 不管控，只验证扫描信息的合法性；
     * 1: 一般模式，验证扫入合法性，给出推荐最先入库的线边库存，但用户可以不采纳;
     * 2: 强制模式，验证扫入合法性，给出推荐最先入库的线边库存，用户必须采纳;
     *
     * @param wipInventoryFifoDTO 入库先进先出dto
     * @return WipInventoryFifoResDTO
     * <AUTHOR>
     * @since 1.8.1
     */
    @PostMapping("/fifo")
    public ResponseEntity<ResponseData<WipInventoryFifoResDTO>> fifoInventory(@RequestBody WipInventoryFifoDTO wipInventoryFifoDTO) {
        try {
            return ResponseData.ok(wipInventoryService.fifoInventory(wipInventoryFifoDTO));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览线边库存";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建线边库存";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改线边库存";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除线边库存";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入线边库存";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出线边库存";
        }
        return "";
    }

}
