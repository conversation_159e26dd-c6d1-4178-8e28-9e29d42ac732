package net.airuima.wip.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/03/21
 */
@Schema(description = "线边库存物料对应的仓库以及批次DTO")
public class WipInventoryDTO {

    @Schema(description = "物料名称")
    private String materialName;

    @Schema(description = "物料编码")
    private String materialCode;

    @Schema(description = "物料Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    @Schema(description = "仓库列表")
    private List<WipWarehouseDTO> wipWarehouseDtoList;

    public static class WipWarehouseDTO{

        @Schema(description = "仓库名称")
        private String wipWarehouseName;

        @Schema(description = "仓库编码")
        private String wipWarehouseCode;

        @Schema(description = "仓库id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long wipWarehouseId;
        
        public String getWipWarehouseName() {
            return wipWarehouseName;
        }

        public WipWarehouseDTO setWipWarehouseName(String wipWarehouseName) {
            this.wipWarehouseName = wipWarehouseName;
            return this;
        }

        public String getWipWarehouseCode() {
            return wipWarehouseCode;
        }

        public WipWarehouseDTO setWipWarehouseCode(String wipWarehouseCode) {
            this.wipWarehouseCode = wipWarehouseCode;
            return this;
        }

        public Long getWipWarehouseId() {
            return wipWarehouseId;
        }

        public WipWarehouseDTO setWipWarehouseId(Long wipWarehouseId) {
            this.wipWarehouseId = wipWarehouseId;
            return this;
        }
    }

    public String getMaterialName() {
        return materialName;
    }

    public WipInventoryDTO setMaterialName(String materialName) {
        this.materialName = materialName;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public WipInventoryDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WipInventoryDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public List<WipWarehouseDTO> getWipWarehouseDtoList() {
        return wipWarehouseDtoList;
    }

    public WipInventoryDTO setWipWarehouseDtoList(List<WipWarehouseDTO> wipWarehouseDtoList) {
        this.wipWarehouseDtoList = wipWarehouseDtoList;
        return this;
    }
}
