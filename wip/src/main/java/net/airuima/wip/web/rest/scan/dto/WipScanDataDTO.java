package net.airuima.wip.web.rest.scan.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 线边仓扫码核验基础数据
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(description = "线边仓扫码核验基础数据")
public class WipScanDataDTO implements Serializable {

    /**
     * 线边仓编码
     */
    @Schema(description = "线边仓编码")
    private String wipWarehouseCode;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    public String getWipWarehouseCode() {
        return wipWarehouseCode;
    }

    public WipScanDataDTO setWipWarehouseCode(String wipWarehouseCode) {
        this.wipWarehouseCode = wipWarehouseCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public WipScanDataDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }
}
