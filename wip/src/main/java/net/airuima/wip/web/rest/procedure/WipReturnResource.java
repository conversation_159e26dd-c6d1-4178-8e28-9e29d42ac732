package net.airuima.wip.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.wip.domain.procedure.WipReturn;
import net.airuima.wip.service.procedure.WipReturnService;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.net.URISyntaxException;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓退料记录Resource
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Tag(name = "线边仓退料记录Resource")
@RestController
@RequestMapping("/api/wip-returns")
@AuthorityRegion("线边仓管理")
@FuncInterceptor("SideWarehouse")
public class WipReturnResource extends ProtectBaseResource<WipReturn> {

    private final WipReturnService wipReturnService;

    public WipReturnResource(WipReturnService wipReturnService) {
        this.wipReturnService = wipReturnService;
        this.mapUri = "/api/wip-returns";
    }

    /**
     * @description 新增退料入库
     * <AUTHOR>
     * @param entity 退料记录
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.procedure.wip.WipReturn>
     **/
    @Operation(summary = "退料")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @Override
    public ResponseEntity<WipReturn> create(@Valid @RequestBody WipReturn entity) throws URISyntaxException {
        try {
            WipReturn result = wipReturnService.createReturnInfo(entity);
            return (ResponseEntity.created(new URI(this.mapUri + "/" + result.getId())).headers(HeaderUtil.createdAlert(this.entityName, result.getId().toString()))).body(result);
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览线边仓退料记录";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建线边仓退料记录";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改线边仓退料记录";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除线边仓退料记录";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入线边仓退料记录";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出线边仓退料记录";
        }
        return "";
    }

}
