package net.airuima.wip.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.wip.dto.VerificateDTO;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import net.airuima.wip.domain.procedure.WipVerificate;
import net.airuima.wip.service.procedure.WipVerificateService;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URISyntaxException;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存盘点历史Resource
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Tag(name = "线边库存盘点历史Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/wip-verificates")
@AuthorityRegion("线边仓管理")
@FuncInterceptor("SideWarehouse")
@AuthSkip("IE")
public class WipVerificateResource extends ProtectBaseResource<WipVerificate> {

    private final WipVerificateService wipVerificateService;

    public WipVerificateResource(WipVerificateService wipVerificateService) {
        this.wipVerificateService = wipVerificateService;
        this.mapUri = "/api/wip-verificates";
    }

    /**
     * @description 新增盘点单历史
     * <AUTHOR>
     * @param verificateDTO 盘点单历史
     * @return ResponseEntity<ResponseData<WipVerificate>>
     **/
    @Operation(summary = "新增盘点单")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @PostMapping("/verificate")
    public ResponseEntity<ResponseData<WipVerificate>> create(@Valid @RequestBody VerificateDTO verificateDTO) throws URISyntaxException {
        try {
            WipVerificate result = wipVerificateService.createVerificateInfo(verificateDTO);
            return ResponseData.ok(result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览线边库存盘点";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建线边库存盘点";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改线边库存盘点";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除线边库存盘点";
        }
        return "";
    }

}
