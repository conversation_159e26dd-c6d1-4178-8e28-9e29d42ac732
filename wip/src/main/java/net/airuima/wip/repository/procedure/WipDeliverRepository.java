package net.airuima.wip.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.wip.domain.procedure.WipDeliver;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓出库表（工序上料，自动扣料）Repository
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Repository
public interface WipDeliverRepository extends LogicDeleteableRepository<WipDeliver>,
        EntityGraphJpaSpecificationExecutor<WipDeliver>, EntityGraphJpaRepository<WipDeliver, Long> {

    /**
     * @param wipWarehouseId 线边仓主键id
     * @return void
     * @description 根据线边仓主键id删除（逻辑删除）
     * <AUTHOR>
     **/
    @Modifying
    @Query(value = "update WipDeliver wd set wd.deleted = wd.id where wd.wipWarehouse.id=?1")
    void deleteByWipWarehouseId(Long wipWarehouseId);

    /**
     * 根据领用批次号获取出库记录
     *
     * @param deliverBatch 领用批次号
     * @param deleted      累加shan
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipDeliver> 线边仓出库列表
     * <AUTHOR>
     * @date 2023/3/7
     */
    @DataFilter(isSkip = true)
    List<WipDeliver> findByDeliverBatchAndDeleted(String deliverBatch, Long deleted);

    /**
     * t通过线边仓主键ID、物料主键ID、入库批次、出库批次及逻辑删除获取出库记录
     * @param wipWareHouseId 线边仓主键ID
     * @param materialId     物料主键ID
     * @param originBatch    入库批次
     * @param deliverBatch   出库批次
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipDeliver> 线边仓出库列表
     */
    @DataFilter(isSkip = true)
    List<WipDeliver> findByWipWarehouseIdAndMaterialIdAndOriginBatchAndDeliverBatchAndDeleted(Long wipWareHouseId, Long materialId, String originBatch, String deliverBatch, Long deleted);
}
