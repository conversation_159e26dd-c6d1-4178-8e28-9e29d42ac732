package net.airuima.wip.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.wip.domain.base.WipWarehouse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓Repository
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Repository
public interface WipWarehouseRepository extends LogicDeleteableRepository<WipWarehouse>,
        EntityGraphJpaSpecificationExecutor<WipWarehouse>, EntityGraphJpaRepository<WipWarehouse, Long> {

    /**
     * 禁用、启用线边仓
     * @param isEnable 是否启用
     * @param id 主键id
     */
    @Modifying
    @Query("update WipWarehouse ww set ww.isEnable=?2 where ww.id=?1")
    void updateEnableById(Long id, Boolean isEnable);

    /**
     * @description 根据id查询有效的线边仓记录
     * <AUTHOR>
     * @param id 主键
     * @param isEnable 是否启用
     * @param deleted 删除标志
     * @return net.airuima.rbase.domain.base.wip.WipWarehouse  线边仓
     **/
    @DataFilter(isSkip = true)
    WipWarehouse findByIdAndIsEnableAndDeleted(Long id, Boolean isEnable, Long deleted);

    /**
     * @description 根据线边仓名称或者编码，模糊查询线边仓记录
     * <AUTHOR>
     * @param text 线边仓编码
     * @param isEnable 是否启用
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.wip.WipWarehouse> 线边仓分页
     **/
    @FetchMethod
    @Query("select wipWarehouse from WipWarehouse wipWarehouse where (wipWarehouse.name like concat('%',?1,'%') or wipWarehouse.code like concat('%',?1,'%') ) and wipWarehouse.isEnable=?2 and wipWarehouse.deleted = ?3")
    Page<WipWarehouse> findByNameOrCode(String text, Boolean isEnable, Long deleted, Pageable pageable);

    /**
     * @description 根据产线id查询线边仓记录
     * <AUTHOR>
     * @param workLineId 产线id
     * @param isEnable 是否启用
     * @param deleted 删除标志
     * @return net.airuima.rbase.domain.base.wip.WipWarehouse  线边仓
     **/
    @DataFilter(isSkip = true)
    WipWarehouse findByWorkLineIdAndIsEnableAndDeleted(Long workLineId, Boolean isEnable, Long deleted);

    /**
     * 根据线边仓编码获取线边信息
     * @param code 线边仓编码
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2023/2/3
     * @return net.airuima.rbase.domain.base.wip.WipWarehouse  线边仓
     */
    @DataFilter(isSkip = true)
    WipWarehouse findByCodeAndDeleted(String code,Long deleted);
}
