package net.airuima.wip.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存盘点单历史明细Domain
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Schema(name = "线边库存盘点单历史明细(WipVerificateDetail)", description = "线边库存盘点单历史明细")
@Entity
@Table(name = "procedure_wip_verificate_detail", uniqueConstraints = {
        @UniqueConstraint(name = "procedure_wip_verificate_detail_unique_index", columnNames = {"verificate_id", "material_id", "batch", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wipVerificateDetailEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "wipVerificate",subgraph = "wipVerificateEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "wipVerificateEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "wipWarehouse",subgraph = "wipWarehouseEntityGraph"),
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "wipWarehouseEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})})
public class WipVerificateDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 盘点单历史
     */
    @Schema(description = "盘点单历史")
    @ManyToOne
    @JoinColumn(name = "verificate_id")
    private WipVerificate wipVerificate;

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    @Column(name = "material_id")
    private Long materialId;

    /**
     * 物料DTO
     */
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 批次
     */
    @Schema(description = "批次")
    @Column(name = "batch")
    private String batch;

    /**
     * 系统库存数量
     */
    @NotNull
    @Schema(description = "系统库存数量", required = true)
    @Column(name = "system_number", nullable = false)
    private BigDecimal systemNumber;

    /**
     * 实盘数量
     */
    @NotNull
    @Schema(description = "实盘数量", required = true)
    @Column(name = "actual_number", nullable = false)
    private BigDecimal actualNumber;

    /**
     * 盘亏/盘盈
     */
    @NotNull
    @Schema(description = "盘亏/盘盈", required = true)
    @Column(name = "differ_number", nullable = false)
    private BigDecimal differNumber;


    public String getBatch() {
        return batch;
    }

    public WipVerificateDetail setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public BigDecimal getSystemNumber() {
        return systemNumber;
    }

    public WipVerificateDetail setSystemNumber(BigDecimal systemNumber) {
        this.systemNumber = systemNumber;
        return this;
    }

    public BigDecimal getActualNumber() {
        return actualNumber;
    }

    public WipVerificateDetail setActualNumber(BigDecimal actualNumber) {
        this.actualNumber = actualNumber;
        return this;
    }

    public BigDecimal getDifferNumber() {
        return differNumber;
    }

    public WipVerificateDetail setDifferNumber(BigDecimal differNumber) {
        this.differNumber = differNumber;
        return this;
    }

    public WipVerificate getWipVerificate() {
        return wipVerificate;
    }

    public WipVerificateDetail setWipVerificate(WipVerificate wipVerificate) {
        this.wipVerificate = wipVerificate;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WipVerificateDetail setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WipVerificateDetail setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WipVerificateDetail wipVerificateDetail = (WipVerificateDetail) o;
        if (wipVerificateDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wipVerificateDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
