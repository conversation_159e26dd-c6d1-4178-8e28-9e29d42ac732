package net.airuima.wip.domain.base;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓Domain
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Schema(name = "线边仓(WipWarehouse)", description = "线边仓")
@Entity
@Table(name = "base_wip_warehouse", uniqueConstraints = {
        @UniqueConstraint(name = "base_wip_warehouse_unique_index", columnNames = {"code", "deleted"}),
        @UniqueConstraint(name = "base_wip_warehouse_work_line_id_unique_index", columnNames = {"work_line_id", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wipWarehouseEntityGraph",attributeNodes = {
        @NamedAttributeNode("workLine")})
public class WipWarehouse extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线边仓编码
     */
    @NotNull
    @Schema(description = "线边仓编码", required = true)
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 线边仓名称
     */
    @NotNull
    @Schema(description = "线边仓名称", required = true)
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 生产线
     */
    @Schema(description = "生产线")
    @ManyToOne
    @JoinColumn(name = "work_line_id")
    private WorkLine workLine;

    /**
     * 负责人
     */
    @NotNull
    @Schema(description = "负责人", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id", nullable = false)
    private Long operatorId;

    /**
     * 操作人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 是否启用(0:否;1:是)
     */
    @Schema(description = "是否启用(0:否;1:是)", required = true)
    @Column(name = "is_enable", nullable = false)
    @Forbidden
    private boolean isEnable;

    public String getCode() {
        return code;
    }

    public WipWarehouse setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public WipWarehouse setName(String name) {
        this.name = name;
        return this;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public WipWarehouse setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public WipWarehouse setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public WipWarehouse setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public WipWarehouse setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WipWarehouse wipWarehouse = (WipWarehouse) o;
        if (wipWarehouse.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wipWarehouse.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
