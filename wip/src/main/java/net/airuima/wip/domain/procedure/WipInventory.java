package net.airuima.wip.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.wip.domain.base.WipWarehouse;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存Domain
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Schema(name = "线边库存(WipInventory)", description = "线边库存")
@Entity
@Table(name = "procedure_wip_inventory", uniqueConstraints = {
        @UniqueConstraint(name = "procedure_wip_inventory_unique_index", columnNames = {"warehouse_id", "material_id", "batch", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wipInventoryEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "wipWarehouse",subgraph = "wipWarehouseEntityGraph")},
        subgraphs = {@NamedSubgraph(name = "wipWarehouseEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workLine")})})
public class WipInventory extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线边仓
     */
    @NotNull
    @Schema(description = "线边仓", required = true)
    @ManyToOne
    @JoinColumn(name = "warehouse_id", nullable = false)
    private WipWarehouse wipWarehouse;

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    @Column(name = "material_id")
    private Long materialId;

    /**
     * 物料DTO
     */
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 批次
     */
    @Schema(description = "批次")
    @Column(name = "batch")
    private String batch;

    /**
     * 领料总数
     */
    @NotNull
    @Schema(description = "领料总数", required = true)
    @Column(name = "number", nullable = false)
    private BigDecimal number;

    /**
     * 剩余数量
     */
    @NotNull
    @Schema(description = "剩余数量", required = true)
    @Column(name = "left_number", nullable = false)
    private BigDecimal leftNumber;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    @Column(name = "produce_date")
    private LocalDate produceDate;

    /**
     * 截止有效期
     */
    @Schema(description = "截止有效期")
    @Column(name = "expiration_date")
    private LocalDate expirationDate;

    /**
     * 是否过期 0:未过期，1：过期
     */
    @Schema(description = "是否过期 0:未过期，1：过期")
    @Column(name = "expired")
    private boolean expired;

    /**
     * 保质期(天)
     */
    @Schema(description = "保质期(天)")
    @Column(name = "expire_day")
    private int expireDay;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    public LocalDate getProduceDate() {
        return produceDate;
    }

    public WipInventory setProduceDate(LocalDate produceDate) {
        this.produceDate = produceDate;
        return this;
    }

    public LocalDate getExpirationDate() {
        return expirationDate;
    }

    public WipInventory setExpirationDate(LocalDate expirationDate) {
        this.expirationDate = expirationDate;
        return this;
    }

    public boolean getExpired() {
        if(null != this.expirationDate && this.expirationDate.isBefore(LocalDate.now())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public WipInventory setExpired(boolean expired) {
        this.expired = expired;
        return this;
    }


    public int getExpireDay() {
        return expireDay;
    }

    public WipInventory setExpireDay(int expireDay) {
        this.expireDay = expireDay;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WipInventory setNote(String note) {
        this.note = note;
        return this;
    }

    public WipWarehouse getWipWarehouse() {
        return wipWarehouse;
    }

    public WipInventory setWipWarehouse(WipWarehouse wipWarehouse) {
        this.wipWarehouse = wipWarehouse;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WipInventory setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WipInventory setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public WipInventory setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public WipInventory setNumber(BigDecimal number) {
        this.number = number;
        return this;
    }

    public BigDecimal getLeftNumber() {
        return leftNumber;
    }

    public WipInventory setLeftNumber(BigDecimal leftNumber) {
        this.leftNumber = leftNumber;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WipInventory wipInventory = (WipInventory) o;
        if (wipInventory.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wipInventory.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
