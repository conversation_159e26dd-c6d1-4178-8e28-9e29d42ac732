package net.airuima.wip.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/11
 */
@Schema(description = "胶水线边出库DTO")
public class GlueDeliverDTO {

    /**
     * 线边仓
     */
    @Schema(description = "线边仓", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wipWarehouseId;

    /**
     * 出库人id
     */
    @Schema(description = "出库人id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;

    /**
     *  领用部门
     */
    @Schema(description = "领用部门", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organizationId;

    /**
     *  领用人
     */
    @Schema(description = "领用人id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long receiverId;

    /**
     * 物料明细集合
     */
    @Schema(description = "物料明细集合", required = true)
    private DeliverDTO.MaterialDetailDTO materialDetailDto;

    public Long getWipWarehouseId() {
        return wipWarehouseId;
    }

    public GlueDeliverDTO setWipWarehouseId(Long wipWarehouseId) {
        this.wipWarehouseId = wipWarehouseId;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public GlueDeliverDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public GlueDeliverDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public GlueDeliverDTO setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
        return this;
    }

    public DeliverDTO.MaterialDetailDTO getMaterialDetailDto() {
        return materialDetailDto;
    }

    public GlueDeliverDTO setMaterialDetailDto(DeliverDTO.MaterialDetailDTO materialDetailDto) {
        this.materialDetailDto = materialDetailDto;
        return this;
    }
}
