package net.airuima.wip.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.wip.WipWarehouseDTO;
import net.airuima.wip.domain.base.WipWarehouse;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台账DTO
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
@Schema(description = "台账DTO")
public class LedgerDTO {

    /**
     * 线边仓
     */
    @Schema(description = "线边仓")
    private WipWarehouse wipWarehouse;

    /**
     * 入库(原始)批次
     */
    @Schema(description = "入库(原始)批次")
    private String originBatch;

    /**
     * 出库（领料）批次
     */
    @Schema(description = "出库（领料）批次")
    private String deliverBatch;

    /**
     * 物料id
     */
    @Schema(description = "物料id")
    private Long materialId;

    /**
     * 类型（1：工单出库；2：更改库存；3：领料入库；4:退料入库；5：盘盈入库；6：盘亏出库；7：工单入库；8: 领料出库）
     */
    @Schema(description = "类型（1：工单出库；2：更改库存；3：领料入库；4:退料入库；5：盘盈入库；6：盘亏出库；7：工单入库；8: 领料出库）")
    private Integer operation;

    /**
     * 库存类型（1：线边库存；2：工单库存；）
     */
    @Schema(description = "库存类型（1：线边库存；2：工单库存；）")
    private Integer category;

    /**
     * 关联工单
     */
    @Schema(description = "关联工单id")
    private WorkSheet workSheet;

    /**
     * 操作人id
     */
    @Schema(description = "操作人id", required = true)
    private Long operatorId;

    /**
     * 数量
     */
    @Schema(description = "改变数量")
    private BigDecimal changeNumber;


    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    private LocalDate produceDate;

    /**
     * 保质期(天)
     */
    @Schema(description = "保质期(天)")
    private Integer expireDay;


    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String note;



    public LocalDate getProduceDate() {
        return produceDate;
    }

    public LedgerDTO setProduceDate(LocalDate produceDate) {
        this.produceDate = produceDate;
        return this;
    }

    public Integer getExpireDay() {
        return expireDay;
    }

    public LedgerDTO setExpireDay(Integer expireDay) {
        this.expireDay = expireDay;
        return this;
    }

    public String getNote() {
        return note;
    }

    public LedgerDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public WipWarehouse getWipWarehouse() {
        return wipWarehouse;
    }

    public LedgerDTO setWipWarehouse(WipWarehouse wipWarehouse) {
        this.wipWarehouse = wipWarehouse;
        return this;
    }

    public LedgerDTO setWipWarehouse(WipWarehouseDTO wipWarehouse) {
        this.wipWarehouse = new WipWarehouse();
        this.wipWarehouse.setId(wipWarehouse.getId());
        this.wipWarehouse.setDeleted(wipWarehouse.getDeleted());
        this.wipWarehouse.setCreatedBy(wipWarehouse.getCreatedBy());
        this.wipWarehouse.setCreatedDate(wipWarehouse.getCreatedDate());
        this.wipWarehouse.setLastModifiedBy(wipWarehouse.getLastModifiedBy());
        this.wipWarehouse.setLastModifiedDate(wipWarehouse.getLastModifiedDate());
        this.wipWarehouse.setCustom1(wipWarehouse.getCustom1());
        this.wipWarehouse.setCustom2(wipWarehouse.getCustom2());
        this.wipWarehouse.setCustom3(wipWarehouse.getCustom3());
        this.wipWarehouse.setCustom4(wipWarehouse.getCustom4());
        this.wipWarehouse.setCustom5(wipWarehouse.getCustom5());
        this.wipWarehouse.setCode(wipWarehouse.getCode())
                .setName(wipWarehouse.getName())
                .setWorkLine(wipWarehouse.getWorkLine())
                .setOperatorId(wipWarehouse.getOperatorId())
                .setOperatorDto(wipWarehouse.getOperatorDto())
                .setIsEnable(wipWarehouse.isEnable());
        return this;
    }

    public String getOriginBatch() {
        return originBatch;
    }

    public LedgerDTO setOriginBatch(String originBatch) {
        this.originBatch = originBatch;
        return this;
    }

    public String getDeliverBatch() {
        return deliverBatch;
    }

    public LedgerDTO setDeliverBatch(String deliverBatch) {
        this.deliverBatch = deliverBatch;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public LedgerDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public Integer getOperation() {
        return operation;
    }

    public LedgerDTO setOperation(Integer operation) {
        this.operation = operation;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public LedgerDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public BigDecimal getChangeNumber() {
        return changeNumber;
    }

    public LedgerDTO setChangeNumber(BigDecimal changeNumber) {
        this.changeNumber = changeNumber;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public LedgerDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public LedgerDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }
}
