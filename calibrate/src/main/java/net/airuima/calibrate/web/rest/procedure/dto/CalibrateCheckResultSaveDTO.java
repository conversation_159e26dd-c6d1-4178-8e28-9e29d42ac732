package net.airuima.calibrate.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则表Domain
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Schema(name = "校准数据（内校）新增DTO", description = "校准数据（内校）新增DTO")
public class CalibrateCheckResultSaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 校准数据ID
     */
    @Schema(description = "校准数据ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工位ID
     */
    @Schema(description = "工位ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workCellId;

    /**
     * 标准件ID
     */
    @Schema(description = "标准件ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardPartId;

    /**
     * 标准件CODE
     */
    @Schema(description = "标准件CODE")
    private String standardPartCode;

    /**
     * 校准项目列表
     **/
    @Schema(description = "校校准数据（内校）内部类", required = true)
    private List<CalibrateCheckResultSave> calibrateCheckResultSaveList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public void setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
    }

    public Long getStandardPartId() {
        return standardPartId;
    }

    public void setStandardPartId(Long standardPartId) {
        this.standardPartId = standardPartId;
    }

    public String getStandardPartCode() {
        return standardPartCode;
    }

    public void setStandardPartCode(String standardPartCode) {
        this.standardPartCode = standardPartCode;
    }

    public List<CalibrateCheckResultSave> getCalibrateCheckResultSaveList() {
        return calibrateCheckResultSaveList;
    }

    public void setCalibrateCheckResultSaveList(List<CalibrateCheckResultSave> calibrateCheckResultSaveList) {
        this.calibrateCheckResultSaveList = calibrateCheckResultSaveList;
    }

    /**
     * 校校准数据（内校）内部类
     **/
    @Schema(name = "校准数据（内校）内部类", description = "校准数据（内校）内部类")
    public static class CalibrateCheckResultSave implements Serializable {
        /**
         * 规则详情表ID
         */
        @Schema(description = "规则详情表ID", required = true)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 工位ID
         */
        @Schema(description = "工位ID", required = true)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long workCellId;

        /**
         * 设备ID
         */
        @Schema(description = "设备ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long facilityId;

        /**
         * 校准次数
         */
        @Schema(description = "校准次数，第几次")
        private Integer testTimes;

        /**
         * 测试值（1：值，2：OK/NG）
         */
        @Schema(description = "测试值，1:2.2 2:OK/NG")
        private String number;


        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Integer getTestTimes() {
            return testTimes;
        }

        public void setTestTimes(Integer testTimes) {
            this.testTimes = testTimes;
        }

        public String getNumber() {
            return number;
        }

        public void setNumber(String number) {
            this.number = number;
        }

        public Long getWorkCellId() {
            return workCellId;
        }

        public void setWorkCellId(Long workCellId) {
            this.workCellId = workCellId;
        }

        public Long getFacilityId() {
            return facilityId;
        }

        public void setFacilityId(Long facilityId) {
            this.facilityId = facilityId;
        }
    }
}
