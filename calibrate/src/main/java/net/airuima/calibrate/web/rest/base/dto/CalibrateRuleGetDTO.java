package net.airuima.calibrate.web.rest.base.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.rfms.FacilityDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则表Domain
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Schema(description = "校准数据_查询关联设备及项目DTO")
public class CalibrateRuleGetDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 设备列表
     **/
    @Schema(description = "设备列表", requiredMode = Schema.RequiredMode.REQUIRED, implementation = FacilityDTO.class)
    private FacilityDTO facilityDto;

    /**
     * 校准项目列表
     **/
    @ArraySchema(schema = @Schema(implementation = CalibrateRuleDTO.class, description = "校准项目"))
    private List<CalibrateRuleDTO> calibrateRuleDtoList;

    public CalibrateRuleGetDTO() {
    }

    public CalibrateRuleGetDTO(FacilityDTO facilityDto, List<CalibrateRuleDTO> calibrateRuleDtoList) {
        this.facilityDto = facilityDto;
        this.calibrateRuleDtoList = calibrateRuleDtoList;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public void setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
    }

    public List<CalibrateRuleDTO> getCalibrateRuleDtoList() {
        return calibrateRuleDtoList;
    }

    public void setCalibrateRuleDtoList(List<CalibrateRuleDTO> calibrateRuleDtoList) {
        this.calibrateRuleDtoList = calibrateRuleDtoList;
    }

    /**
     * 校准规则内部类
     **/
    @Schema(name = "校准规则内部类", description = "校准规则内部类")
    public static class CalibrateRuleDTO implements Serializable {
        /**
         * 规则详情表ID
         */
        @Schema(description = "规则详情表ID", required = true)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 工位ID
         */
        @Schema(description = "工位ID", required = true)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long workCellId;

        /**
         * 设备ID
         */
        @Schema(description = "设备ID", required = true)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long facilityId;

        /**
         * 设备Code
         */
        @Schema(description = "设备Code", required = true)
        private String facilityCode;

        /**
         * 设备名称
         */
        @Schema(description = "设备名称", required = true)
        private String facilityName;

        /**
         * 项目名称
         */
        @Schema(description = "项目名称", required = true)
        private String name;

        /**
         * 校准次数
         */
        @Schema(description = "校准次数")
        private Integer testTimes;

        /**
         * 校准标准，0：参考基值，1：是否正常
         */
        @Schema(description = "校准标准，0：参考基值，1：是否正常", required = true)
        private Integer category;

        /**
         * 范围，(10,50]
         */
        @Schema(description = "范围，(10,50]")
        private String qualifiedRange;

        /**
         * 单位
         */
        @Schema(description = "单位")
        private String unit;

        public Long getId() {
            return id;
        }

        public CalibrateRuleDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getFacilityCode() {
            return facilityCode;
        }

        public void setFacilityCode(String facilityCode) {
            this.facilityCode = facilityCode;
        }

        public Long getWorkCellId() {
            return workCellId;
        }

        public CalibrateRuleDTO setWorkCellId(Long workCellId) {
            this.workCellId = workCellId;
            return this;
        }

        public Long getFacilityId() {
            return facilityId;
        }

        public CalibrateRuleDTO setFacilityId(Long facilityId) {
            this.facilityId = facilityId;
            return this;
        }

        public String getFacilityName() {
            return facilityName;
        }

        public CalibrateRuleDTO setFacilityName(String facilityName) {
            this.facilityName = facilityName;
            return this;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getTestTimes() {
            return testTimes;
        }

        public CalibrateRuleDTO setTestTimes(Integer testTimes) {
            this.testTimes = testTimes;
            return this;
        }

        public Integer getCategory() {
            return category;
        }

        public CalibrateRuleDTO setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public String getQualifiedRange() {
            return qualifiedRange;
        }

        public CalibrateRuleDTO setQualifiedRange(String qualifiedRange) {
            this.qualifiedRange = qualifiedRange;
            return this;
        }

        public String getUnit() {
            return unit;
        }

        public CalibrateRuleDTO setUnit(String unit) {
            this.unit = unit;
            return this;
        }
    }
}
