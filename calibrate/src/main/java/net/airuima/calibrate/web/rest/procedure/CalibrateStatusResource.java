package net.airuima.calibrate.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.calibrate.domain.procedure.CalibrateStatus;
import net.airuima.calibrate.service.procedure.CalibrateStatusService;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备校准状态表Resource
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Tag(name = "设备校准状态表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/calibrate-statuses")
@AuthorityRegion("工位设备校准")
@FuncInterceptor("FBase && FCalibration")
@AuthSkip("IECUD")
public class CalibrateStatusResource extends ProtectBaseResource<CalibrateStatus> {

    private final CalibrateStatusService calibrateStatusService;

    public CalibrateStatusResource(CalibrateStatusService calibrateStatusService) {
        this.calibrateStatusService = calibrateStatusService;
        this.mapUri = "/api/calibrate-statuses";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览设备校准状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建设备校准状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改设备校准状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除设备校准状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入设备校准状态";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出设备校准状态";
        }
        return "";
    }

}
