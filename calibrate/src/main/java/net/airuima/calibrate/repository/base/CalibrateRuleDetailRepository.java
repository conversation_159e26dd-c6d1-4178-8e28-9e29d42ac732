package net.airuima.calibrate.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.calibrate.domain.base.CalibrateRule;
import net.airuima.calibrate.domain.base.CalibrateRuleDetail;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则详情表Repository
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@Repository
public interface CalibrateRuleDetailRepository extends LogicDeleteableRepository<CalibrateRuleDetail>,
        EntityGraphJpaSpecificationExecutor<CalibrateRuleDetail>, EntityGraphJpaRepository<CalibrateRuleDetail, Long> {

    /**
     * 通过设备校准规则主键ID列表查询启用的规则明细集合
     * @param ruleIdList  校准规则主键ID集合
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.calibrate.CalibrateRuleDetail> 校准规则详情列表
     * <AUTHOR>
     * @date 2024/1/18
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select crd from CalibrateRuleDetail crd where crd.calibrateRule.id in (?1) and crd.calibrateItem.enable = true and crd.calibrateItem.deleted = 0L and crd.deleted = ?2")
    List<CalibrateRuleDetail> findByCalibrateRuleIdInAndCalibrateItemEnableAndDeleted(List<Long> ruleIdList, Long deleted);

    /**
     * 根据规则主键ID、项目主键Id及删除标识删除规则明细
     *
     * @param calibrateRuleId     规则主键ID
     * @param calibrateItemIdList 项目主键ID集合
     * <AUTHOR>
     * @date 2022/7/14
     **/
    @Modifying
    @Query("UPDATE CalibrateRuleDetail rd SET rd.deleted=rd.id WHERE rd.calibrateRule.id=?1 AND rd.calibrateItem.id in (?2) AND rd.deleted=0")
    void deleteByCalibrateRuleIdAndCalibrateItemIdInAndDeleted(Long calibrateRuleId, List<Long> calibrateItemIdList);

    /**
     * 根据规则主键ID 、删除标识删除规则明细
     *
     * @param calibrateRuleId 规则主键ID
     * <AUTHOR>
     * @date 2022/7/14
     **/
    @Modifying
    @Query("UPDATE CalibrateRuleDetail rd SET rd.deleted=rd.id WHERE rd.calibrateRule.id=?1 AND rd.deleted=0")
    void deleteByCalibrateRuleIdAndDeleted(Long calibrateRuleId);

    /**
     * 根据工位主键ID、设备主键ID为空 校准程序 + 项目是否启用 + 规则删除状态 查询 规则
     *
     * @param workCellId 工位主键ID
     * @param process    校准程序
     * @param enable     项目是否启用
     * @param deleted    删除状态
     * @return : java.util.List<net.airuima.rbase.domain.base.calibrate.CalibrateRule>  校准规则列表
     * <AUTHOR>
     * @date 2022/7/22
     **/
    @DataFilter(isSkip = true)
    @Query("select crd.calibrateRule from CalibrateRuleDetail crd where crd.calibrateRule.workCell.id = ?1 and crd.calibrateRule.facilityId is null and crd.calibrateRule.process = ?2 and crd.calibrateItem.enable = ?3 and crd.deleted = ?4")
    List<CalibrateRule> findByWorkCellIdAndFacilityIdIsNullAndProcessAndItemEnabledAndDeleted(Long workCellId, Integer process, Boolean enable, Long deleted);


    /**
     * 根据工位主键ID + 设备主键ID集合 + 校准程序 + 项目是否启用 + 规则删除状态 查询 规则
     *
     * @param workCellId     工位主键ID
     * @param facilityIdList 设备主键ID集合
     * @param process        校准程序
     * @param enable         项目是否启用
     * @param deleted        删除状态
     * @return : java.util.List<net.airuima.rbase.domain.base.calibrate.CalibrateRule> 校准规则列表
     * <AUTHOR>
     * @date 2022/7/22
     **/
    @DataFilter(isSkip = true)
    @Query("select crd.calibrateRule from CalibrateRuleDetail crd where crd.calibrateRule.workCell.id = ?1 and crd.calibrateRule.facilityId in (?2) and crd.calibrateRule.process = ?3 and crd.calibrateItem.enable = ?4 and crd.deleted = ?5")
    List<CalibrateRule> findByWorkCellIdAndFacilityIdAndProcessAndItemEnabledAndDeleted(Long workCellId, List<Long> facilityIdList, Integer process, Boolean enable, Long deleted);
}
