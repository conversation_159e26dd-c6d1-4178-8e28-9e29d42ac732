package net.airuima.calibrate.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.calibrate.domain.procedure.CalibrateCheckResultDetail;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位校准历史数据明细Repository
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Repository
public interface CalibrateCheckResultDetailRepository extends LogicDeleteableRepository<CalibrateCheckResultDetail>,
        EntityGraphJpaSpecificationExecutor<CalibrateCheckResultDetail>, EntityGraphJpaRepository<CalibrateCheckResultDetail, Long> {

    /**
     * 通过台位校准历史主键ID获取明细列表
     * @param checkResultId 台位校准历史主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.calibrate.CalibrateCheckResultDetail> 台位校准历史数据明细列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CalibrateCheckResultDetail> findByCalibrateCheckResultIdAndDeleted(Long checkResultId,Long deleted);

    /**
     * 通过台位校准历史主键ID批量逻辑删除明细数据
     * @param checkResultId 位校准历史主键ID
     */
    @Modifying
    @Query("update CalibrateCheckResultDetail set deleted=id where calibrateCheckResult.id=?1 and deleted=0 ")
    void batchDeleteCalibrateCheckResultDetailByCheckResultId(Long checkResultId);
}
