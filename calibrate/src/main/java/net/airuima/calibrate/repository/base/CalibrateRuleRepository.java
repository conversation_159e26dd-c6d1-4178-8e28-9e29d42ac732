package net.airuima.calibrate.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.calibrate.domain.base.CalibrateRule;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则表Repository
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Repository
public interface CalibrateRuleRepository extends LogicDeleteableRepository<CalibrateRule>,
        EntityGraphJpaSpecificationExecutor<CalibrateRule>, EntityGraphJpaRepository<CalibrateRule, Long> {

    /**
     * 根据工位主键ID及校准程序查询校准规则
     *
     * @param workCellId 工位主键ID
     * @param process    校准程序，0：内校，1：外校
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.calibrate.CalibrateRule> 校准规则列表
     * <AUTHOR>
     * @date 2022/7/8
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CalibrateRule> findByWorkCellIdAndProcessAndDeleted(Long workCellId, Integer process, Long deleted);

    /**
     * 根据工位主键ID+设备主键ID+删除标识+不等于当前规则ID查询校准规则
     *
     * @param workCellId 工位主键ID
     * @param facilityId 设备主键ID
     * @param process    校准程序，0：内校，1：外校
     * @param deleted    删除标识
     * @param id         规则主键ID
     * @return net.airuima.rbase.domain.base.calibrate.CalibrateRule 校准规则
     * <AUTHOR>
     * @date 2022/7/11
     **/
    @DataFilter(isSkip = true)
    CalibrateRule findByWorkCellIdAndFacilityIdAndProcessAndDeletedAndIdNot(Long workCellId, Long facilityId, Integer process, Long deleted, Long id);

    /**
     * 根据工位主键ID+设备主键ID为null+删除标识+不等于当前规则ID 查询规则
     *
     * @param workCellId 工位主键ID
     * @param process    校准程序，0：内校，1：外校
     * @param deleted    删除标识
     * @param id         规则主键ID
     * @return net.airuima.rbase.domain.base.calibrate.CalibrateRule 校准规则
     * <AUTHOR>
     * @date 2022/7/11
     **/
    @DataFilter(isSkip = true)
    CalibrateRule findByWorkCellIdAndFacilityIdIsNullAndProcessAndDeletedAndIdNot(Long workCellId, Integer process, Long deleted, Long id);

    /**
     * 根据工位主键ID+设备主键ID+删除标识 查询校准规则
     *
     * @param workCellId 工位主键ID
     * @param facilityId 设备主键ID集合
     * @param process    校准程序，0：内校，1：外校
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.calibrate.CalibrateRule> 校准规则列表
     * <AUTHOR>
     * @date 2022/7/11
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CalibrateRule> findByWorkCellIdAndFacilityIdInAndProcessAndDeleted(Long workCellId, List<Long> facilityId, Integer process, Long deleted);

    /**
     * 根据工位主键ID+设备主键ID为空+删除标识 查询校准规则
     *
     * @param workCellId 工位主键ID
     * @param process    校准程序，0：内校，1：外校
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.calibrate.CalibrateRule> 校准规则列表
     * <AUTHOR>
     * @date 2022/7/11
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CalibrateRule> findByWorkCellIdAndFacilityIdIsNullAndProcessAndDeleted(Long workCellId, Integer process, Long deleted);

    /**
     * 通过工位主键ID、设备主键ID列表查询校准规则
     * @param workCellId 工位主键ID
     * @param facilityIds 设备主键ID列表
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.calibrate.CalibrateRule> 校准规则列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CalibrateRule> findByWorkCellIdAndFacilityIdInAndDeleted(Long workCellId, List<Long> facilityIds, Long deleted);

    /**
     *
     * 通过工位主键ID获取没有设备的校准规则
     * @param workCellId 工位主键ID
     * @param deleted  逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.calibrate.CalibrateRule> 校准规则列表
     */
    @DataFilter(isSkip = true)
    List<CalibrateRule> findByWorkCellIdAndFacilityIdIsNullAndDeleted(Long workCellId,Long deleted);

    /**
     * 通过 工位主键id + 设备主键id + 校准程序 获取唯一规则记录
     * @param workCellId 工位主键id
     * @param facilityId 设备主键id
     * @param process 校准程序
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.calibrate.CalibrateRule> 校准规则
     */
    @DataFilter(isSkip = true)
    Optional<CalibrateRule> findByWorkCellIdAndFacilityIdAndProcessAndDeleted(Long workCellId,Long facilityId,Integer process,Long deleted);
}
