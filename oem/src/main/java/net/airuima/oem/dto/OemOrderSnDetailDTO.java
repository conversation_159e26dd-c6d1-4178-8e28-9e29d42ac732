package net.airuima.oem.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "外协收货获取可收货信息详情")
public class OemOrderSnDetailDTO implements Serializable {

    /**
     * 剩余数量
     */
    @Schema(description = "剩余数量")
    private Integer remainingNumber;

    /**
     * 剩余入库sn列表
     */
    @Schema(description = "剩余入库sn列表")
    private List<String> snList;

    public Integer getRemainingNumber() {
        return remainingNumber;
    }

    public OemOrderSnDetailDTO setRemainingNumber(Integer remainingNumber) {
        this.remainingNumber = remainingNumber;
        return this;
    }

    public List<String> getSnList() {
        return snList;
    }

    public OemOrderSnDetailDTO setSnList(List<String> snList) {
        this.snList = snList;
        return this;
    }
}
