package net.airuima.oem.dto.rworker;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "获取外协质检方案")
public class RworkerOemInspectionPlanDTO implements Serializable {

    /**
     * 任务单号id
     */
    @Schema(description = "任务单号id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    /**
     * 员工id
     */
    @Schema(description = "员工id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    /**
     * 检查项目id
     */
    @Schema(description = "检查项目id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long varietyId;

    public Long getTaskId() {
        return taskId;
    }

    public RworkerOemInspectionPlanDTO setTaskId(Long taskId) {
        this.taskId = taskId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public RworkerOemInspectionPlanDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public RworkerOemInspectionPlanDTO setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }
}
