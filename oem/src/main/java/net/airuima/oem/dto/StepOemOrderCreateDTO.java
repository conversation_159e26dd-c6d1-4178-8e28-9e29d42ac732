package net.airuima.oem.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "工序外协创建DTO")
public class StepOemOrderCreateDTO implements Serializable {

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "投产工单id")
    private Long productId;

    @Schema(description = "外协工序id")
    private Long stepId;

    @Schema(description = "外协工单号")
    private String serialNumber;

    @Schema(description = "计划完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planEndTime;

    @Schema(description = "加工数量")
    private Integer stepProcessNumber;

    @Schema(description = "备注")
    private String note;

    public Long getSupplierId() {
        return supplierId;
    }

    public StepOemOrderCreateDTO setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public Long getProductId() {
        return productId;
    }

    public StepOemOrderCreateDTO setProductId(Long productId) {
        this.productId = productId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public StepOemOrderCreateDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public StepOemOrderCreateDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public LocalDateTime getPlanEndTime() {
        return planEndTime;
    }

    public StepOemOrderCreateDTO setPlanEndTime(LocalDateTime planEndTime) {
        this.planEndTime = planEndTime;
        return this;
    }

    public Integer getStepProcessNumber() {
        return stepProcessNumber;
    }

    public StepOemOrderCreateDTO setStepProcessNumber(Integer stepProcessNumber) {
        this.stepProcessNumber = stepProcessNumber;
        return this;
    }

    public String getNote() {
        return note;
    }

    public StepOemOrderCreateDTO setNote(String note) {
        this.note = note;
        return this;
    }
}
