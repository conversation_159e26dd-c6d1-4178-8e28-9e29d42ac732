package net.airuima.oem.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.oem.domain.procedure.OemReceiptHistoryDetail;
import net.airuima.oem.service.procedure.OemReceiptHistoryDetailService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "外协收货历史明细Resource")
@RestController
@RequestMapping("/api/oem-receipt-history-details")
@AppKey("OemService")
@AuthorityRegion("外协Oem")
@FuncInterceptor("StepOem")
public class OemReceiptHistoryDetailResource extends ProtectBaseResource<OemReceiptHistoryDetail> {

    private final OemReceiptHistoryDetailService oemReceiptHistoryDetailService;

    public OemReceiptHistoryDetailResource(OemReceiptHistoryDetailService oemReceiptHistoryDetailService) {
        this.oemReceiptHistoryDetailService = oemReceiptHistoryDetailService;
        this.mapUri = "/api/oem-receipt-history-details";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "外协收货历史明细");
    }
}
