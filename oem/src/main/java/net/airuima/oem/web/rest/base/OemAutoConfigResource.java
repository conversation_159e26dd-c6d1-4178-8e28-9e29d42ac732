package net.airuima.oem.web.rest.base;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.oem.domain.base.OemAutoConfig;
import net.airuima.oem.service.base.OemAutoConfigService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "工序外协自动分单配置Resource")
@RestController
@RequestMapping("/api/oem-auto-configs")
@AppKey("OemService")
@AuthorityRegion("外协Oem")
@FuncInterceptor("StepOem")
public class OemAutoConfigResource extends ProtectBaseResource<OemAutoConfig> {

    private final OemAutoConfigService oemAutoConfigService;

    public OemAutoConfigResource(OemAutoConfigService oemAutoConfigService) {
        this.oemAutoConfigService = oemAutoConfigService;
        this.mapUri = "/oem-auto-configs";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "工序外协自动分单配置");
    }

}
