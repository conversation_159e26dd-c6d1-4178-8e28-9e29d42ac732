package net.airuima.oem.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.oem.domain.procedure.OemOrderSn;
import net.airuima.oem.service.procedure.OemOrderSnService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "外协订单绑定SNResource")
@RestController
@RequestMapping("/api/oem-order-sns")
@AppKey("OemService")
@AuthorityRegion("外协Oem")
@FuncInterceptor("StepOem")
public class OemOrderSnResource extends ProtectBaseResource<OemOrderSn> {

    private final OemOrderSnService oemOrderSnService;

    public OemOrderSnResource(OemOrderSnService oemOrderSnService) {
        this.oemOrderSnService = oemOrderSnService;
        this.mapUri = "/api/oem-order-sns";
    }



    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "外协订单绑定SN");
    }
}
