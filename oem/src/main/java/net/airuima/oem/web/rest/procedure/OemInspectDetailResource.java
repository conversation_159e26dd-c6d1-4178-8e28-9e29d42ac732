package net.airuima.oem.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.oem.domain.procedure.OemInspectDetail;
import net.airuima.oem.dto.OemInspectDetailGetDTO;
import net.airuima.oem.service.procedure.OemInspectDetailService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "外协质检结果明细表Resource")
@RestController
@RequestMapping("/api/oem-inspect-details")
@AppKey("OemService")
@AuthorityRegion("外协Oem")
@FuncInterceptor("StepOem")
public class OemInspectDetailResource extends ProtectBaseResource<OemInspectDetail> {

    private final OemInspectDetailService oemInspectDetailService;

    public OemInspectDetailResource(OemInspectDetailService oemInspectDetailService) {
        this.oemInspectDetailService = oemInspectDetailService;
        this.mapUri = "/api/oem-inspect-details";
    }

    /**
     * 获取外协质检结果明细
     *
     * @param oemInspectId 外协质检结果ID
     * @return OemInspectDetailGetDTO
     */
    @GetMapping("/oemInspectId/{oemInspectId}")
    public ResponseEntity<ResponseData<OemInspectDetailGetDTO>> getOemInspectDetail(@PathVariable("oemInspectId") Long oemInspectId) {
        try {
            return ResponseData.ok(oemInspectDetailService.getOemInspectDetail(oemInspectId));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "外协质检结果明细");
    }
}
