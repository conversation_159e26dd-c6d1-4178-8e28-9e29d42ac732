package net.airuima.oem.service.procedure;

import net.airuima.constant.Constants;
import net.airuima.oem.domain.procedure.*;
import net.airuima.oem.dto.OemReceiptHistorySaveDTO;
import net.airuima.oem.repository.procedure.*;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class OemReceiptHistoryService extends CommonJpaService<OemReceiptHistory> {

    private static final String oemInspectSerialNumberKey = "key_oem_inspect_serial_number";

    private final OemReceiptHistoryRepository oemReceiptHistoryRepository;
    @Autowired
    private OemOrderRepository oemOrderRepository;
    @Autowired
    private OemOrderSnRepository oemOrderSnRepository;
    @Autowired
    private OemReceiptHistoryDetailRepository oemReceiptHistoryDetailRepository;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private OemInspectRepository oemInspectRepository;
    @Autowired
    private OemInspectWarehouseRepository oemInspectWarehouseRepository;

    public OemReceiptHistoryService(OemReceiptHistoryRepository oemReceiptHistoryRepository) {
        this.oemReceiptHistoryRepository = oemReceiptHistoryRepository;
    }

    @Override
    @FetchMethod
    public Page<OemReceiptHistory> find(Specification<OemReceiptHistory> spec, Pageable pageable) {
        return oemReceiptHistoryRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<OemReceiptHistory> find(Specification<OemReceiptHistory> spec) {
        return oemReceiptHistoryRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<OemReceiptHistory> findAll(Pageable pageable) {
        return oemReceiptHistoryRepository.findAll(pageable);
    }


    /**
     * 工单外协创建收货记录
     *
     * @param oemReceiptHistorySaveDto 收货数据
     */
    public void createOemReceiptHistory(OemReceiptHistorySaveDTO oemReceiptHistorySaveDto) {

        OemOrder oemOrder = oemOrderRepository.findByIdAndDeleted(oemReceiptHistorySaveDto.getOemOrderId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("OemOrderIdNotFound", "外协工单不存在"));

        if (oemOrder.getWorkSheet().getNumber() < (oemOrder.getFinishNumber() + oemReceiptHistorySaveDto.getNumber())) {
            throw new ResponseException("FinishNumberExceedInputNumber", "入库数量超过投产数量");
        }
        //存在sn需要验证当前sn是否属于源投产工单，并且当前sn不能是已通过原工单质检的
        validReceiptSnData(oemOrder, oemReceiptHistorySaveDto.getSnList(),oemReceiptHistorySaveDto.getNumber());

        //创建外协收货记录
        OemReceiptHistory oemReceiptHistory = new OemReceiptHistory();
        oemReceiptHistory.setOemOrder(oemOrder)
                .setQuantity(oemReceiptHistorySaveDto.getNumber())
                .setReceiptTime(LocalDateTime.now())
                .setOperatorId(oemReceiptHistorySaveDto.getOperatorId());
        oemReceiptHistory = oemReceiptHistoryRepository.save(oemReceiptHistory);

        if (ValidateUtils.isValid(oemReceiptHistorySaveDto.getSnList())) {
            OemReceiptHistory finalOemReceiptHistory = oemReceiptHistory;
            List<OemReceiptHistoryDetail> oemReceiptHistoryDetails = oemReceiptHistorySaveDto.getSnList().stream().distinct().map(sn -> {
                OemReceiptHistoryDetail oemReceiptHistoryDetail = new OemReceiptHistoryDetail();
                return oemReceiptHistoryDetail.setSn(sn).setOemReceiptHistory(finalOemReceiptHistory);
            }).collect(Collectors.toList());
            oemReceiptHistoryDetailRepository.saveAll(oemReceiptHistoryDetails);
        }
        //创建外协收货质检记录
        createOemInspectTask(oemReceiptHistory);
    }

    /**
     * 验证外协收货sn 的合法性
     *
     * @param oemOrder 外协工单
     * @param snList   收货sn列表
     */
    public void validReceiptSnData(OemOrder oemOrder, List<String> snList,Integer number) {
        if (!ValidateUtils.isValid(snList)){
            return;
        }
        if (snList.size() != number){
            throw new ResponseException("errorBindOemOrderSns", "绑定sn数量与收货数量不一致");
        }
        List<OemOrderSn> oemOrderSns = Objects.nonNull(oemOrder.getOriginalSubWorkSheet()) ?
                oemOrderSnRepository.findByOemOrderOriginalSubWorkSheetIdAndOemOrderStepIdAndDeleted(oemOrder.getOriginalSubWorkSheet().getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO) :
                oemOrderSnRepository.findByOemOrderOriginalWorkSheetIdAndOemOrderStepIdAndDeleted(oemOrder.getOriginalWorkSheet().getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO);

        //验证当前sn是否属于源投产工单
        if (ValidateUtils.isValid(oemOrderSns)) {
            List<String> bindSns = oemOrderSns.stream().map(OemOrderSn::getSn).distinct().toList();
            List<String> errorSns = snList.stream().filter(sn -> bindSns.stream().noneMatch(bindSn -> bindSn.equals(sn)))
                    .toList();
            if (ValidateUtils.isValid(errorSns)) {
                throw new ResponseException("errorBindOemOrderSns", "绑定sn不属于原投产工单" + errorSns.stream().collect(Collectors.joining(",")));
            }
        }
        //获取 所有投产工单+工序 已入库的sn - 当前sn不能是已通过原工单质检的
        List<String> oemInspectWareHouseSnList = Objects.nonNull(oemOrder.getOriginalSubWorkSheet()) ? oemInspectWarehouseRepository.findOemOrderOriginalSubWorkSheetIdAndOemOrderStepIdAndDeletedBySns(oemOrder.getOriginalSubWorkSheet().getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO):
                oemInspectWarehouseRepository.findOemOrderOriginalWorkSheetIdAndOemOrderStepIdAndDeletedBySns(oemOrder.getOriginalWorkSheet().getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(oemInspectWareHouseSnList)) {
            List<String> errorSns = snList.stream().filter(sn -> oemInspectWareHouseSnList.stream().anyMatch(oemInspectWareHouseSn -> oemInspectWareHouseSn.equals(sn)))
                    .toList();
            if (ValidateUtils.isValid(errorSns)) {
                throw new ResponseException("errorBindOemOrderSns", "绑定sn已通过原投产工单质检" + errorSns.stream().collect(Collectors.joining(",")));
            }
        }
    }

    /**
     * 外协收货创建外协质检记录
     *
     * @param oemReceiptHistory 外协收货历史
     */
    public void createOemInspectTask(OemReceiptHistory oemReceiptHistory) {
        OemInspect oemInspect = new OemInspect();
        SerialNumberDTO serialNumberDto = new SerialNumberDTO();
        serialNumberDto.setCode(oemInspectSerialNumberKey).setCustomDate(LocalDate.now());
        oemInspect.setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto))
                .setStatus(Constants.INT_ZERO)
                .setOemReceiptHistory(oemReceiptHistory)
                .setNumber(oemReceiptHistory.getQuantity())
                .setArrivalTime(LocalDateTime.now())
                .setDealWay(Constants.INT_ZERO);
        oemInspectRepository.save(oemInspect);
    }
}
