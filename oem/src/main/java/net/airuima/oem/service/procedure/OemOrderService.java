package net.airuima.oem.service.procedure;

import net.airuima.constant.Constants;
import net.airuima.oem.domain.base.OemAutoConfig;
import net.airuima.oem.domain.procedure.OemOrder;
import net.airuima.oem.domain.procedure.OemOrderSn;
import net.airuima.oem.domain.procedure.OemReceiptHistory;
import net.airuima.oem.domain.procedure.OemReceiptHistoryDetail;
import net.airuima.oem.dto.OemOrderSnDetailDTO;
import net.airuima.oem.dto.OemOrderUpdateDTO;
import net.airuima.oem.dto.StepOemOrderCreateDTO;
import net.airuima.oem.dto.StepOemRemainingNumberDTO;
import net.airuima.oem.repository.procedure.*;
import net.airuima.oem.service.base.OemAutoConfigService;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.StepCategoryEnum;
import net.airuima.rbase.constant.WorkSheetCategoryEnum;
import net.airuima.rbase.constant.WorkSheetStatusEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.quality.InspectTaskService;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Transactional(rollbackFor = Exception.class)
public class OemOrderService extends CommonJpaService<OemOrder> {

    private final OemOrderRepository oemOrderRepository;

    @Autowired
    private OemAutoConfigService oemAutoConfigService;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private InspectTaskService inspectTaskService;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private OemOrderSnRepository oemOrderSnRepository;
    @Autowired
    private OemReceiptHistoryDetailRepository oemReceiptHistoryDetailRepository;
    @Autowired
    private OemOrderSnService oemOrderSnService;
    @Autowired
    private OemInspectWarehouseRepository oemInspectWarehouseRepository;
    @Autowired
    private OemInspectRepository oemInspectRepository;

    public OemOrderService(OemOrderRepository oemOrderRepository) {
        this.oemOrderRepository = oemOrderRepository;
    }

    @Override
    @FetchMethod
    public Page<OemOrder> find(Specification<OemOrder> spec, Pageable pageable) {
        return oemOrderRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<OemOrder> find(Specification<OemOrder> spec) {
        return oemOrderRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<OemOrder> findAll(Pageable pageable) {
        return oemOrderRepository.findAll(pageable);
    }

    /**
     * 获取 投产工单 已工序外协数量
     *
     * @param workSheetId    工单
     * @param subWorkSheetId 子工单
     * @param stepId         工序
     * @return 工序外协数量
     */
    public Integer originalStepProcessNumber(Long workSheetId, Long subWorkSheetId, Long stepId) {
        Integer processNumber = Objects.nonNull(subWorkSheetId) ?
                oemOrderRepository.sumOemOrderWorkSheetNumberByStepIdAndOriginalSubWorkSheetIdAndDeleted(stepId, subWorkSheetId, Constants.LONG_ZERO) :
                oemOrderRepository.sumOemOrderWorkSheetNumberByStepIdAndOriginalWorkSheetIdAndDeleted(stepId, workSheetId, Constants.LONG_ZERO);
        return Objects.isNull(processNumber) || processNumber == Constants.INT_ZERO ? Constants.INT_ZERO : processNumber;
    }


    /**
     * 递归创建工序外协工单
     *
     * @param workSheet         工单
     * @param subWorkSheet      子工单
     * @param step              外协工序
     * @param stepProcessNumber 剩余外协数量
     * @param isFinished        是否上道工序完成
     */
    public void generateStepProcessOemOrder(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step step, List<String> currentStepOemBindSns, Integer stepProcessNumber, Boolean isFinished) {
        Pedigree pedigree = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getPedigree() : workSheet.getPedigree();

        OemAutoConfig oemAutoConfig = oemAutoConfigService.findOemAutoConfigByPedigree(pedigree);
        // 不分单
        if (Objects.isNull(oemAutoConfig)) {
            return;
        }
        int splitNumber = oemAutoConfig.getSplitNumber();
        if (splitNumber <= 0) {
            return; // 分单数必须大于0
        }

        int remaining = stepProcessNumber != null ? stepProcessNumber : 0;
        if (remaining <= 0) {
            return; // 无需分单
        }
        if (isFinished) {
            // 工序完成：剩余数量<=分单数则分一单，否则递归
            if (remaining <= splitNumber) {
                // 处理剩余数量，绑定对应的SN
                List<String> currentBatchSns = getCurrentBatchSns(currentStepOemBindSns, remaining);
                OemOrder oemOrder = createProcessStepOemOrder(remaining, workSheet, subWorkSheet, step, oemAutoConfig);
                createProcessStepOemOrderBindSn(oemOrder, currentBatchSns);
            } else {
                // 分单处理，递归继续
                List<String> currentBatchSns = getCurrentBatchSns(currentStepOemBindSns, splitNumber);
                OemOrder oemOrder = createProcessStepOemOrder(splitNumber, workSheet, subWorkSheet, step, oemAutoConfig);
                createProcessStepOemOrderBindSn(oemOrder, currentBatchSns);
                //剩余sn
                List<String> remainingSns = getRemainingSns(currentStepOemBindSns, splitNumber);
                generateStepProcessOemOrder(workSheet, subWorkSheet, step, remainingSns, remaining - splitNumber, isFinished);
            }
        } else {
            // 工序未完成：剩余数量>分单数则递归分单
            if (remaining >= splitNumber) {
                List<String> currentBatchSns = getCurrentBatchSns(currentStepOemBindSns, splitNumber);
                OemOrder oemOrder = createProcessStepOemOrder(splitNumber, workSheet, subWorkSheet, step, oemAutoConfig);
                createProcessStepOemOrderBindSn(oemOrder, currentBatchSns);
                List<String> remainingSns = getRemainingSns(currentStepOemBindSns, splitNumber);
                generateStepProcessOemOrder(workSheet, subWorkSheet, step, remainingSns, remaining - splitNumber, isFinished);
            }
        }
    }

    /**
     * 获取指定批次数量的可绑定sn
     *
     * @param sns       sn列表
     * @param batchSize 批次数量
     * @return sn列表
     */
    private List<String> getCurrentBatchSns(List<String> sns, int batchSize) {
        if (!ValidateUtils.isValid(sns) || batchSize <= 0) {
            return Collections.emptyList();
        }
        int endIndex = Math.min(batchSize, sns.size());
        return new ArrayList<>(sns.subList(0, endIndex));
    }

    /**
     * 获取剩余批次数量的可绑定sn
     *
     * @param sns       sn列表
     * @param processed 剩余数量
     * @return sn列表
     */
    private List<String> getRemainingSns(List<String> sns, int processed) {
        if (!ValidateUtils.isValid(sns) || processed <= 0) {
            return Collections.emptyList();
        }
        int startIndex = Math.min(processed, sns.size());
        return new ArrayList<>(sns.subList(startIndex, sns.size()));
    }

    /**
     * 创建工序外协单(生成过程中创建)
     *
     * @param quantity             投产数
     * @param originalWorkSheet    原始单据
     * @param originalSubWorkSheet 原始子单据
     * @param step                 外协工序
     * @param oemAutoConfig        工序外协配置
     * @return OemOrder
     */
    private OemOrder createProcessStepOemOrder(int quantity, WorkSheet originalWorkSheet, SubWorkSheet originalSubWorkSheet, Step step, OemAutoConfig oemAutoConfig) {
        OemOrder oemOrder = new OemOrder();
        WorkSheet baseWorkSheet = createBaseWorkSheet(originalWorkSheet, quantity, oemAutoConfig);
        oemOrder.setWorkSheet(baseWorkSheet)
                .setStep(step)
                .setOriginalWorkSheet(originalWorkSheet)
                .setOriginalSubWorkSheet(originalSubWorkSheet)
                .setCategory(Constants.INT_ONE)
                .setFinishNumber(Constants.INT_ZERO)
                .setDeleted(Constants.LONG_ZERO);
        return oemOrderRepository.save(oemOrder);
    }

    /**
     * 工序生成过程中外协单绑定sn
     *
     * @param oemOrder              外协订单
     * @param currentStepOemBindSns 绑定sn 列表
     */
    private void createProcessStepOemOrderBindSn(OemOrder oemOrder, List<String> currentStepOemBindSns) {
        if (ValidateUtils.isValid(currentStepOemBindSns)) {
            List<OemOrderSn> oemOrderSns = currentStepOemBindSns.stream().map(currentStepOemBindSn -> {
                OemOrderSn oemOrderSn = new OemOrderSn();
                return oemOrderSn.setOemOrder(oemOrder).setSn(currentStepOemBindSn);
            }).toList();
            oemOrderSnRepository.saveAll(oemOrderSns);
        }
    }

    /**
     * 创建外协关联的工单
     *
     * @param originalWorkSheet 原始工单
     * @param number            加工数量
     * @param oemAutoConfig     外协配置
     * @return 工单
     */
    public WorkSheet createBaseWorkSheet(WorkSheet originalWorkSheet, Integer number, OemAutoConfig oemAutoConfig) {
        WorkSheet workSheet = new WorkSheet();
        workSheet
                .setNumber(number)
                .setPlanStartDate(LocalDateTime.now())
                .setPlanEndDate(LocalDateTime.now().plusDays(oemAutoConfig.getPlanDay()))
                .setCategory(WorkSheetCategoryEnum.OEM_CATEGORY.getCategory())
                .setStatus(WorkSheetStatusEnum.DEVOTE.getStatus())
                .setPedigree(originalWorkSheet.getPedigree())
                .setWorkFlow(originalWorkSheet.getWorkFlow())
                .setOrganizationId(originalWorkSheet.getOrganizationId())
                .setWorkLine(originalWorkSheet.getWorkLine())
                .setBomInfoId(originalWorkSheet.getBomInfoId())
                .setClientId(originalWorkSheet.getClientId())
                .setDeleted(Constants.LONG_ZERO);

        SerialNumberDTO serialNumberDto = new SerialNumberDTO();
        serialNumberDto.setCode(WorkSheetCategoryEnum.OEM_CATEGORY.getKey()).setCustomDate(LocalDate.now());
        String serialNumber = rbaseSerialNumberProxy.generate(serialNumberDto);
        workSheet.setSerialNumber(serialNumber);
        return workSheetRepository.save(workSheet);
    }


    /**
     * 获取投产工单对应的工序外协中可手动创建的个数
     *
     * @param productId 投产工单id
     * @return List<StepOemRemainingNumberDTO>
     */
    public List<StepOemRemainingNumberDTO> findOemStepByProductId(Long productId) {

        boolean subWsProductionMode = commonService.subWsProductionMode();

        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findByIdAndDeleted(productId, Constants.LONG_ZERO).orElse(null) : null;
        if (Objects.isNull(subWorkSheet) && subWsProductionMode) {
            throw new ResponseException("productIdNotFind", "关联工单不存在");
        }
        WorkSheet workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : workSheetRepository.findByIdAndDeleted(productId, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(workSheet)) {
            throw new ResponseException("productIdNotFind", "关联工单不存在");
        }

        List<WsStep> wsSteps = subWsProductionMode ?
                wsStepRepository.findBySubWorkSheetIdAndCategoryAndDeleted(subWorkSheet.getId(), StepCategoryEnum.OEM_STEP.getStatus(), Constants.LONG_ZERO) :
                wsStepRepository.findByWorkSheetIdAndCategoryAndDeleted(workSheet.getId(), StepCategoryEnum.OEM_STEP.getStatus(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsSteps) && !subWsProductionMode) {
            throw new ResponseException("productWsStepNotFindOemStep", "关联工单未获取到外协工序");
        }
        if (!ValidateUtils.isValid(wsSteps)) {
            wsSteps = wsStepRepository.findByWorkSheetIdAndCategoryAndDeleted(workSheet.getId(), StepCategoryEnum.OEM_STEP.getStatus(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(wsSteps)) {
            throw new ResponseException("productWsStepNotFindOemStep", "关联工单未获取到外协工序");
        }

        List<StepOemRemainingNumberDTO> stepOemRemainingNumberDtoList = new ArrayList<>();
        //获取当前存在工序快照前置工序，并且需要验证前置工序不存在 待检任务(没有前置工序直接跳过，不支持第一道工序为外协工序)
        wsSteps.stream().filter(wsStep -> ValidateUtils.isValid(wsStep.getPreStepId())).forEach(wsStep -> {
            //获取当前工序剩余可外协工序数量
            int remaining = getOemStepRemainingNumber(subWorkSheet, workSheet, wsStep);
            if (remaining > 0) {
                stepOemRemainingNumberDtoList.add(new StepOemRemainingNumberDTO().setStep(wsStep.getStep()).setRemainingNumber(remaining));
            }
        });

        if (ValidateUtils.isValid(stepOemRemainingNumberDtoList)) {
            return stepOemRemainingNumberDtoList;
        }
        throw new ResponseException("notFind", "未获取到外协工序信息");
    }

    /**
     * 获取投产工单 对应的 工序 投产合格数量
     *
     * @param workSheetId    工单
     * @param subWorkSheetId 子工单
     * @param stepId         工序
     * @return 合格数量
     */
    public Integer getBatchWorkDetailQuantityNumber(Long workSheetId, Long subWorkSheetId, Long stepId) {
        BatchWorkDetail batchWorkDetail = Objects.nonNull(subWorkSheetId) ?
                batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheetId, stepId, Constants.LONG_ZERO).orElse(null) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheetId, stepId, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(batchWorkDetail)) {
            throw new ResponseException("batchWorkDetailNotFind", "外协前置工序工序详情不存在");
        }
        return batchWorkDetail.getQualifiedNumber();
    }

    /**
     * 手动创建工序外协单
     *
     * @param stepOemOrderCreateDto 工序外协单创建DTO
     * @return OemOrder
     */
    public OemOrder createStepOemOrder(StepOemOrderCreateDTO stepOemOrderCreateDto) {
        boolean subWsProductionMode = commonService.subWsProductionMode();

        SubWorkSheet originalSubWorkSheet = subWsProductionMode ? subWorkSheetRepository.findByIdAndDeleted(stepOemOrderCreateDto.getProductId(), Constants.LONG_ZERO).orElse(null) : null;
        if (Objects.isNull(originalSubWorkSheet) && subWsProductionMode) {
            throw new ResponseException("productIdNotFind", "关联工单不存在");
        }
        WorkSheet originalWorkSheet = subWsProductionMode ? originalSubWorkSheet.getWorkSheet() : workSheetRepository.findByIdAndDeleted(stepOemOrderCreateDto.getProductId(), Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(originalWorkSheet)) {
            throw new ResponseException("productIdNotFind", "关联工单不存在");
        }
        Step step = stepRepository.findByIdAndDeleted(stepOemOrderCreateDto.getStepId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("stepNotFind", "外协工序不存在"));

        //验证下交参数合法性
        validCreateOemOrder(originalSubWorkSheet, originalWorkSheet, step, stepOemOrderCreateDto.getStepProcessNumber());
        //创建工单
        WorkSheet workSheet = new WorkSheet();
        workSheet
                .setSerialNumber(stepOemOrderCreateDto.getSerialNumber())
                .setNumber(stepOemOrderCreateDto.getStepProcessNumber())
                .setPlanStartDate(LocalDateTime.now())
                .setPlanEndDate(stepOemOrderCreateDto.getPlanEndTime())
                .setCategory(WorkSheetCategoryEnum.OEM_CATEGORY.getCategory())
                .setStatus(WorkSheetStatusEnum.DEVOTE.getStatus())
                .setPedigree(originalWorkSheet.getPedigree())
                .setWorkFlow(originalWorkSheet.getWorkFlow())
                .setOrganizationId(originalWorkSheet.getOrganizationId())
                .setWorkLine(originalWorkSheet.getWorkLine())
                .setBomInfoId(originalWorkSheet.getBomInfoId())
                .setClientId(originalWorkSheet.getClientId())
                .setNote(stepOemOrderCreateDto.getNote())
                .setDeleted(Constants.LONG_ZERO);
        workSheet = workSheetRepository.save(workSheet);

        OemOrder oemOrder = new OemOrder();
        oemOrder.setWorkSheet(workSheet)
                .setSupplierId(stepOemOrderCreateDto.getSupplierId())
                .setStep(step)
                .setOriginalWorkSheet(originalWorkSheet)
                .setOriginalSubWorkSheet(originalSubWorkSheet)
                .setCategory(Constants.INT_ONE)
                .setFinishNumber(Constants.INT_ZERO)
                .setDeleted(Constants.LONG_ZERO);
        oemOrderRepository.save(oemOrder);
        //获取绑定sn
        List<String> snList = oemOrderGetBindSn(oemOrder);
        if (ValidateUtils.isValid(snList)) {
            if (snList.size() < workSheet.getNumber()) {
                throw new ResponseException("", "");
            }
            List<String> currentBatchSns = getCurrentBatchSns(snList, workSheet.getNumber());
            createProcessStepOemOrderBindSn(oemOrder, currentBatchSns);
        }
        return oemOrder;
    }

    /**
     * 外协收货获取可收货信息详情
     *
     * @param oemOrderId 外协工单Id
     * @return 外协收货获取可收货信息详情
     */
    public OemOrderSnDetailDTO getOemOrderDetail(Long oemOrderId) {

        OemOrderSnDetailDTO oemOrderSnDetailDto = new OemOrderSnDetailDTO();

        OemOrder oemOrder = oemOrderRepository.findByIdAndDeleted(oemOrderId, Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("oemOrderNotFind", "外协工单不存在"));

        SubWorkSheet originalSubWorkSheet = oemOrder.getOriginalSubWorkSheet();
        WorkSheet originalWorkSheet = Objects.nonNull(originalSubWorkSheet) ? originalSubWorkSheet.getWorkSheet() : oemOrder.getOriginalWorkSheet();

        if (oemOrder.getWorkSheet().getNumber() == oemOrder.getFinishNumber()) {
            throw new ResponseException("oemOrderFinish", "外协工单已收货完成");
        }
        //获取当前工单已生产待质检的个数
        Integer todoNumber = oemInspectRepository.countByOemReceiptHistoryOemorderIdAndStatusAndDeleted(oemOrderId, Constants.INT_ZERO, Constants.LONG_ZERO);
        //剩余可收货数量
        oemOrderSnDetailDto.setRemainingNumber(oemOrder.getWorkSheet().getNumber() - oemOrder.getFinishNumber() -Optional.ofNullable(todoNumber).orElse(0));
        if (oemOrderSnDetailDto.getRemainingNumber() == 0) {
            throw new ResponseException("oemOrderFinish", "外协工单已收货完成,待质检入库");
        }
        //获取所有投产工单+工序 绑定的外协sn
        List<OemOrderSn> oemOrderSns = Objects.nonNull(originalSubWorkSheet) ? oemOrderSnRepository.findByOemOrderOriginalSubWorkSheetIdAndOemOrderStepIdAndDeleted(originalSubWorkSheet.getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO) :
                oemOrderSnRepository.findByOemOrderOriginalWorkSheetIdAndOemOrderStepIdAndDeleted(originalWorkSheet.getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO);

        //工序外协-存在sn则返回未入库的sn记录
        if (ValidateUtils.isValid(oemOrderSns)) {

            List<String> bindSnList = Lists.newArrayList();

            //获取 所有投产工单+工序 已入库的sn
            List<String> oemInspectWareHouseSnList = Objects.nonNull(originalSubWorkSheet) ? oemInspectWarehouseRepository.findOemOrderOriginalSubWorkSheetIdAndOemOrderStepIdAndDeletedBySns(originalSubWorkSheet.getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO):
                    oemInspectWarehouseRepository.findOemOrderOriginalWorkSheetIdAndOemOrderStepIdAndDeletedBySns(originalWorkSheet.getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(oemInspectWareHouseSnList)){
                bindSnList.addAll(oemInspectWareHouseSnList);
            }
            //获取所以投产工单+工序 已录入质检，但未质检的sn列表
            List<OemReceiptHistory> oemReceiptHistories = Objects.nonNull(originalSubWorkSheet) ? oemInspectRepository.findByOriginalSubWorkSheetIdAndStatusAndDeleted(originalSubWorkSheet.getId(), Constants.INT_ZERO, Constants.LONG_ZERO) :
                    oemInspectRepository.findByOriginalWorkSheetIdAndStatusAndDeleted(originalWorkSheet.getId(), Constants.INT_ZERO, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(oemReceiptHistories)) {
                List<String> receiptSnList = oemReceiptHistoryDetailRepository.findByOemReceiptHistoryIdInAndDeleted(oemReceiptHistories.stream().map(OemReceiptHistory::getId).toList(), Constants.LONG_ZERO);
                if (ValidateUtils.isValid(receiptSnList)) {
                    bindSnList.addAll(receiptSnList);
                }
            }

            if (ValidateUtils.isValid(bindSnList)) {
                //去掉已入库的sn,或者待质检的sn
                oemOrderSnDetailDto.setSnList(oemOrderSns.stream().map(OemOrderSn::getSn).filter(orderSn -> !bindSnList.contains(orderSn)).distinct().toList());
            } else {
                oemOrderSnDetailDto.setSnList(oemOrderSns.stream().map(OemOrderSn::getSn).distinct().toList());
            }
        }
        return oemOrderSnDetailDto;
    }

    /**
     * 工序外协单获取可绑定的sn列表
     *
     * @param oemOrder 工序外协单
     * @return 可绑定的sn列表
     */
    public List<String> oemOrderGetBindSn(OemOrder oemOrder) {
        SubWorkSheet originalSubWorkSheet = oemOrder.getOriginalSubWorkSheet();
        WorkSheet originalWorkSheet = Objects.nonNull(originalSubWorkSheet) ? originalSubWorkSheet.getWorkSheet() : oemOrder.getOriginalWorkSheet();

        //获取外协工序已绑定的sn,没有绑定的sn 说明 不存在单只投产 直接跳过后续
        List<String> oemOrderBindSns = oemOrderSnService.getStepOemOrderBindSn(originalWorkSheet.getId(), originalSubWorkSheet.getId(), oemOrder.getStep().getId());
        if (!ValidateUtils.isValid(oemOrderBindSns)) {
            return Collections.emptyList();
        }

        //获取当前子工单 工序的上一道待流转sn信息
        Optional<WsStep> wsStepOptional = Objects.nonNull(originalSubWorkSheet) ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(originalSubWorkSheet.getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO) :
                wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(originalWorkSheet.getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO);
        if (wsStepOptional.isEmpty() && Objects.isNull(originalSubWorkSheet)) {
            throw new ResponseException("wsStepNotFind", "工序快照不存在！");
        }
        if (wsStepOptional.isEmpty()) {
            wsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(originalWorkSheet.getId(), oemOrder.getStep().getId(), Constants.LONG_ZERO);
        }
        WsStep oemWsStep = wsStepOptional.orElseThrow(() -> new ResponseException("wsStepNotFind", "工序快照不存在！"));

        if (!ValidateUtils.isValid(oemWsStep.getPreStepId())) {
            throw new ResponseException("prohibitFirstStepOem", "禁止第一道工序外协");
        }

        //不支持分叉闭合工序进行外协，默认去取第一个
        Long preStepId = Arrays.stream(oemWsStep.getPreStepId().split(",")).map(Long::parseLong).findFirst().get();

        List<SnWorkDetail> snWorkDetails = Objects.nonNull(originalSubWorkSheet) ? snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(originalSubWorkSheet.getId(), preStepId, Constants.LONG_ZERO) :
                snWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(originalWorkSheet.getId(), preStepId, Constants.LONG_ZERO);

        if (ValidateUtils.isValid(snWorkDetails)) {
            List<String> currentStepSns = snWorkDetails.stream().filter(snWorkDetail -> snWorkDetail.getResult() == Constants.INT_ONE).map(SnWorkDetail::getSn).distinct().toList();
            if (ValidateUtils.isValid(currentStepSns)) {
                if (ValidateUtils.isValid(oemOrderBindSns)) {
                    return currentStepSns.stream().filter(currentStepSn -> oemOrderBindSns.stream().noneMatch(oemOrderSn -> oemOrderSn.equals(currentStepSn)))
                            .toList();
                }
                return currentStepSns;
            }
        }
        return Collections.emptyList();
    }

    /**
     * 验证工序外协单创建
     *
     * @param subWorkSheet 投产子工单
     * @param workSheet    投产工单
     * @param step         工序
     * @param inputNumber  分单数量
     */
    public void validCreateOemOrder(SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step, Integer inputNumber) {

        //获取当前子工单 工序的上一道待流转sn信息
        Optional<WsStep> wsStepOptional = Objects.nonNull(subWorkSheet) ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO) :
                wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO);
        if (wsStepOptional.isEmpty() && Objects.isNull(subWorkSheet)) {
            throw new ResponseException("wsStepNotFind", "工序快照不存在！");
        }
        if (wsStepOptional.isEmpty()) {
            wsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO);
        }
        WsStep oemWsStep = wsStepOptional.orElseThrow(() -> new ResponseException("wsStepNotFind", "工序快照不存在！"));

        //工序剩余外协数量
        Integer oemStepRemainingNumber = getOemStepRemainingNumber(subWorkSheet, workSheet, oemWsStep);
        if (oemStepRemainingNumber < inputNumber) {
            throw new ResponseException("oemStepRemainingNumberExceed", "工序剩余外协数量小于分单数量");
        }
    }

    /**
     * 获取工序剩余外协数量
     *
     * @param subWorkSheet 投产子工单
     * @param workSheet    投产工单
     * @param wsStep       工序快照
     * @return 剩余外协数量
     */
    public Integer getOemStepRemainingNumber(SubWorkSheet subWorkSheet, WorkSheet workSheet, WsStep wsStep) {
        List<Long> preStepIds = Arrays.stream(wsStep.getPreStepId().split(",")).map(Long::parseLong).toList();
        //不支持分支闭合的前置工序进行外协
        Long preStepId = preStepIds.get(Constants.INT_ZERO);
        //验证前置工序是否 存在待检任务
        BaseDTO resultDto = inspectTaskService.getTodoStepTask(workSheet, subWorkSheet, stepRepository.getReferenceById(preStepId));
        if (Constants.KO.equals(resultDto.getStatus())) {
            throw new ResponseException("todoTaskExist", resultDto.getMessage());
        }
        //获取工序已外协数量
        Integer stepProcessNumber = originalStepProcessNumber(workSheet.getId(), Objects.isNull(subWorkSheet) ? null : subWorkSheet.getId(), wsStep.getStep().getId());
        //获取前置工序已合格数量
        Integer batchWorkDetailQuantityNumber = getBatchWorkDetailQuantityNumber(workSheet.getId(), Objects.isNull(subWorkSheet) ? null : subWorkSheet.getId(), preStepId);
        //获取当前工序剩余可外协工序数量
        return batchWorkDetailQuantityNumber - stepProcessNumber;
    }


    /**
     * 修改外协工单基础数据
     *
     * @param oemOrderUpdateDto 外协工单基础DTO
     * @return 外协工单
     */
    public OemOrder updateEntity(OemOrderUpdateDTO oemOrderUpdateDto) {
        OemOrder oemOrder = oemOrderRepository.findByIdAndDeleted(oemOrderUpdateDto.getOemOrderId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("oemOrderNotFind", "外协工单不存在"));

        oemOrder.setSupplierId(oemOrderUpdateDto.getSupplierId());
        return oemOrderRepository.save(oemOrder);
    }
}
