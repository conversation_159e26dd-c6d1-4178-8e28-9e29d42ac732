package net.airuima.oem.service.base;

import net.airuima.oem.domain.base.OemAutoConfig;
import net.airuima.oem.repository.base.OemAutoConfigRepository;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
@Transactional(rollbackFor = Exception.class)
public class OemAutoConfigService extends CommonJpaService<OemAutoConfig> {

    private final OemAutoConfigRepository oemAutoConfigRepository;

    @Autowired
    private CommonService commonService;

    public OemAutoConfigService(OemAutoConfigRepository oemAutoConfigRepository) {
        this.oemAutoConfigRepository = oemAutoConfigRepository;
    }

    @Override
    @FetchMethod
    public Page<OemAutoConfig> find(Specification<OemAutoConfig> spec, Pageable pageable) {
        return oemAutoConfigRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<OemAutoConfig> find(Specification<OemAutoConfig> spec) {
        return oemAutoConfigRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<OemAutoConfig> findAll(Pageable pageable) {
        return oemAutoConfigRepository.findAll(pageable);
    }

    /**
     * 获取产品谱系配置的工序外协分单配置
     *
     * @param pedigree 产品谱系
     * @return OemAutoConfig
     */
    public OemAutoConfig findOemAutoConfigByPedigree(Pedigree pedigree) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        commonService.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        List<Long> pedigreeIdList = pedigrees.stream().map(Pedigree::getId).toList();
        List<OemAutoConfig> oemAutoConfigs = oemAutoConfigRepository.findByPedigreeIdInAndDeletedAndIsEnable(pedigreeIdList, Constants.LONG_ZERO,Constants.TRUE);
        if(CollectionUtils.isEmpty(oemAutoConfigs)){
            return null;
        }
        int maxPedigreeLevel = oemAutoConfigs.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
        return oemAutoConfigs.stream().filter(i -> i.getPedigree().getType() == maxPedigreeLevel).toList().get(Constants.INT_ZERO);
    }
}
