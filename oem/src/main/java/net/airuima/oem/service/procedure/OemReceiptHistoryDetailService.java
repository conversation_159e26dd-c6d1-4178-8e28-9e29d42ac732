package net.airuima.oem.service.procedure;

import net.airuima.oem.domain.procedure.OemReceiptHistoryDetail;
import net.airuima.oem.repository.procedure.OemReceiptHistoryDetailRepository;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class OemReceiptHistoryDetailService extends CommonJpaService<OemReceiptHistoryDetail> {

    private final OemReceiptHistoryDetailRepository oemReceiptHistoryDetailRepository;

    public OemReceiptHistoryDetailService(OemReceiptHistoryDetailRepository oemReceiptHistoryDetailRepository) {
        this.oemReceiptHistoryDetailRepository = oemReceiptHistoryDetailRepository;
    }

    @Override
    @FetchMethod
    public Page<OemReceiptHistoryDetail> find(Specification<OemReceiptHistoryDetail> spec, Pageable pageable) {
        return oemReceiptHistoryDetailRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<OemReceiptHistoryDetail> find(Specification<OemReceiptHistoryDetail> spec) {
        return oemReceiptHistoryDetailRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<OemReceiptHistoryDetail> findAll(Pageable pageable) {
        return oemReceiptHistoryDetailRepository.findAll(pageable);
    }


}
