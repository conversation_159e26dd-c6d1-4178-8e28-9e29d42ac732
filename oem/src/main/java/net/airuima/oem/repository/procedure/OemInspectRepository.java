package net.airuima.oem.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.oem.domain.procedure.OemInspect;
import net.airuima.oem.domain.procedure.OemReceiptHistory;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OemInspectRepository extends LogicDeleteableRepository<OemInspect>,
        EntityGraphJpaSpecificationExecutor<OemInspect>, EntityGraphJpaRepository<OemInspect, Long> {

    /**
     * 获取待质检的外协收货记录
     *
     * @param status  状态
     * @param deleted 逻辑删除
     * @return List<OemInspect>
     */
    @FetchMethod
    List<OemInspect> findByStatusAndDeletedOrderByIdAsc(Integer status, Long deleted);

    /**
     * 通过id 获取外协质检记录
     *
     * @param id      外协质检id
     * @param deleted 逻辑删除
     * @return Optional<OemInspect>
     */
    @FetchMethod
    Optional<OemInspect> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 更新缓存
     *
     * @param id    主键
     * @param cache 缓存内容
     */
    @Modifying
    @Query("update OemInspect set cache=?2 where id=?1")
    void updateCache(Long id, String cache);

    /**
     * 获取指定状态下收货质检的  外协工单的质检记录数量
     *
     * @param oemOrderId 外协工单id
     * @param status     外协质检状态
     * @param deleted    逻辑删除
     * @return 质检记录数量
     */
    @Query("""
                        select sum(o.number) from OemInspect o
                        where o.oemReceiptHistory.oemOrder.id=?1 and o.status=?2 and o.deleted=?3
            """)
    Integer countByOemReceiptHistoryOemorderIdAndStatusAndDeleted(Long oemOrderId, Integer status, Long deleted);

    /**
     * 获取指定状态下收货质检的  外协工单的质检记录
     *
     * @param originalSubWorkSheetId  源子工单
     * @param status      外协质检状态
     * @param deleted     逻辑删除
     * @return List<OemReceiptHistory>
     */
    @Query("""
                        select o.oemReceiptHistory from OemInspect o
                        where o.oemReceiptHistory.oemOrder.originalSubWorkSheet.id=?1 and o.status=?2 and o.deleted=?3""")
    List<OemReceiptHistory> findByOriginalSubWorkSheetIdAndStatusAndDeleted(Long originalSubWorkSheetId, Integer status, Long deleted);

    /**
     * 获取指定状态下收货质检的  外协工单的质检记录
     * @param originalWorkSheet  源工单
     * @param status      外协质检状态
     * @param deleted     逻辑删除
     * @return List<OemReceiptHistory>
     */
    @Query("""
                        select o.oemReceiptHistory from OemInspect o
                        where o.oemReceiptHistory.oemOrder.originalWorkSheet.id=?1 and o.status=?2 and o.deleted=?3""")
    List<OemReceiptHistory> findByOriginalWorkSheetIdAndStatusAndDeleted(Long originalWorkSheet, Integer status, Long deleted);
}
