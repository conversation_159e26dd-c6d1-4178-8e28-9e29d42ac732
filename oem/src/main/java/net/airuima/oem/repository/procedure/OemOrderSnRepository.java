package net.airuima.oem.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.oem.domain.procedure.OemOrderSn;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OemOrderSnRepository extends LogicDeleteableRepository<OemOrderSn>,
        EntityGraphJpaSpecificationExecutor<OemOrderSn>, EntityGraphJpaRepository<OemOrderSn, Long> {

    /**
     * 获取子工单 工序 绑定的 sn
     *
     * @param originalSubWorkSheetId 子工单
     * @param stepId                 工序
     * @param deleted                逻辑删除
     * @return List<OemOrderSn>
     */
    @FetchMethod
    List<OemOrderSn> findByOemOrderOriginalSubWorkSheetIdAndOemOrderStepIdAndDeleted(Long originalSubWorkSheetId, Long stepId, Long deleted);

    /**
     * 获取工单 工序 绑定的 sn
     *
     * @param originalWorkSheetId 工单
     * @param stepId              工序
     * @param deleted             逻辑删除
     * @return List<OemOrderSn>
     */
    @FetchMethod
    List<OemOrderSn> findByOemOrderOriginalWorkSheetIdAndOemOrderStepIdAndDeleted(Long originalWorkSheetId, Long stepId, Long deleted);
}
