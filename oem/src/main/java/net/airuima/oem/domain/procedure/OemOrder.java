package net.airuima.oem.domain.procedure;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.SupplierDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

@Schema(name = "外协订单(OemOrder)", description = "外协订单")
@Entity
@Table(name = "procedure_oem_order", uniqueConstraints = {
        @UniqueConstraint(name = "procedure_oem_order_unique", columnNames = {"work_sheet_id", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class OemOrder extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商id
     */
    @Column(name = "supplier_id")
    @Schema(description = "供应商id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId", tableName = "supplier")
    private SupplierDTO supplierDto = new SupplierDTO();


    /**
     * 工单id
     */
    @NotNull
    @Schema(description = "工单id", required = true)
    @ManyToOne
    @JoinColumn(name = "work_sheet_id", nullable = false)
    private WorkSheet workSheet;

    /**
     * 外协类型：0产品外协，1工序外协
     */
    @Column(name = "category")
    @Schema(description = "外协类型：0产品外协，1工序外协")
    private int category;

    /**
     * 外协工序id
     */
    @Schema(description = "外协工序id")
    @ManyToOne
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 关联源工单id
     */
    @Schema(description = "关联源工单id")
    @ManyToOne
    @JoinColumn(name = "origin_work_sheet_id")
    private WorkSheet originalWorkSheet;

    /**
     * 关联源子工单id
     */
    @Schema(description = "关联源子工单id")
    @ManyToOne
    @JoinColumn(name = "origin_sub_work_sheet_id")
    private SubWorkSheet originalSubWorkSheet;

    /**
     * 完成数
     */
    @Schema(description = "完成数")
    @Column(name = "finish_number")
    private int finishNumber;

    public Long getSupplierId() {
        return supplierId;
    }

    public OemOrder setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDto() {
        return supplierDto;
    }

    public OemOrder setSupplierDto(SupplierDTO supplierDto) {
        this.supplierDto = supplierDto;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public OemOrder setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public OemOrder setCategory(int category) {
        this.category = category;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public OemOrder setStep(Step step) {
        this.step = step;
        return this;
    }

    public WorkSheet getOriginalWorkSheet() {
        return originalWorkSheet;
    }

    public OemOrder setOriginalWorkSheet(WorkSheet originalWorkSheet) {
        this.originalWorkSheet = originalWorkSheet;
        return this;
    }

    public SubWorkSheet getOriginalSubWorkSheet() {
        return originalSubWorkSheet;
    }

    public OemOrder setOriginalSubWorkSheet(SubWorkSheet originalSubWorkSheet) {
        this.originalSubWorkSheet = originalSubWorkSheet;
        return this;
    }

    public int getFinishNumber() {
        return finishNumber;
    }

    public OemOrder setFinishNumber(int finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }
}
