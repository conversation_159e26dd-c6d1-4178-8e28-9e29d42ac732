package net.airuima.oem.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.persistence.Table;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.dto.qms.CheckItemDTO;
import org.hibernate.annotations.*;
import org.hibernate.annotations.Cache;

import java.io.Serializable;
import java.util.List;

@Schema(name = "外协质检结果明细表(OemInspect)", description = "外协质检结果明细表")
@Entity
@Table(name = "procedure_oem_inspect_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class OemInspectDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外协质检结果id
     */
    @ManyToOne
    @Schema(name = "外协质检结果")
    @JoinColumn(name = "oem_inspect_id")
    private OemInspect oemInspect;

    /**
     * sn
     */
    @Schema(name = "sn")
    @Column(name = "sn")
    private String sn;

    /**
     * 不良项目
     */
    @ManyToOne
    @Schema(name = "不良项目")
    @JoinColumn(name = "unqualified_item_id")
    private UnqualifiedItem unqualifiedItem;

    /**
     * 质检处理结果明细列表
     */
    @Schema(description = "质检处理结果明细列表")
    @Type(JsonType.class)
    @Column(name = "process_check_item_info")
    private List<OemInspectDetailCheckItemJson> checkItems;

    public OemInspect getOemInspect() {
        return oemInspect;
    }

    public OemInspectDetail setOemInspect(OemInspect oemInspect) {
        this.oemInspect = oemInspect;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public OemInspectDetail setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public OemInspectDetail setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public List<OemInspectDetailCheckItemJson> getCheckItems() {
        return checkItems;
    }

    public OemInspectDetail setCheckItems(List<OemInspectDetailCheckItemJson> checkItems) {
        this.checkItems = checkItems;
        return this;
    }
}
