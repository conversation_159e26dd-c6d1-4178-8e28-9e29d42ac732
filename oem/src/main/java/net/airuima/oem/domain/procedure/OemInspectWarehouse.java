package net.airuima.oem.domain.procedure;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

@Schema(name = "外协质检入库(OemInspectWarehouse)", description = "外协质检入库")
@Entity
@Table(name = "procedure_oem_inspect_warehouse")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class OemInspectWarehouse extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外协订单id
     */
    @ManyToOne
    @Schema(description = "外协订单id")
    @JoinColumn(name = "oem_order_id")
    private OemOrder oemOrder;


    /**
     * 外协质检结果
     */
    @Schema(description = "外协质检结果")
    @ManyToOne
    @JoinColumn(name = "oem_inspect_id")
    private OemInspect oemInspect;

    /**
     * 不良项目
     */
    @Schema(description = "不良项目")
    @ManyToOne
    @JoinColumn(name = "unqualified_item_id")
    private UnqualifiedItem unqualifiedItem;

    /**
     * sn
     */
    @Schema(description = "sn")
    @Column(name = "sn")
    private String sn;


    @Column(name = "number")
    @Schema(description = "数量")
    private int number;


    /**
     * 检验结果(0:不合格;1:合格)
     */
    @Column(name = "result")
    @Schema(description = "检验结果(0:不合格;1:合格)")
    private Boolean result;

    public int getNumber() {
        return number;
    }

    public OemInspectWarehouse setNumber(int number) {
        this.number = number;
        return this;
    }

    public OemInspect getOemInspect() {
        return oemInspect;
    }

    public OemInspectWarehouse setOemInspect(OemInspect oemInspect) {
        this.oemInspect = oemInspect;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public OemInspectWarehouse setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public OemInspectWarehouse setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public OemInspectWarehouse setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public OemOrder getOemOrder() {
        return oemOrder;
    }

    public OemInspectWarehouse setOemOrder(OemOrder oemOrder) {
        this.oemOrder = oemOrder;
        return this;
    }
}
