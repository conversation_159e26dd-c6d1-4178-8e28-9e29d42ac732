package net.airuima.skill.web.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.skill.domain.Skill;
import net.airuima.skill.service.SkillService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产技能Resource
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@Tag(name = "生产技能Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/skills")
@AuthorityRegion("员工技能")
@FuncInterceptor("StaffSkillControl")
@AuthSkip("D")
public class SkillResource extends ProtectBaseResource<Skill> {

    private final SkillService skillService;

    public SkillResource(SkillService skillService) {
        this.skillService = skillService;
        this.mapUri = "/api/skills";
    }

    /**
     * 通过名称或者代码模糊查询技能列表
     * @param text 技能名称或者技能代码
     * @param isEnable 是否启用
     * @param size 要查询条数
     * @return org.springframework.http.ResponseEntity<java.util.List<net.airuima.rbase.domain.base.skill.Skill>> 技能列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过名称或者代码模糊查询技能列表")
    @Parameters({
           @Parameter(name = "text", description = "技能名称或者技能代码", required = true),
            @Parameter(name = "isEnable", description = "是否启用"),
           @Parameter(name = "size", description = "查询条数", required = true)
    })
    @GetMapping("/nameOrCode")
    public ResponseEntity<ResponseData<List<Skill>>> findByNameOrCode(@RequestParam(value = "text") String text, @RequestParam(value = "isEnable",required = false) Boolean isEnable, @RequestParam(value = "size") Integer size){
        List<Skill> skillList = skillService.findByNameOrCode(text,isEnable, size);
        return ResponseData.ok(skillList);
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "生产技能");
    }

}
