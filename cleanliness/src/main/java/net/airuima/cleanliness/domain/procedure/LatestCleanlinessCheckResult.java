package net.airuima.cleanliness.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 最新洁净度检测结果Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "最新洁净度检测结果(LatestEnviromentCheckResult)", description = "最新洁净度检测结果")
@Entity
@FetchEntity
@Table(name = "procedure_latest_cleanliness_check_result", uniqueConstraints = {
        @UniqueConstraint(name = "procedure_latest_cleanliness_check_result_unique", columnNames = {"area_id", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "latestCleanlinessCheckResultEntityGraph",attributeNodes = {@NamedAttributeNode("area")})
public class LatestCleanlinessCheckResult extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门区域
     */
    @NotNull
    @ManyToOne
    @Schema(description = "部门区域")
    @JoinColumn(name = "area_id", nullable = false)
    private OrganizationArea area;

    /**
     * 结果（0：不合格, 1：合格）
     */
    @Schema(description = "结果（0：不合格, 1：合格）", required = true)
    @Column(name = "result", nullable = false)
    private int result;

    /**
     * 最新检测人ID
     */
    @Schema(description = "最新检测人ID", required = true)
    @Column(name = "latest_operator_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private long latestOperatorId;

    /**
     * 最新检测人DTO
     */
    @Transient
    @Schema(description = "最新检测人DTO", required = true)
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "latestOperatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "latest_operator_id")
    private StaffDTO latestOperatorDto = new StaffDTO();

    /**
     * 最新检测日期
     */
    @NotNull
    @Schema(description = "最新检测日期", required = true)
    @Column(name = "latest_inspect_date", nullable = false)
    private LocalDateTime latestInspectDate;

    /**
     * 下次检测日期
     */
    @NotNull
    @Schema(description = "下次检测日期", required = true)
    @Column(name = "next_inspect_date", nullable = false)
    private LocalDateTime nextInspectDate;

    public int getResult() {
        return result;
    }

    public LatestCleanlinessCheckResult setResult(int result) {
        this.result = result;
        return this;
    }

    public Long getLatestOperatorId() {
        return latestOperatorId;
    }

    public void setLatestOperatorId(Long latestOperatorId) {
        this.latestOperatorId = latestOperatorId;
    }

    public OrganizationArea getArea() {
        return area;
    }

    public LatestCleanlinessCheckResult setArea(OrganizationArea area) {
        this.area = area;
        return this;
    }

    public LatestCleanlinessCheckResult setLatestOperatorId(long latestOperatorId) {
        this.latestOperatorId = latestOperatorId;
        return this;
    }

    public StaffDTO getLatestOperatorDto() {
        return latestOperatorDto;
    }

    public void setLatestOperatorDto(StaffDTO latestOperatorDto) {
        this.latestOperatorDto = latestOperatorDto;
    }

    public LocalDateTime getLatestInspectDate() {
        return latestInspectDate;
    }

    public LatestCleanlinessCheckResult setLatestInspectDate(LocalDateTime latestInspectDate) {
        this.latestInspectDate = latestInspectDate;
        return this;
    }

    public LocalDateTime getNextInspectDate() {
        return nextInspectDate;
    }

    public void setNextInspectDate(LocalDateTime nextInspectDate) {
        this.nextInspectDate = nextInspectDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LatestCleanlinessCheckResult latestCleanlinessCheckResult = (LatestCleanlinessCheckResult) o;
        if (latestCleanlinessCheckResult.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), latestCleanlinessCheckResult.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
