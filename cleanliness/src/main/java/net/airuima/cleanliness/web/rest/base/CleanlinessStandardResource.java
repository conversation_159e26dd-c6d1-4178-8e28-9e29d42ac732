package net.airuima.cleanliness.web.rest.base;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.cleanliness.domain.base.CleanlinessStandard;
import net.airuima.cleanliness.service.base.CleanlinessStandardService;
import net.airuima.cleanliness.web.rest.base.dto.CleanlinessStandardExcelDTO;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportDTO;
import net.airuima.query.QueryConditionParser;
import net.airuima.rbase.constant.Constants;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度检测标准表Resource
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Tag(name = "洁净度检测标准表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/cleanliness-standards")
@AuthorityRegion("环境管控")
@FuncInterceptor("EnvCleanliness")
public class CleanlinessStandardResource extends ProtectBaseResource<CleanlinessStandard> {

    public static final String MODULE = "洁净度检测标准";
    private final CleanlinessStandardService cleanlinessStandardService;

    public CleanlinessStandardResource(CleanlinessStandardService cleanlinessStandardService) {
        this.cleanlinessStandardService = cleanlinessStandardService;
        this.mapUri = "/api/cleanliness-standards";
    }

    /**
     * 根据区域ID查询洁净度标准
     *
     * @param areaId 区域ID
     * @return ResponseEntity<ResponseData<CleanlinessStandard>>
     * <AUTHOR>
     * @date 2022-06-29
     **/
    @Operation(summary = "根据区域ID查询洁净度标准")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/areaId/{areaId}")
    public ResponseEntity<ResponseData<CleanlinessStandard>> findByOrganizationId(@PathVariable("areaId") Long areaId) {
        try {
            CleanlinessStandard cleanlinessStandardList = cleanlinessStandardService.findByAreaId(areaId);
            return ResponseData.ok(cleanlinessStandardList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 洁净度检测标准表 导入
     *
     * @deprecated
     * @param file 文件
     * @return ResponseEntity
     */
    @Override
    @Deprecated
    @Operation(summary= "洁净度检测标准表 导入")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file,
                                                               @RequestParam("data") String data,
                                                               @RequestParam(value = "suffix", required = false) String suffix,
                                                               @RequestParam(value = "metaColumn", required = false) String metaColumn,
                                                               HttpServletResponse response) {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(entityName, "FileEmpty", "File invalid.")).build();
            }
            ImportParams importParams = new ImportParams();
            importParams.setHeadRows(Constants.INT_TWO);
            List<CleanlinessStandardExcelDTO> cleanlinessStandardExcelDtoList = ExcelImportUtil.importExcel(file.getInputStream(), CleanlinessStandardExcelDTO.class, importParams);
            cleanlinessStandardService.importTableExcel(cleanlinessStandardExcelDtoList);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(entityName + ".importSuccess", entityName)).build();
        }catch (ResponseException responseException){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "importFailed", e.toString())).build();
        }
    }

    /**
     * 洁净度检测标准表 导出
     *
     * @deprecated
     */
    @Override
    @PostMapping(value = "/v2/exportExcel")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "洁净度检测标准表 导出")
    public void exportExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<CleanlinessStandard> cleanlinessStandards = new ArrayList<>();
        if (Objects.isNull(exportDTO.getExportTemplate()) || !exportDTO.getExportTemplate()) {
            Specification<CleanlinessStandard> spec = QueryConditionParser.buildSpecificationWithClassName(CleanlinessStandard.class.getName(), exportDTO.getQcs(), this.filters, this.filterReformer);
            cleanlinessStandards = this.getService().find(spec);
        }
        try {
            cleanlinessStandardService.exportTableExcel(exportDTO, cleanlinessStandards, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
