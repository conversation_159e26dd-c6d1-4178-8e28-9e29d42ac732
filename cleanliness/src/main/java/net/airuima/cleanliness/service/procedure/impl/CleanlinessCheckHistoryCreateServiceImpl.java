package net.airuima.cleanliness.service.procedure.impl;


import net.airuima.cleanliness.service.procedure.CleanlinessCheckHistoryService;
import net.airuima.cleanliness.service.procedure.api.ICleanlinessCheckHistoryService;
import net.airuima.cleanliness.web.rest.procedure.dto.CleanlinessCheckHistorySaveDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 洁净度检测历史扩展保存实现
 *
 * <AUTHOR>
 * @date 2023/4/25 9:54
 **/
@Service
@Order(0)
public class CleanlinessCheckHistoryCreateServiceImpl implements ICleanlinessCheckHistoryService {

    @Autowired
    private CleanlinessCheckHistoryService cleanlinessCheckHistoryService;

    /**
     * 新增洁净度度检测历史
     *
     * @param saveDto 洁净度检测历史DTO
     */
    @Override
    public void createCleanlinessCheckHistory(CleanlinessCheckHistorySaveDTO saveDto) {
        cleanlinessCheckHistoryService.create(saveDto);
    }


}
