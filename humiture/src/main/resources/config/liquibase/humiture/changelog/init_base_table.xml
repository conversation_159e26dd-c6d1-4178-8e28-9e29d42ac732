<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="RMPC (generated)" id="1714012191927-1">
        <createTable remarks="温湿度检测标准表" tableName="base_humiture_standard">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="temperature_range" remarks="温度检测范围" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="humidity_range" remarks="湿度检测范围" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="period_number" remarks="温湿度周期数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1" name="period_unit" remarks="温湿度周期单位(0:小时，1:天，2:周，3:月，4:年)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="abnormal_id" remarks="异常配置ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="area_id" remarks="部门区域ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="warn_duration" remarks="提醒数值" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="warn_unit" remarks="提醒周期" type="TINYINT(3)"/>
            <column name="temperature_stop_line_range" remarks="温度停线范围（json）" type="JSON">
                <constraints nullable="false"/>
            </column>
            <column name="humidity_stop_line_range" remarks="湿度停线范围（json）" type="JSON">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012191927-2">
        <createTable remarks="温湿度检测历史表" tableName="procedure_humiture_check_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="temperature" remarks="温度" type="DECIMAL(10, 1)">
                <constraints nullable="false"/>
            </column>
            <column name="humidity" remarks="湿度" type="DECIMAL(10, 1)">
                <constraints nullable="false"/>
            </column>
            <column name="result" remarks="结果（0：停线, 1：合格，2：预警）" type="INT"/>
            <column name="operator_id" remarks="检验人ID" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date" remarks="检验日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="area_id" remarks="部门区域ID" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012191927-3">
        <createTable remarks="最新环境检测结果表" tableName="procedure_latest_humiture_check_result">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="temperature" type="DECIMAL(10, 1)"/>
            <column name="humidity" type="DECIMAL(10, 1)"/>
            <column name="result" remarks="结果（0：停线, 1：合格，2：预警）" type="INT"/>
            <column name="latest_operator_id" remarks="最新检测人ID" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="latest_inspect_date" remarks="最新检测日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="next_inspect_date" remarks="下次检测日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="area_id" remarks="部门区域ID" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012191927-4">
        <addUniqueConstraint columnNames="area_id, deleted" constraintName="base_humiture_standard_unique" tableName="base_humiture_standard"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012191927-5">
        <addUniqueConstraint columnNames="area_id, deleted" constraintName="procedure_latest_enviroment_check_result_unique" tableName="procedure_latest_humiture_check_result"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012191927-6">
        <createIndex associatedWith="" indexName="base_humiture_standard_area_id_index" tableName="base_humiture_standard">
            <column name="area_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012191927-7">
        <createIndex associatedWith="" indexName="procedure_humiture_check_history_area_id_index" tableName="procedure_humiture_check_history">
            <column name="area_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012191927-8">
        <createIndex associatedWith="" indexName="procedure_latest_enviroment_check_result_area_id_index" tableName="procedure_latest_humiture_check_result">
            <column name="area_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="202502061504" author="zorro">
        <dropNotNullConstraint tableName="base_humiture_standard" columnName="temperature_stop_line_range" columnDataType="json"/>
        <dropNotNullConstraint tableName="base_humiture_standard" columnName="humidity_stop_line_range" columnDataType="json"/>
    </changeSet>
</databaseChangeLog>
