package net.airuima.humiture.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.humiture.domain.procedure.HumitureCheckHistory;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 温湿度检测历史Repository
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Repository
public interface HumitureCheckHistoryRepository extends LogicDeleteableRepository<HumitureCheckHistory>,
        EntityGraphJpaSpecificationExecutor<HumitureCheckHistory>, EntityGraphJpaRepository<HumitureCheckHistory, Long> {

    /**
     * 通过区域主键ID及逻辑删除获取最新记录
     * @param areaId 区域主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rmes.domain.procedure.quality.HumitureCheckHistory> 温湿度检测历史
     */
    @DataFilter(isSkip = true)
    Optional<HumitureCheckHistory> findTop1ByAreaIdAndDeletedOrderByIdDesc(Long areaId,Long deleted);
}
