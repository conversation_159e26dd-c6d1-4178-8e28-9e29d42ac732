package net.airuima.humiture.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.humiture.domain.procedure.LatestHumitureCheckResult;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 最新温湿度检测结果Repository
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Repository
public interface LatestHumitureCheckResultRepository extends LogicDeleteableRepository<LatestHumitureCheckResult>,
        EntityGraphJpaSpecificationExecutor<LatestHumitureCheckResult>, EntityGraphJpaRepository<LatestHumitureCheckResult, Long> {

    /**
     * 通过 区域主键Id 和 类型，查找唯一最新环境检测结果
     *
     * @param areaId   区域主键Id
     * @param deleted  删除标识
     * @return Optional<LatestEnviromentCheckResult>
     */
    @DataFilter(isSkip = true)
    Optional<LatestHumitureCheckResult> findByAreaIdAndDeleted(Long areaId, Long deleted);

    /**
     * 通过日期、逻辑删除获取超出指定日期的待检记录
     * @param compareTime 比较日期
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.rmes.domain.procedure.quality.LatestEnviromentCheckResult> 最新检测结果列表
     * <AUTHOR>
     * @date 2023/12/12
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<LatestHumitureCheckResult> findByNextInspectDateBeforeAndDeleted(LocalDateTime compareTime,Long deleted);

    /**
     * 根据主键id和删除标记查找最新环境检测结果
     * @param id 主键id
     * @param deleted 删除标记
     * @return net.airuima.rmes.domain.procedure.quality.LatestEnviromentCheckResult 最新环境检测结果
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    LatestHumitureCheckResult findByIdAndDeleted(Long id,Long deleted);
}
