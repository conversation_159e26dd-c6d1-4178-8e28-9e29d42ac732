package net.airuima.humiture.rabbitmq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.rabbitmq.client.Channel;
import net.airuima.humiture.domain.procedure.LatestHumitureCheckResult;
import net.airuima.humiture.service.procedure.LatestHumitureCheckResultService;
import net.airuima.rbase.dto.rabbitmq.DelayedMessageDTO;
import net.airuima.rbase.rabbitmq.RmesRabbitMqConfig;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/16
 */
@Component
public class HumitureRabbitmqListener {

    @Autowired
    private LatestHumitureCheckResultService latestHumitureCheckResultService;


    /**
     * 接收延迟队列的消息
     *
     * @param message
     * @param channel
     * @param tag
     */
    @RabbitListener(bindings = @QueueBinding(value = @Queue(), exchange = @Exchange(value = RmesRabbitMqConfig.DELAYED_QUEUE_NAME, type = ExchangeTypes.FANOUT)))
    public void receiveDelayQueue(String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) {
        try {
            DelayedMessageDTO delayedMessageDTO = JSON.parseObject(message, new TypeReference<>() {
            });
            String businessKey = delayedMessageDTO.getBusinessKey();
            if (businessKey.equals(LatestHumitureCheckResult.class.getSimpleName())) {
                latestHumitureCheckResultService.humitureInspectExpiredWarn(JSON.parseObject(delayedMessageDTO.getData(), LatestHumitureCheckResult.class));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
