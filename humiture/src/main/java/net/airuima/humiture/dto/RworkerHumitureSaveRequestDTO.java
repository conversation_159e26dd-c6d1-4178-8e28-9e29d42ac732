package net.airuima.humiture.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *  Rworker-Web保存温湿度参数DTO
 * <AUTHOR>
 * @date 2024/1/8
 */
@Schema(description = "Rworker-web保存温湿度数据DTO")
public class RworkerHumitureSaveRequestDTO implements Serializable {


    /**
     * 区域编码
     */
    @Schema(description = "区域编码")
    private String areaCode;

    /**
     * 温度值
     */
    @Schema(description = "温度值")
    private BigDecimal temperature;

    /**
     * 湿度值
     */
    @Schema(description = "湿度值")
    private BigDecimal humidity;



    public String getAreaCode() {
        return areaCode;
    }

    public RworkerHumitureSaveRequestDTO setAreaCode(String areaCode) {
        this.areaCode = areaCode;
        return this;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public RworkerHumitureSaveRequestDTO setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
        return this;
    }

    public BigDecimal getHumidity() {
        return humidity;
    }

    public RworkerHumitureSaveRequestDTO setHumidity(BigDecimal humidity) {
        this.humidity = humidity;
        return this;
    }
}
