package net.airuima.humiture.service.procedure;

import com.alibaba.fastjson.JSON;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.humiture.constant.HumitureEnum;
import net.airuima.humiture.domain.base.HumitureStandard;
import net.airuima.humiture.domain.procedure.HumitureCheckHistory;
import net.airuima.humiture.domain.procedure.LatestHumitureCheckResult;
import net.airuima.humiture.repository.base.HumitureStandardRepository;
import net.airuima.humiture.repository.procedure.HumitureCheckHistoryRepository;
import net.airuima.humiture.repository.procedure.LatestHumitureCheckResultRepository;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.FuncKeyConstants;
import net.airuima.rbase.domain.base.scene.AreaWorkCell;
import net.airuima.rbase.dto.message.SendMessageDTO;
import net.airuima.rbase.dto.rabbitmq.DelayedMessageDTO;
import net.airuima.rbase.proxy.message.RbaseTaskMessageProxy;
import net.airuima.rbase.rabbitmq.RmesRabbitMqSender;
import net.airuima.rbase.repository.base.scene.AreaWorkCellRepository;
import net.airuima.rbase.repository.base.scene.OrganizationAreaRepository;
import net.airuima.rbase.util.DateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.DateTimeUtil;
import net.airuima.util.FuncKeyUtil;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 最新温湿度检测结果Service
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class LatestHumitureCheckResultService extends CommonJpaService<LatestHumitureCheckResult> {

    private static final String BEGIN_LI = "<li>";
    private static final String END_LI = "</li>";
    private final String LATEST_HUMITURE_CHECK_RESULT_ENTITY_GRAPH = "latestHumitureCheckResultEntityGraph";
    @Autowired
    private LatestHumitureCheckResultRepository latestHumitureCheckResultRepository;
    @Autowired
    private HumitureStandardRepository humitureStandardRepository;
    @Autowired
    private RbaseTaskMessageProxy rbaseTaskMessageProxy;
    @Autowired
    private OrganizationAreaRepository organizationAreaRepository;
    @Autowired
    private RmesRabbitMqSender rmesRabbitMqSender;
    @Autowired
    private AreaWorkCellRepository areaWorkCellRepository;
    @Autowired
    private HumitureCheckHistoryRepository humitureCheckHistoryRepository;

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<LatestHumitureCheckResult> find(Specification<LatestHumitureCheckResult> spec, Pageable pageable) {
        return latestHumitureCheckResultRepository.findAll(spec, pageable, new NamedEntityGraph(LATEST_HUMITURE_CHECK_RESULT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<LatestHumitureCheckResult> find(Specification<LatestHumitureCheckResult> spec) {
        return latestHumitureCheckResultRepository.findAll(spec, new NamedEntityGraph(LATEST_HUMITURE_CHECK_RESULT_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<LatestHumitureCheckResult> findAll(Pageable pageable) {
        return latestHumitureCheckResultRepository.findAll(pageable, new NamedEntityGraph(LATEST_HUMITURE_CHECK_RESULT_ENTITY_GRAPH));
    }

    /**
     * 新增/更新 最新环境检测结果表
     *
     * @param humitureCheckHistory 结果参数
     */
    public void updateFinal(HumitureCheckHistory humitureCheckHistory) {
        LatestHumitureCheckResult latestHumitureCheckResult = new LatestHumitureCheckResult();
        //如果为温湿度检测历史表
        Long areaId = humitureCheckHistory.getArea().getId();
        Optional<LatestHumitureCheckResult> latestHumitureCheckResultOptional = latestHumitureCheckResultRepository.findByAreaIdAndDeleted(areaId, Constants.LONG_ZERO);
        if (latestHumitureCheckResultOptional.isPresent()) {
            latestHumitureCheckResult = latestHumitureCheckResultOptional.get();
        } else {
            latestHumitureCheckResult.setArea(humitureCheckHistory.getArea());
        }
        latestHumitureCheckResult.setTemperature(humitureCheckHistory.getTemperature())
                .setHumidity(humitureCheckHistory.getHumidity())
                .setResult(humitureCheckHistory.getResult() == HumitureEnum.QUALIFIED.getCategory() ? HumitureEnum.QUALIFIED.getCategory() : HumitureEnum.NOT_QUALIFIED.getCategory())
                .setLatestOperatorId(humitureCheckHistory.getOperatorId()).setLatestInspectDate(humitureCheckHistory.getRecordDate());

        //设置下次检测日期
        nextInspectDate(latestHumitureCheckResult);
    }

    /**
     * 设置下次检测日期
     *
     * @param latestHumitureCheckResult 最新环境检测结果表 实体类
     */
    public void nextInspectDate(LatestHumitureCheckResult latestHumitureCheckResult) {
        if (latestHumitureCheckResult.getResult() == Constants.INT_ZERO) {
            // 如果检测结果不合格的话下次检测日期应该是现在
            latestHumitureCheckResult.setNextInspectDate(latestHumitureCheckResult.getLatestInspectDate());
            latestHumitureCheckResult = latestHumitureCheckResultRepository.save(latestHumitureCheckResult);
            latestHumitureCheckResult.setArea(organizationAreaRepository.findByIdAndDeleted(latestHumitureCheckResult.getArea().getId(), Constants.LONG_ZERO).orElse(null));
            this.humitureInspectExpiredWarn(latestHumitureCheckResult);
            return;
        }
        Optional<HumitureStandard> humitureStandardOptional = humitureStandardRepository.findByAreaIdAndDeleted(latestHumitureCheckResult.getArea().getId(), Constants.LONG_ZERO);
        if (humitureStandardOptional.isEmpty()) {
            throw new ResponseException("error.HumitureStandardNotExist", "温湿度检测标准不存在");
        }
        HumitureStandard humitureStandard = humitureStandardOptional.get();
        LocalDateTime nextInspectDate = DateUtils.delayDate(latestHumitureCheckResult.getLatestInspectDate(), humitureStandard.getPeriodNumber(), humitureStandard.getPeriodUnit());
        latestHumitureCheckResult.setNextInspectDate(nextInspectDate);
        latestHumitureCheckResult = latestHumitureCheckResultRepository.save(latestHumitureCheckResult);
        //发送延迟队列提醒消息
        this.humitureRemindRabbitmq(latestHumitureCheckResult, humitureStandard.getWarnDuration(), humitureStandard.getWarnUnit());

    }

    /**
     * 发送延迟队列提醒消息
     *
     * @param latestHumitureCheckResult 最新检测结果
     * @param warnDuration              提前提醒周期
     * @param warnUnit                  提前提醒周期单位
     */
    private void humitureRemindRabbitmq(LatestHumitureCheckResult latestHumitureCheckResult, int warnDuration, int warnUnit) {
        if (!FuncKeyUtil.checkApi(FuncKeyConstants.MESSAGE)) {
            return;
        }
        latestHumitureCheckResult = latestHumitureCheckResultRepository.findByIdAndDeleted(latestHumitureCheckResult.getId(), Constants.LONG_ZERO);
        //如果需要立刻检测则立即发出提醒消息
        if (latestHumitureCheckResult.getNextInspectDate().isBefore(LocalDateTime.now()) || latestHumitureCheckResult.getNextInspectDate().isEqual(LocalDateTime.now())) {
            this.humitureInspectExpiredWarn(latestHumitureCheckResult);
            return;
        }
        LocalDateTime nextWarnDate = DateUtils.delayDate(latestHumitureCheckResult.getNextInspectDate(), warnDuration * Constants.NEGATIVE_ONE, warnUnit);
        //如果按照提醒周期计算出来的提醒日期在当前日期时间之前则立即发出提醒信息
        if (nextWarnDate.isBefore(LocalDateTime.now())) {
            this.humitureInspectExpiredWarn(latestHumitureCheckResult);
            return;
        }
        //计算间隔时间
        Duration duration = Duration.between(LocalDateTime.now(), nextWarnDate);
        DelayedMessageDTO delayedMessageDTO = new DelayedMessageDTO();
        delayedMessageDTO.setId(String.valueOf(latestHumitureCheckResult.getId())).setData(JSON.toJSONString(latestHumitureCheckResult)).setBusinessKey(LatestHumitureCheckResult.class.getSimpleName());
        rmesRabbitMqSender.send(JSON.toJSONString(delayedMessageDTO), (int) duration.toMillis());
    }

    /**
     * 发送即将到期的环境检测任务信息
     *
     * @param latestHumitureCheckResult 最新检测结果
     * <AUTHOR>
     */
    public void humitureInspectExpiredWarn(LatestHumitureCheckResult latestHumitureCheckResult) {
        if (!FuncKeyUtil.checkApi(FuncKeyConstants.MESSAGE)) {
            return;
        }
        LatestHumitureCheckResult latestHumitureCheckResultTemp = null;
        LatestHumitureCheckResult targetLatestHumitureCheckResult = latestHumitureCheckResultRepository.findByIdAndDeleted(latestHumitureCheckResult.getId(), Constants.LONG_ZERO);
        //如果最新状态已经发生了改变则无需发送消息
        if (Objects.nonNull(targetLatestHumitureCheckResult) && (targetLatestHumitureCheckResult.getNextInspectDate().isEqual(LocalDateTime.now())  || targetLatestHumitureCheckResult.getNextInspectDate().isBefore(LocalDateTime.now()))) {
            latestHumitureCheckResultTemp = targetLatestHumitureCheckResult;
        }else if(Objects.isNull(targetLatestHumitureCheckResult) && (latestHumitureCheckResult.getNextInspectDate().isEqual(LocalDateTime.now())  || latestHumitureCheckResult.getNextInspectDate().isBefore(LocalDateTime.now()))){
            latestHumitureCheckResultTemp = latestHumitureCheckResult;
        }
        if(Objects.isNull(latestHumitureCheckResultTemp)){
            return;
        }
        SendMessageDTO sendMessageDTO = new SendMessageDTO();
        sendMessageDTO.setConfigGroupCode("LatestEnviromentCheckResult");
        sendMessageDTO.setSubject("环境检测任务(即将)到期提醒");
        String htmlBuilder = "<h1>环境检测任务(即将)到期提醒</h1>" +
                "<p>任务内容：</p><ul>" +
                BEGIN_LI + "区域组织架构：" + latestHumitureCheckResultTemp.getArea().getOrganizationDto().getName() + END_LI +
                BEGIN_LI + "检测区域名称：" + latestHumitureCheckResultTemp.getArea().getName() + END_LI +
                BEGIN_LI + "检测区域编码：" + latestHumitureCheckResultTemp.getArea().getCode() + END_LI +
                BEGIN_LI + "检测任务类型：温湿度" +
                BEGIN_LI + "最新检测结果：" + (latestHumitureCheckResultTemp.getResult() == Constants.INT_ONE ? "合格" : "不合格") + END_LI +
                BEGIN_LI + "最新检测时间：" + DateTimeUtil.localDateTime2String(latestHumitureCheckResultTemp.getLatestInspectDate()) + END_LI +
                BEGIN_LI + "下次检测到期时间：" + DateTimeUtil.localDateTime2String(latestHumitureCheckResultTemp.getNextInspectDate()) + END_LI;
        sendMessageDTO.setMessageData(htmlBuilder);
        rbaseTaskMessageProxy.groupMessage(sendMessageDTO);
    }

    /**
     * Rworker验证工位区域温湿度合规性
     * @param workCellId 工位ID
     */
    public void validate(Long workCellId) {
        AreaWorkCell areaWorkCell = areaWorkCellRepository.findByWorkCellIdAndIsEnableAndDeleted(workCellId, Constants.TRUE, Constants.LONG_ZERO).orElse(null);
        if (areaWorkCell == null) {
            return;
        }
        Optional<HumitureStandard> humitureStandardOptional = humitureStandardRepository.findByAreaIdAndDeleted(areaWorkCell.getArea().getId(), Constants.LONG_ZERO);
        if (humitureStandardOptional.isPresent()) {
            HumitureCheckHistory latestHumitureCheckHistory = humitureCheckHistoryRepository.findTop1ByAreaIdAndDeletedOrderByIdDesc(areaWorkCell.getArea().getId(), Constants.LONG_ZERO).orElse(null);
            if (null == latestHumitureCheckHistory) {
                throw new ResponseException("error.notExistLatestHumitureCheckRecord", "区域温湿度检测记录不存在");
            }
            if (latestHumitureCheckHistory.getResult() == HumitureEnum.WARNING.getCategory()) {
                throw new ResponseException("error.areaHumitureWarning", "区域温湿度检测结果预警,请尽快处理");
            }
            if (latestHumitureCheckHistory.getResult() == HumitureEnum.STOPPED.getCategory()) {
                throw new ResponseException("error.latestHumitureCheckResultNotQualified", "区域温湿度检测结果停线");
            }
            Optional<LatestHumitureCheckResult> latestEnviromentCheckResultOptional = latestHumitureCheckResultRepository.findByAreaIdAndDeleted(areaWorkCell.getArea().getId(), Constants.LONG_ZERO);
            if (latestHumitureCheckHistory.getResult() == HumitureEnum.QUALIFIED.getCategory() && latestEnviromentCheckResultOptional.isPresent() && latestEnviromentCheckResultOptional.get().getNextInspectDate().isBefore(LocalDateTime.now())) {
                throw new ResponseException("error.humitureCheckTimeExpired", "区域温湿度检测超期未检");
            }
        }
    }
}
