package net.airuima.rbase.dto.maintain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Transient;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.dto.AbstractDto;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.dto.organization.StaffDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class MaintainHistoryDetailDTO extends AbstractDto {

    /**
     * 维修分析历史
     */
    @Schema(description = "维修分析历史")
    private MaintainHistoryDTO maintainHistory;

    /**
     * 在线返修单和正常单关联信息
     */
    @Schema(description = "在线返修单和正常单关联信息")
    private WsRework wsRework;

    /**
     * 维修分析方案
     */
    @Schema(description = "维修分析方案")
    private MaintainCaseDTO maintainCase;

    /**
     * 不良项Id
     */
    @Schema(description = "不良项Id")
    private UnqualifiedItem unqualifiedItem;

    /**
     * 维修数量
     */
    @Schema(description = "维修数量")
    private int number;


    /**
     * 处理方式：-1:待处理，0：报废，1：返工;2:放行，3：退库
     */
    @Schema(description = "处理方式：-1:待处理，0：报废，1：返工;2:放行；3：退库")
    private int result;

    /**
     * 开始返工的工序ID
     */
    @Schema(description = "开始返工的工序ID")
    private Step reWorkStep;

    /**
     * 是否需要替换料(0:否;1:是)
     */
    @Schema(description = "是否替换料(0:否;1:是)")
    private boolean isReplaceMaterial;


    /**
     * 返修流程(0:返修工艺路线, 1:原工艺路线,2:多种返修路线)
     */
    @Schema(description = "返修流程(0:返修工艺路线, 1:原工艺路线,2:多种返修路线)", required = true)
    private int reworkCategory;

    /**
     * 操作员工id
     */
    @Schema(description = "操作员工id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    private StaffDTO staffDto = new StaffDTO();

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


    /**
     * 维修记录时间
     */
    @Schema(description = "维修记录时间")
    private LocalDateTime recordDate;

    /**
     * 阶段：0：分析，1:维修
     */
    @Schema(description = "阶段：0：分析，1:维修")
    private int stage;

    public MaintainHistoryDTO getMaintainHistory() {
        return maintainHistory;
    }

    public MaintainHistoryDetailDTO setMaintainHistory(MaintainHistoryDTO maintainHistory) {
        this.maintainHistory = maintainHistory;
        return this;
    }

    public WsRework getWsRework() {
        return wsRework;
    }

    public MaintainHistoryDetailDTO setWsRework(WsRework wsRework) {
        this.wsRework = wsRework;
        return this;
    }

    public MaintainCaseDTO getMaintainCase() {
        return maintainCase;
    }

    public MaintainHistoryDetailDTO setMaintainCase(MaintainCaseDTO maintainCase) {
        this.maintainCase = maintainCase;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public MaintainHistoryDetailDTO setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public MaintainHistoryDetailDTO setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getResult() {
        return result;
    }

    public MaintainHistoryDetailDTO setResult(int result) {
        this.result = result;
        return this;
    }

    public Step getReWorkStep() {
        return reWorkStep;
    }

    public MaintainHistoryDetailDTO setReWorkStep(Step reWorkStep) {
        this.reWorkStep = reWorkStep;
        return this;
    }

    public boolean getIsReplaceMaterial() {
        return isReplaceMaterial;
    }

    public MaintainHistoryDetailDTO setIsReplaceMaterial(boolean replaceMaterial) {
        isReplaceMaterial = replaceMaterial;
        return this;
    }

    public int getReworkCategory() {
        return reworkCategory;
    }

    public MaintainHistoryDetailDTO setReworkCategory(int reworkCategory) {
        this.reworkCategory = reworkCategory;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public MaintainHistoryDetailDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public MaintainHistoryDetailDTO setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public String getNote() {
        return note;
    }

    public MaintainHistoryDetailDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public MaintainHistoryDetailDTO setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public int getStage() {
        return stage;
    }

    public MaintainHistoryDetailDTO setStage(int stage) {
        this.stage = stage;
        return this;
    }
}
