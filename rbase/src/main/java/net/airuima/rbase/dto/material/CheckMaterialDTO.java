package net.airuima.rbase.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 导入核料信息DTO
 *
 * <AUTHOR>
 * @date 2021/3/8
 */
@Schema(description = "导入核料信息DTO")
public class CheckMaterialDTO {

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    private String workSheetSerialNumber;

    /**
     * 供应商编码
     */
    @Schema(description = "供应商编码")
    private String supplierCode;

    /**
     * 核料凭证
     */
    @Schema(description = "核料凭证")
    private String checkMaterialCode;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    private String materialBatch;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Double materialNumber;

    public CheckMaterialDTO(Map<String, Object> map) {
        this.workSheetSerialNumber = Optional.ofNullable(map.get("工单号"))
                .map(Object::toString).orElse(null);
        this.supplierCode = Optional.ofNullable(map.get("供应商编码"))
                .map(Object::toString).orElse(null);
        this.checkMaterialCode = Optional.ofNullable(map.get("核料凭证"))
                .map(Object::toString).orElse(null);
        this.materialCode = Optional.ofNullable(map.get("物料编码"))
                .map(Object::toString).orElse(null);
        this.materialBatch = Optional.ofNullable(map.get("物料批次"))
                .map(Object::toString).orElse(null);
        this.materialNumber = Optional.ofNullable(map.get("数量"))
                .map(Object::toString).map(Double::valueOf).orElse(null);
    }

    public CheckMaterialDTO(CheckMaterialDTO checkMaterialDTO) {
        this.workSheetSerialNumber = checkMaterialDTO.getWorkSheetSerialNumber();
        this.supplierCode = checkMaterialDTO.getSupplierCode();
        this.checkMaterialCode = checkMaterialDTO.getCheckMaterialCode();
        this.materialCode = checkMaterialDTO.getMaterialCode();
        this.materialBatch = checkMaterialDTO.getMaterialBatch();
        this.materialNumber = checkMaterialDTO.getMaterialNumber();
    }

    public String getWorkSheetSerialNumber() {
        return workSheetSerialNumber;
    }

    public CheckMaterialDTO setWorkSheetSerialNumber(String workSheetSerialNumber) {
        this.workSheetSerialNumber = workSheetSerialNumber;
        return this;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public CheckMaterialDTO setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
        return this;
    }

    public String getCheckMaterialCode() {
        return checkMaterialCode;
    }

    public CheckMaterialDTO setCheckMaterialCode(String checkMaterialCode) {
        this.checkMaterialCode = checkMaterialCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public CheckMaterialDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getMaterialBatch() {
        return materialBatch;
    }

    public CheckMaterialDTO setMaterialBatch(String materialBatch) {
        this.materialBatch = materialBatch;
        return this;
    }

    public Double getMaterialNumber() {
        return materialNumber;
    }

    public CheckMaterialDTO setMaterialNumber(Double materialNumber) {
        this.materialNumber = materialNumber;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CheckMaterialDTO that = (CheckMaterialDTO) o;
        return Objects.equals(workSheetSerialNumber, that.workSheetSerialNumber) && Objects.equals(checkMaterialCode, that.checkMaterialCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(workSheetSerialNumber, checkMaterialCode);
    }
}
