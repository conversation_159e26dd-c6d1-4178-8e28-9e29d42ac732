package net.airuima.rbase.dto.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.batch.WsStepUnqualifiedItem;

import java.util.List;
import java.util.Set;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-04-29
 */
@Schema(description = "获取待生成返工的不良信息DTO")
public class ReworkGetSubWsDTO {

    @Schema(description = "工单主键ID",type = "integer",format = "int64")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workSheetId;
    @ArraySchema(schema = @Schema(description = "不良项目组别列表"))
    private List<UnqualifiedGroupDTO> unqualifiedGroupDtoList;
    @Schema(description = "不良工序DTO")
    private List<UnqualifiedItemStepDTO> subWsStepDtoList;

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public ReworkGetSubWsDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public List<UnqualifiedGroupDTO> getUnqualifiedGroupDtoList() {
        return unqualifiedGroupDtoList;
    }

    public ReworkGetSubWsDTO setUnqualifiedGroupDtoList(List<UnqualifiedGroupDTO> subWsUnqualifiedGroupDtoList) {
        this.unqualifiedGroupDtoList = subWsUnqualifiedGroupDtoList;
        return this;
    }

    public List<UnqualifiedItemStepDTO> getSubWsStepDtoList() {
        return subWsStepDtoList;
    }

    public ReworkGetSubWsDTO setSubWsStepDtoList(List<UnqualifiedItemStepDTO> subWsStepDtoList) {
        this.subWsStepDtoList = subWsStepDtoList;
        return this;
    }

    @Schema(description = "不良组别DTO")
    public static class UnqualifiedGroupDTO {

        @Schema(description = "不良组别ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unqualifiedGroupId;
        @Schema(description = "不良组别名称")
        private String unqualifiedGroupName;
        @Schema(description = "不良组别编码")
        private String unqualifiedGroupCode;
        @Schema(description = "不良组别汇总数量")
        private Integer number;
        @Schema(description = "各个子工单的不良组别数量分布信息")
        private List<SubWsUnqualifiedGroupInfoDTO> subWsUnqualifiedGroupInfoDtoList;
        @Schema(description = "配置的在线返修流程")
        private Set<WorkFlow> workFlow;

        public Long getUnqualifiedGroupId() {
            return unqualifiedGroupId;
        }

        public UnqualifiedGroupDTO setUnqualifiedGroupId(Long unqualifiedGroupId) {
            this.unqualifiedGroupId = unqualifiedGroupId;
            return this;
        }

        public String getUnqualifiedGroupName() {
            return unqualifiedGroupName;
        }

        public UnqualifiedGroupDTO setUnqualifiedGroupName(String unqualifiedGroupName) {
            this.unqualifiedGroupName = unqualifiedGroupName;
            return this;
        }

        public String getUnqualifiedGroupCode() {
            return unqualifiedGroupCode;
        }

        public UnqualifiedGroupDTO setUnqualifiedGroupCode(String unqualifiedGroupCode) {
            this.unqualifiedGroupCode = unqualifiedGroupCode;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public UnqualifiedGroupDTO setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public List<SubWsUnqualifiedGroupInfoDTO> getSubWsUnqualifiedGroupInfoDtoList() {
            return subWsUnqualifiedGroupInfoDtoList;
        }

        public UnqualifiedGroupDTO setSubWsUnqualifiedGroupInfoDtoList(List<SubWsUnqualifiedGroupInfoDTO> subWsUnqualifiedGroupInfoDtoList) {
            this.subWsUnqualifiedGroupInfoDtoList = subWsUnqualifiedGroupInfoDtoList;
            return this;
        }

        public Set<WorkFlow> getWorkFlow() {
            return workFlow;
        }

        public UnqualifiedGroupDTO setWorkFlow(Set<WorkFlow> workFlow) {
            this.workFlow = workFlow;
            return this;
        }
    }

    @Schema(description = "产生不良的工序DTO")
    public static class UnqualifiedItemStepDTO {
        @Schema(description = "不良工序ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long stepId;
        @Schema(description = "不良工序名称")
        private String stepName;
        @Schema(description = "不良工序编码")
        private String stepCode;
        @Schema(description = "不良工序汇总数量")
        private Integer number;
        @Schema(description = "各个子工单的不良工序数量分布信息")
        private List<SubWsUnqualifiedGroupInfoDTO> subStepInfoDtoList;
        @Schema(description = "配置的在线返修流程")
        private Set<WorkFlow> workFlow;

        public Long getStepId() {
            return stepId;
        }

        public UnqualifiedItemStepDTO setStepId(Long stepId) {
            this.stepId = stepId;
            return this;
        }

        public String getStepName() {
            return stepName;
        }

        public UnqualifiedItemStepDTO setStepName(String stepName) {
            this.stepName = stepName;
            return this;
        }

        public String getStepCode() {
            return stepCode;
        }

        public UnqualifiedItemStepDTO setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public UnqualifiedItemStepDTO setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public List<SubWsUnqualifiedGroupInfoDTO> getSubStepInfoDtoList() {
            return subStepInfoDtoList;
        }

        public UnqualifiedItemStepDTO setSubStepInfoDtoList(List<SubWsUnqualifiedGroupInfoDTO> subStepInfoDtoList) {
            this.subStepInfoDtoList = subStepInfoDtoList;
            return this;
        }

        public Set<WorkFlow> getWorkFlow() {
            return workFlow;
        }

        public UnqualifiedItemStepDTO setWorkFlow(Set<WorkFlow> workFlow) {
            this.workFlow = workFlow;
            return this;
        }
    }

    @Schema(description = "子工单不良数量信息DTO")
    public static class SubWsUnqualifiedGroupInfoDTO {
        @Schema(description = "子工单ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long subWorkSheetId;
        @Schema(description = "子工单号")
        private String serialNumber;
        @Schema(description = "不良数量")
        private Integer number;
        @Schema(description = "子工单对应的不良项目工序DTO")
        private List<UnqualifiedItemStepDTO> unqualifiedItemStepDtoList;


        @Schema(description = "不良项目工序DTO")
        public static class UnqualifiedItemStepDTO{
            @Schema(description = "不良项目ID")
            @JsonSerialize(using = ToStringSerializer.class)
            private Long unqualifiedItemId;

            @Schema(description = "不良项目对应的工序ID")
            @JsonSerialize(using = ToStringSerializer.class)
            private Long unqualifiedStepId;

            public UnqualifiedItemStepDTO() {
            }

            public UnqualifiedItemStepDTO(WsStepUnqualifiedItem wsStepUnqualifiedItem) {
                this.unqualifiedItemId = wsStepUnqualifiedItem.getUnqualifiedItem().getId();
                this.unqualifiedStepId = wsStepUnqualifiedItem.getStep().getId();
            }

            public Long getUnqualifiedItemId() {
                return unqualifiedItemId;
            }

            public UnqualifiedItemStepDTO setUnqualifiedItemId(Long unqualifiedItemId) {
                this.unqualifiedItemId = unqualifiedItemId;
                return this;
            }

            public Long getUnqualifiedStepId() {
                return unqualifiedStepId;
            }

            public UnqualifiedItemStepDTO setUnqualifiedStepId(Long unqualifiedStepId) {
                this.unqualifiedStepId = unqualifiedStepId;
                return this;
            }

        }

        public SubWsUnqualifiedGroupInfoDTO() {

        }

        public SubWsUnqualifiedGroupInfoDTO(Long subWorkSheetId, String serialNumber, Integer number, List<UnqualifiedItemStepDTO> unqualifiedItemStepDtoList) {
            this.subWorkSheetId = subWorkSheetId;
            this.serialNumber = serialNumber;
            this.number = number;
            this.unqualifiedItemStepDtoList = unqualifiedItemStepDtoList;
        }

        public Long getSubWorkSheetId() {
            return subWorkSheetId;
        }

        public SubWsUnqualifiedGroupInfoDTO setSubWorkSheetId(Long subWorkSheetId) {
            this.subWorkSheetId = subWorkSheetId;
            return this;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public SubWsUnqualifiedGroupInfoDTO setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public SubWsUnqualifiedGroupInfoDTO setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public List<UnqualifiedItemStepDTO> getUnqualifiedItemStepDtoList() {
            return unqualifiedItemStepDtoList;
        }

        public SubWsUnqualifiedGroupInfoDTO setUnqualifiedItemStepDtoList(List<UnqualifiedItemStepDTO> unqualifiedItemStepDtoList) {
            this.unqualifiedItemStepDtoList = unqualifiedItemStepDtoList;
            return this;
        }
    }
}
