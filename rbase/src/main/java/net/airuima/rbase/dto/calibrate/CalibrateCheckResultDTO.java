package net.airuima.rbase.dto.calibrate;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;
import net.airuima.dto.AbstractDto;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.rbase.dto.standardpart.StandardPartDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class CalibrateCheckResultDTO extends AbstractDto {

    /**
     * 台位
     */
    @Schema(description = "台位", required = true)
    private String workCellCode;


    /**
     * 标准件ID
     */
    @Schema(description = "标准件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardPartId;

    /**
     * 标准件DTO
     */
    @Schema(description = "标准件DTO", required = true)
    private StandardPartDTO standardPartDto = new StandardPartDTO();

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    private LocalDateTime testTime;

    /**
     * 测试人员
     */
    @Schema(description = "测试人员")
    private String tester;

    /**
     * 是否为最新记录
     */
    @Schema(description = "是否为最新记录")
    private boolean isLatest;

    /**
     * 工位ID
     */
    @Schema(description = "工位ID")
    private WorkCell workCell;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long facilityId;

    @Schema(description = "设备DTO")
    private FacilityDTO facilityDto = new FacilityDTO();

    /**
     * 标准件CODE
     */
    @Schema(description = "标准件CODE")
    private String standardPartCode;

    /**
     * 校准程序，0：内校，1：外校
     **/
    @Schema(description = "校准程序，0：内校，1：外校", required = true)
    private int process;

    /**
     * 校准单位
     */
    @Schema(description = "校准单位")
    private String calibrateCompany;

    /**
     * 设备外观情况
     */
    @Schema(description = "设备外观情况")
    private String appearance;

    /**
     * 校准合格证数量
     */
    @Schema(description = "校准合格证数量, 对应File映射表category的0")
    private int certificateNumber;

    /**
     * 校准合格证数量
     */
    @Schema(description = "校准证明报告书数量, 对应File映射表category的1")
    private int reportNumber;

    /**
     * 是否合格 0:不合格;1:合格
     */
    @Schema(description = "是否合格 0:不合格;1:合格")
    private boolean result;

    public String getWorkCellCode() {
        return workCellCode;
    }

    public CalibrateCheckResultDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public Long getStandardPartId() {
        return standardPartId;
    }

    public CalibrateCheckResultDTO setStandardPartId(Long standardPartId) {
        this.standardPartId = standardPartId;
        return this;
    }

    public StandardPartDTO getStandardPartDto() {
        return standardPartDto;
    }

    public CalibrateCheckResultDTO setStandardPartDto(StandardPartDTO standardPartDto) {
        this.standardPartDto = standardPartDto;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public CalibrateCheckResultDTO setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public CalibrateCheckResultDTO setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public boolean getIsLatest() {
        return isLatest;
    }

    public CalibrateCheckResultDTO setIsLatest(boolean latest) {
        isLatest = latest;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public CalibrateCheckResultDTO setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public CalibrateCheckResultDTO setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public CalibrateCheckResultDTO setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    public String getStandardPartCode() {
        return standardPartCode;
    }

    public CalibrateCheckResultDTO setStandardPartCode(String standardPartCode) {
        this.standardPartCode = standardPartCode;
        return this;
    }

    public int getProcess() {
        return process;
    }

    public CalibrateCheckResultDTO setProcess(int process) {
        this.process = process;
        return this;
    }

    public String getCalibrateCompany() {
        return calibrateCompany;
    }

    public CalibrateCheckResultDTO setCalibrateCompany(String calibrateCompany) {
        this.calibrateCompany = calibrateCompany;
        return this;
    }

    public String getAppearance() {
        return appearance;
    }

    public CalibrateCheckResultDTO setAppearance(String appearance) {
        this.appearance = appearance;
        return this;
    }

    public int getCertificateNumber() {
        return certificateNumber;
    }

    public CalibrateCheckResultDTO setCertificateNumber(int certificateNumber) {
        this.certificateNumber = certificateNumber;
        return this;
    }

    public int getReportNumber() {
        return reportNumber;
    }

    public CalibrateCheckResultDTO setReportNumber(int reportNumber) {
        this.reportNumber = reportNumber;
        return this;
    }

    public boolean getResult() {
        return result;
    }

    public CalibrateCheckResultDTO setResult(boolean result) {
        this.result = result;
        return this;
    }
}
