package net.airuima.rbase.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.util.NumberUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * 工单完成率报表DTO
 */
@Schema(description = "工单完成率报表DTO")
public class WorkSheetFinishReportDTO {
    private WorkSheetFinishReportDTO() {
        // 静态成员的集合，不需要实例化
    }
    /**
     * 报表请求信息
     */
    @Schema(description = "报表请求信息")
    public static class RequestInfo extends PageDTO{

        /**
         * 总工单号
         */
        @Schema(description = "总工单号")
        private String wsSerialNumber;

        /**
         * 产品谱系id(最小层级)
         */
        @Schema(description = "产品谱系id(最小层级)")
        private Long pedigreeId;

        /**
         * 生产线id
         */
        @Schema(description = "产线id")
        private Long workLineId;

        /**
         * 下单日期开始范围
         */
        @Schema(description = "下单日期开始范围")
        private LocalDate startDate;

        /**
         * 下单日期结束范围
         */
        @Schema(description = "下单日期结束范围")
        private LocalDate endDate;

        public String getWsSerialNumber() {
            return wsSerialNumber;
        }

        public void setWsSerialNumber(String wsSerialNumber) {
            this.wsSerialNumber = wsSerialNumber;
        }

        public Long getPedigreeId() {
            return pedigreeId;
        }

        public void setPedigreeId(Long pedigreeId) {
            this.pedigreeId = pedigreeId;
        }

        public Long getWorkLineId() {
            return workLineId;
        }

        public void setWorkLineId(Long workLineId) {
            this.workLineId = workLineId;
        }

        public LocalDate getStartDate() {
            return startDate;
        }

        public void setStartDate(LocalDate startDate) {
            this.startDate = startDate;
        }

        public LocalDate getEndDate() {
            return endDate;
        }

        public void setEndDate(LocalDate endDate) {
            this.endDate = endDate;
        }

    }

    /**
     * 报表返回数据
     */
    @Schema(description = "报表返回数据")
    public static class ResponseInfo extends PageDTO{

        /**
         * 工单完工信息
         */
        @Schema(description = "工单完工信息列表")
        private List<WsFinishDTO> wsFinishDtoList;

        public List<WsFinishDTO> getWsFinishDtoList() {
            return wsFinishDtoList;
        }

        public ResponseInfo setWsFinishDtoList(List<WsFinishDTO> wsFinishDtoList) {
            this.wsFinishDtoList = wsFinishDtoList;
            return this;
        }
    }

    /**
     * 工单完工信息
     */
    @Schema(description = "工单完工信息DTO")
    public static class WsFinishDTO{
        /**
         * 组织架构名称
         */
        @Schema(description = "组织架构名称")
        private String organizationName;

        /**
         * 组织架构编码
         */
        @Schema(description = "组织架构编码")
        private String organizationCode;

        /**
         * 工单编码
         */
        @Schema(description = "工单编码")
        private String serialNumber;

        /**
         * 子工单数量
         */
        @Schema(description = "子工单数量")
        private Integer subWsNumber;

        /**
         * 完成子工单数量
         */
        @Schema(description = "完成子工单数量")
        private Integer finishSubWsNumber;

        /**
         * 完成率
         */
        @Schema(description = "完成率")
        private Double finishRate;

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public Integer getSubWsNumber() {
            return subWsNumber;
        }

        public void setSubWsNumber(Integer subWsNumber) {
            this.subWsNumber = subWsNumber;
        }

        public Integer getFinishSubWsNumber() {
            return finishSubWsNumber;
        }

        public void setFinishSubWsNumber(Integer finishSubWsNumber) {
            this.finishSubWsNumber = finishSubWsNumber;
        }

        public Double getFinishRate() {
            if (subWsNumber != null && finishSubWsNumber != null && !Integer.valueOf(Constants.INT_ZERO).equals(subWsNumber)) {
                finishRate = NumberUtils.divide(finishSubWsNumber, subWsNumber, Constants.INT_FOUR).doubleValue();
            }
            return finishRate;
        }

        public void setFinishRate(Double finishRate) {
            this.finishRate = finishRate;
        }

        public String getOrganizationName() {
            return organizationName;
        }

        public void setOrganizationName(String organizationName) {
            this.organizationName = organizationName;
        }

        public String getOrganizationCode() {
            return organizationCode;
        }

        public void setOrganizationCode(String organizationCode) {
            this.organizationCode = organizationCode;
        }
    }
}
