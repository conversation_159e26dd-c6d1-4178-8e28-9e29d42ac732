package net.airuima.rbase.dto.qms;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;

@Schema(name = "国标类型详情表(GbtDetail)", description = "国标类型详情表")
public class GbtDetailDTO extends AbstractDto implements Serializable {

    /**
     * 国标类型
     */
    @Schema(description = "国标类型")
    private GbtDTO gbt;

    /**
     * 检验水平
     */
    @Schema(description = "检验水平")
    private String insolationLevel;

    /**
     * 接收质量限AQL
     */
    @Schema(description = "接收质量限AQL")
    private double aql;

    /**
     * 下限值
     */
    @Schema(description = "下限值")
    private int lowerLimit;

    /**
     * 上限值
     */
    @Schema(description = "上限值")
    private int upperLimit;

    /**
     * 样本量
     */
    @Schema(description = "样本量")
    private int number;

    /**
     * 允收数Ac
     */
    @Schema(description = "允收数Ac")
    private int ac;

    /**
     * 拒收数Re
     */
    @Schema(description = "拒收数Re")
    private int re;

    public GbtDTO getGbt() {
        return gbt;
    }

    public GbtDetailDTO setGbt(GbtDTO gbt) {
        this.gbt = gbt;
        return this;
    }

    public String getInsolationLevel() {
        return insolationLevel;
    }

    public GbtDetailDTO setInsolationLevel(String insolationLevel) {
        this.insolationLevel = insolationLevel;
        return this;
    }

    public double getAql() {
        return aql;
    }

    public GbtDetailDTO setAql(double aql) {
        this.aql = aql;
        return this;
    }

    public int getLowerLimit() {
        return lowerLimit;
    }

    public GbtDetailDTO setLowerLimit(int lowerLimit) {
        this.lowerLimit = lowerLimit;
        return this;
    }

    public int getUpperLimit() {
        return upperLimit;
    }

    public GbtDetailDTO setUpperLimit(int upperLimit) {
        this.upperLimit = upperLimit;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public GbtDetailDTO setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getAc() {
        return ac;
    }

    public GbtDetailDTO setAc(int ac) {
        this.ac = ac;
        return this;
    }

    public int getRe() {
        return re;
    }

    public GbtDetailDTO setRe(int re) {
        this.re = re;
        return this;
    }
}
