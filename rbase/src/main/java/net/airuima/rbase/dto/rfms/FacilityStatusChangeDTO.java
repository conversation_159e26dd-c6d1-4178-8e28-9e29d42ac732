package net.airuima.rbase.dto.rfms;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 设备状态修改DTO
 * <AUTHOR>
 */
public class FacilityStatusChangeDTO {

    /**
     * 设备ID
     */
    private Long facilityId;

    /**
     * 原始状态
     */
    private int originStatus;

    /**
     * 目标状态
     */
    private int targetStatus;

    /**
     * 记录日期
     */
    private LocalDate recordDate;

    /**
     * 记录时间
     */
    private LocalDateTime recordTime;

    public Long getFacilityId() {
        return facilityId;
    }

    public FacilityStatusChangeDTO setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public int getOriginStatus() {
        return originStatus;
    }

    public FacilityStatusChangeDTO setOriginStatus(int originStatus) {
        this.originStatus = originStatus;
        return this;
    }

    public int getTargetStatus() {
        return targetStatus;
    }

    public FacilityStatusChangeDTO setTargetStatus(int targetStatus) {
        this.targetStatus = targetStatus;
        return this;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public FacilityStatusChangeDTO setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public FacilityStatusChangeDTO setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }
}
