package net.airuima.rbase.dto.rworker.maintain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Set;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/3/23
 */
@Schema(description = "Rworker保存维修分析记录信息")
public class MaintainHistorySaveDTO {
    /**
     * 维修分析类型：0：sn单支,1:容器,2:工单
     */
    @Schema(description = "维修分析类型：0：sn单支,1:容器,2:工单")
    private Integer maintainType;

    /**
     * 员工id
     */
    @Schema(description = "员工id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    /**
     * Rworker保存维修分析记录DTO
     */
    @Schema(description = "Rworker保存维修分析记录DTO")
    private List<MaintainAnalyseInfo> maintainAnalyseInfoList;

    public Integer getMaintainType() {
        return maintainType;
    }

    public MaintainHistorySaveDTO setMaintainType(Integer maintainType) {
        this.maintainType = maintainType;
        return this;
    }

    public List<MaintainAnalyseInfo> getMaintainAnalyseInfoList() {
        return maintainAnalyseInfoList;
    }

    public MaintainHistorySaveDTO setMaintainAnalyseInfoList(List<MaintainAnalyseInfo> maintainAnalyseInfoList) {
        this.maintainAnalyseInfoList = maintainAnalyseInfoList;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public MaintainHistorySaveDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    @Schema(description = "报废处理")
    public static class ScrapHandleInfo{
        /**
         * 处理数量
         */
        @Schema(description = "处理数量")
        private Integer number;

        /**
         * 不良项目Id
         */
        @Schema(description = "不良项目Id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unqualifiedItemId;

        /**
         * 维修分析原因
         */
        @Schema(description = "维修分析原因")
        private String reason;

        public Integer getNumber() {
            return number;
        }

        public ScrapHandleInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Long getUnqualifiedItemId() {
            return unqualifiedItemId;
        }

        public ScrapHandleInfo setUnqualifiedItemId(Long unqualifiedItemId) {
            this.unqualifiedItemId = unqualifiedItemId;
            return this;
        }

        public String getReason() {
            return reason;
        }

        public ScrapHandleInfo setReason(String reason) {
            this.reason = reason;
            return this;
        }
    }
    @Schema(description = "放行处理")
    public static class ReleaseHandleInfo{

        /**
         * 放行数量
         */
        @Schema(description = "放行数量")
        private Integer number;

        /**
         * 放行原因
         */
        @Schema(description = "放行原因")
        private String reason;

        public Integer getNumber() {
            return number;
        }

        public ReleaseHandleInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public String getReason() {
            return reason;
        }

        public ReleaseHandleInfo setReason(String reason) {
            this.reason = reason;
            return this;
        }
    }

    @Schema(description = "返修处理")
    public static class ReworkHandleInfo{
        /**
         * 处理数量
         */
        @Schema(description = "处理数量")
        private Integer number;

        @Schema(description = "不良项目Id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unqualifiedItemId;

        /**
         * 维修方案ID
         */
        @Schema(description = "维修方案ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long maintainCaseId;


        /**
         * 返修流程(0:返修工艺路线, 1:原工艺路线)
         */
        @Schema(description = "返修流程(0:返修工艺路线, 1:原工艺路线)")
        private Integer reworkCategory;

        /**
         * 退补料清单
         */
        @Schema(description = "退补料清单")
        private Set<MaintainMaterialExchangeInfo> maintainMaterialExchangeInfos;

        /**
         * 返修工艺路线
         */
        @Schema(description = "返修工艺路线")
        private String workFlowCode;

        /**
         * 返修指定开始工序
         */
        @Schema(description = "返修指定开始工序")
        private String stepCode;

        /**
         * 维修分析原因
         */
        @Schema(description = "维修分析原因")
        private String reason;

        public Integer getNumber() {
            return number;
        }

        public ReworkHandleInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Long getUnqualifiedItemId() {
            return unqualifiedItemId;
        }

        public ReworkHandleInfo setUnqualifiedItemId(Long unqualifiedItemId) {
            this.unqualifiedItemId = unqualifiedItemId;
            return this;
        }

        public Long getMaintainCaseId() {
            return maintainCaseId;
        }

        public ReworkHandleInfo setMaintainCaseId(Long maintainCaseId) {
            this.maintainCaseId = maintainCaseId;
            return this;
        }

        public Integer getReworkCategory() {
            return reworkCategory;
        }

        public ReworkHandleInfo setReworkCategory(Integer reworkCategory) {
            this.reworkCategory = reworkCategory;
            return this;
        }

        public Set<MaintainMaterialExchangeInfo> getMaintainMaterialExchangeInfos() {
            return maintainMaterialExchangeInfos;
        }

        public ReworkHandleInfo setMaintainMaterialExchangeInfos(Set<MaintainMaterialExchangeInfo> maintainMaterialExchangeInfos) {
            this.maintainMaterialExchangeInfos = maintainMaterialExchangeInfos;
            return this;
        }

        public String getWorkFlowCode() {
            return workFlowCode;
        }

        public ReworkHandleInfo setWorkFlowCode(String workFlowCode) {
            this.workFlowCode = workFlowCode;
            return this;
        }

        public String getStepCode() {
            return stepCode;
        }

        public ReworkHandleInfo setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }

        public String getReason() {
            return reason;
        }

        public ReworkHandleInfo setReason(String reason) {
            this.reason = reason;
            return this;
        }
    }

    @Schema(description = "Rworker保存维修分析记录DTO")
    public static class MaintainAnalyseInfo{


        /**
         * 维修分析编号：sn、容器号、工单号
         */
        @Schema(description = "维修分析编号")
        private String maintainTypeCode;


        /**
         * 维修分析记录ID
         */
        @Schema(description = "维修分析记录ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long maintainHistoryId;

        /**
         * 报废处理
         */
        @Schema(description = "报废处理")
        private List<ScrapHandleInfo> scrapHandleInfos;

        /**
         * 放行处理
         */
        @Schema(description = "放行处理")
        private ReleaseHandleInfo releaseHandleInfo;

        /**
         * 返修处理
         */
        @Schema(description = "返修处理")
        private List<ReworkHandleInfo> reworkHandleInfos;


        public List<ScrapHandleInfo> getScrapHandleInfos() {
            return scrapHandleInfos;
        }

        public MaintainAnalyseInfo setScrapHandleInfos(List<ScrapHandleInfo> scrapHandleInfos) {
            this.scrapHandleInfos = scrapHandleInfos;
            return this;
        }

        public ReleaseHandleInfo getReleaseHandleInfo() {
            return releaseHandleInfo;
        }

        public MaintainAnalyseInfo setReleaseHandleInfo(ReleaseHandleInfo releaseHandleInfo) {
            this.releaseHandleInfo = releaseHandleInfo;
            return this;
        }

        public List<ReworkHandleInfo> getReworkHandleInfos() {
            return reworkHandleInfos;
        }

        public MaintainAnalyseInfo setReworkHandleInfos(List<ReworkHandleInfo> reworkHandleInfos) {
            this.reworkHandleInfos = reworkHandleInfos;
            return this;
        }

        public String getMaintainTypeCode() {
            return maintainTypeCode;
        }

        public MaintainAnalyseInfo setMaintainTypeCode(String maintainTypeCode) {
            this.maintainTypeCode = maintainTypeCode;
            return this;
        }

        public Long getMaintainHistoryId() {
            return maintainHistoryId;
        }

        public MaintainAnalyseInfo setMaintainHistoryId(Long maintainHistoryId) {
            this.maintainHistoryId = maintainHistoryId;
            return this;
        }
    }

    @Schema(description = "退补料信息")
    public static class MaintainMaterialExchangeInfo{
        @Schema(description = "退料物料详情")
        private MaterialBatchInfo returnMaterialInfo;
        @Schema(description = "替换料详情")
        private MaterialBatchInfo replaceMaterialInfo;

        public MaterialBatchInfo getReturnMaterialInfo() {
            return returnMaterialInfo;
        }

        public MaintainMaterialExchangeInfo setReturnMaterialInfo(MaterialBatchInfo returnMaterialInfo) {
            this.returnMaterialInfo = returnMaterialInfo;
            return this;
        }

        public MaterialBatchInfo getReplaceMaterialInfo() {
            return replaceMaterialInfo;
        }

        public MaintainMaterialExchangeInfo setReplaceMaterialInfo(MaterialBatchInfo replaceMaterialInfo) {
            this.replaceMaterialInfo = replaceMaterialInfo;
            return this;
        }
    }

    @Schema(description = "物料批次基础信息")
    public static class MaterialBatchInfo{
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "物料id")
        private Long materialId;

        @Schema(description = "物料批次")
        private String materialBatch;

        @Schema(description = "数量")
        private Double number;

        public Long getMaterialId() {
            return materialId;
        }

        public MaterialBatchInfo setMaterialId(Long materialId) {
            this.materialId = materialId;
            return this;
        }

        public String getMaterialBatch() {
            return materialBatch;
        }

        public MaterialBatchInfo setMaterialBatch(String materialBatch) {
            this.materialBatch = materialBatch;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public MaterialBatchInfo setNumber(Double number) {
            this.number = number;
            return this;
        }
    }
}
