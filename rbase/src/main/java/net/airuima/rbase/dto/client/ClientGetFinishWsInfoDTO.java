package net.airuima.rbase.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * RWorker请求(子)工单完成后相关信息DTO
 *
 * <AUTHOR>
 * @date 2022/3/24
 */
@Schema(description = "RWorker请求(子)工单完成后相关信息DTO")
public class ClientGetFinishWsInfoDTO {

    /**
     * 总工单号
     */
    @Schema(description = "总工单号")
    private String wsSerialNumber;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    private String subWsSerialNumber;

    public String getWsSerialNumber() {
        return wsSerialNumber;
    }

    public ClientGetFinishWsInfoDTO setWsSerialNumber(String wsSerialNumber) {
        this.wsSerialNumber = wsSerialNumber;
        return this;
    }

    public String getSubWsSerialNumber() {
        return subWsSerialNumber;
    }

    public ClientGetFinishWsInfoDTO setSubWsSerialNumber(String subWsSerialNumber) {
        this.subWsSerialNumber = subWsSerialNumber;
        return this;
    }
}
