package net.airuima.rbase.dto.sync;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/3/15
 */
@Schema(description = "同步工艺路线dto")
public class SyncWorkFlowDTO {

    /**
     * 工艺路线Id
     */
    @Schema(description = "工艺路线Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工艺路线编码
     */
    @Schema(description = "工艺路线编码")
    private String  code;

    /**
     * 工艺路线名称
     */
    @Schema(description = "工艺路线名称")
    private String name;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    private String pedigreeCode;

    /**
     * 流程框图类型(0:正常生产流程;1:返修方案流程)
     */
    @Schema(description = "流程框图类型(0:正常生产流程;1:返修方案流程)")
    private Integer category;

    /**
     * 工艺路线对应的有序工序
     */
    @Schema(description = "工艺路线对应的有序工序")
    private List<WorkFlowStepInfo> workFlowStepInfoList;

    /**
     * 同步类型(0:新增;1:修改;2:删除;)
     */
    @Schema(description = "同步类型(0:新增;1:修改;2:删除;)", required = true)
    private Integer operate;

    public Long getId() {
        return id;
    }

    public SyncWorkFlowDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCode() {
        return code;
    }

    public SyncWorkFlowDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public SyncWorkFlowDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public SyncWorkFlowDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public SyncWorkFlowDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public List<WorkFlowStepInfo> getWorkFlowStepInfoList() {
        return workFlowStepInfoList;
    }

    public SyncWorkFlowDTO setWorkFlowStepInfoList(List<WorkFlowStepInfo> workFlowStepInfoList) {
        this.workFlowStepInfoList = workFlowStepInfoList;
        return this;
    }

    public Integer getOperate() {
        return operate;
    }

    public SyncWorkFlowDTO setOperate(Integer operate) {
        this.operate = operate;
        return this;
    }

    @Schema(description = "工艺路线对应的有序工序")
    public static class WorkFlowStepInfo{

        /**
         * 工艺路线编码
         */
        @Schema(description = "工艺路线编码")
        private String workFlowCode;

        /**
         * 序列号
         */
        @Schema(description = "序列号")
        private String no;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码")
        private String stepCode;

        public WorkFlowStepInfo() {
            // 空构造
        }


        public String getWorkFlowCode() {
            return workFlowCode;
        }

        public WorkFlowStepInfo setWorkFlowCode(String workFlowCode) {
            this.workFlowCode = workFlowCode;
            return this;
        }

        public String getNo() {
            return no;
        }

        public WorkFlowStepInfo setNo(String no) {
            this.no = no;
            return this;
        }

        public String getStepCode() {
            return stepCode;
        }

        public WorkFlowStepInfo setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }
    }

}
