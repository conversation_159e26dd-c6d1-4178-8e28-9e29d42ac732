package net.airuima.rbase.dto.report;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "产线报表DTO")
public class WorkLineReportDTO implements Serializable {
    /**
     * 产线ID
     */
    @Schema(description = "产线ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 产线名称
     */
    @Schema(description = "产线名称")
    private String workLineName;

    /**
     * 车间名称
     */
    @Schema(description = "车间名称")
    private String organizationName;

    public Long getId() {
        return id;
    }

    public WorkLineReportDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public WorkLineReportDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public WorkLineReportDTO setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
        return this;
    }
}
