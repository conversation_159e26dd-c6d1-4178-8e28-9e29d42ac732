package net.airuima.rbase.dto.rworker.process.dto.general;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.SnWorkStatusEnum;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.domain.procedure.single.WorkSheetSn;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 投产SN信息
 * <AUTHOR>
 * @date 2023/4/6
 */
@Schema(description = "投产SN信息")
public class SnGetInfo implements Serializable {

    /**
     * 待投产内部SN
     */
    @Schema(description = "待投产内部SN")
    private String sn;

    /**
     * 内部关联的YSN
     */
    @Schema(description = "内部关联的YSN")
    private String ysn;

    /**
     * SN生产状态:0-待投产;1-投产中;2-待返修;3-返修中;4-合格;5-报废;6-待维修分析
     */
    @Schema(description = "SN生产状态:0-待投产;1-投产中;2-待返修;3-返修中;4-合格;5-报废;6-待维修分析")
    private Integer status;



    public SnGetInfo() {

    }

    public SnGetInfo(SnWorkStatus snWorkStatus){
        this.sn = snWorkStatus.getSn();
        this.ysn = snWorkStatus.getYsn();
        this.status = snWorkStatus.getStatus();
    }

    public SnGetInfo(WorkSheetSn workSheetSn){
        this.sn = workSheetSn.getSn();
        this.status = SnWorkStatusEnum.TO_BE_PUT_INTO_PRODUCTION.getStatus();
    }

    public SnGetInfo(String sn){
        this.sn = sn;
        this.status = SnWorkStatusEnum.TO_BE_PUT_INTO_PRODUCTION.getStatus();
    }

    public String getSn() {
        return sn;
    }

    public SnGetInfo setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getYsn() {
        return ysn;
    }

    public SnGetInfo setYsn(String ysn) {
        this.ysn = ysn;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public SnGetInfo setStatus(Integer status) {
        this.status = status;
        return this;
    }
}
