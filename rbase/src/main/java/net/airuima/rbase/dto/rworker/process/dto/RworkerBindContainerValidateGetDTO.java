package net.airuima.rbase.dto.rworker.process.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.procedure.batch.Container;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@Schema(description = "待绑定容器信息")
public class RworkerBindContainerValidateGetDTO implements Serializable {

    /**
     * 绑定的容器ID
     */
    @Schema(description = "绑定的容器ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bindContainerId;

    /**
     * 绑定的容器编码
     */
    @Schema(description = "绑定的容器编码")
    private String code;

    /**
     * 绑定的容器名称
     */
    @Schema(description = "绑定的容器名称")
    private String name;

    public RworkerBindContainerValidateGetDTO() {
    }

    public RworkerBindContainerValidateGetDTO(Container container) {
        this.bindContainerId = container.getId();
        this.code = container.getCode();
        this.name = container.getName();
    }

    public Long getBindContainerId() {
        return bindContainerId;
    }

    public RworkerBindContainerValidateGetDTO setBindContainerId(Long bindContainerId) {
        this.bindContainerId = bindContainerId;
        return this;
    }

    public String getCode() {
        return code;
    }

    public RworkerBindContainerValidateGetDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public RworkerBindContainerValidateGetDTO setName(String name) {
        this.name = name;
        return this;
    }
}
