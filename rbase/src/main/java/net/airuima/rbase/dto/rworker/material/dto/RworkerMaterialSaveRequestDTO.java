package net.airuima.rbase.dto.rworker.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/29
 */
@Schema(description = "Rworker保存上料数据参数DTO")
public class RworkerMaterialSaveRequestDTO implements Serializable {

    /**
     * 工单ID
     */
    @Schema(description = "工单ID")
    private Long workSheetId;

    /**
     * 工位ID
     */
    @Schema(description = "工位ID")
    private Long workCellId;

    /**
     * 工序ID
     */
    @Schema(description = "工序ID")
    private Long stepId;

    /**
     * 物料ID
     */
    @Schema(description = "物料ID")
    private Long materialId;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    private String batch;

    /**
     * 上料数量
     */
    @Schema(description = "上料数量")
    private Double number;

    /**
     * 是否核物料批次(true:是;false:否)
     */
    @Schema(description = "是否核物料批次(true:核对物料批次;false:不核对物料批次)")
    private Boolean isCheckMaterialBatch;

    /**
     * 是否扣除库存(true:是;false:否)
     */
    @Schema(description = "是否扣除库存(true:是;false:否)")
    private Boolean isDeduct;

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public RworkerMaterialSaveRequestDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerMaterialSaveRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerMaterialSaveRequestDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public RworkerMaterialSaveRequestDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public RworkerMaterialSaveRequestDTO setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public RworkerMaterialSaveRequestDTO setNumber(Double number) {
        this.number = number;
        return this;
    }

    public Boolean getIsCheckMaterialBatch() {
        return isCheckMaterialBatch;
    }

    public RworkerMaterialSaveRequestDTO setIsCheckMaterialBatch(Boolean checkMaterialBatch) {
        isCheckMaterialBatch = checkMaterialBatch;
        return this;
    }

    public Boolean getIsDeduct() {
        return isDeduct;
    }

    public RworkerMaterialSaveRequestDTO setIsDeduct(Boolean deduct) {
        isDeduct = deduct;
        return this;
    }
}
