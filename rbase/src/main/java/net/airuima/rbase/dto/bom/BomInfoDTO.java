package net.airuima.rbase.dto.bom;

import net.airuima.config.annotation.Forbidden;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.dto.document.DocumentDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020/12/15
 */
public class BomInfoDTO extends AbstractDto implements Serializable {

    /**
     * 物料方案名称
     */
    private String name;

    /**
     * 物料方案编码
     */
    private String code;

    /**
     * 工艺图片
     */
    private String picture;

    /**
     * BOM适用阶段
     */
    private Integer stage;

    /**
     * 发行日期
     */
    private LocalDateTime effectiveDate;

    /**
     * 失效日期
     */
    private LocalDateTime invalidDate;

    /**
     * 是否启用（1：启用 0：禁用）
     */
    @Forbidden
    private Boolean isEnable;

    /**
     * 主物料（型号对应物料）
     * 新增产品型号时,同时会生成一份物料信息,对应关系如下
     * material.setName(产品描述);
     * material.setCode(产品编码);
     * material.setDescription(产品型号);
     */
    private MaterialDTO material;

    /**
     * 工艺图片DTO集合
     */
    private List<DocumentDTO> documentDTOList;

    public String getName() {
        return name;
    }

    public BomInfoDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public BomInfoDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getPicture() {
        return picture;
    }

    public BomInfoDTO setPicture(String picture) {
        this.picture = picture;
        return this;
    }

    public Integer getStage() {
        return stage;
    }

    public BomInfoDTO setStage(Integer stage) {
        this.stage = stage;
        return this;
    }

    public LocalDateTime getEffectiveDate() {
        return effectiveDate;
    }

    public BomInfoDTO setEffectiveDate(LocalDateTime effectiveDate) {
        this.effectiveDate = effectiveDate;
        return this;
    }

    public LocalDateTime getInvalidDate() {
        return invalidDate;
    }

    public BomInfoDTO setInvalidDate(LocalDateTime invalidDate) {
        this.invalidDate = invalidDate;
        return this;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public BomInfoDTO setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public MaterialDTO getMaterial() {
        return material;
    }

    public BomInfoDTO setMaterial(MaterialDTO material) {
        this.material = material;
        return this;
    }

    public List<DocumentDTO> getDocumentDTOList() {
        return documentDTOList;
    }

    public BomInfoDTO setDocumentDTOList(List<DocumentDTO> documentDTOList) {
        this.documentDTOList = documentDTOList;
        return this;
    }
}
