package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/1
 */
@Schema(description = "获取子工单对应的工序的预警事件")
public class ClientUnqualifiedEventGetDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "子工单id")
    private Long subWorkSheetId;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "工序id")
    private Long stepId;

    public Long getSubWorkSheetId() {
        return subWorkSheetId;
    }

    public ClientUnqualifiedEventGetDTO setSubWorkSheetId(Long subWorkSheetId) {
        this.subWorkSheetId = subWorkSheetId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public ClientUnqualifiedEventGetDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }
}
