package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork请求工序相关接口DTO
 *
 * <AUTHOR>
 * @date 2020/12/29
 */
@Schema(description = "Rwork请求单支返回工序信息")
public class ClientGetSnStepInfoDTO<T> extends ClientGetStepInfoDTO {
    /**
     * 投产SN
     */
    @Schema(description = "SN")
    private String sn;
    /**
     * 子工单基础信息
     */
    @Schema(description = "子工单基础信息")
    private ClientStepSubWsInfoDTO clientStepSubWsInfoDto;

    /**
     * 当前SN返修次数
     */
    @Schema(description = "SN返修次数")
    private Integer reworkTime;

    /**
     * 流程框图ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workFlowId;

    /**
     * 防重复提交Token(采用32位uuid)
     */
    @Schema(description = "防重复提交Token")
    private String xsrfToken;

    /**
     * 扩展字段
     */
    @Schema(description = "扩展字段")
    private T extend;

    public ClientGetSnStepInfoDTO() {
    }

    public ClientGetSnStepInfoDTO(BaseClientDTO baseClientDTO) {
        this.setStatus(baseClientDTO.getStatus());
        this.setMessage(baseClientDTO.getMessage());
    }

    public Long getWorkFlowId() {
        return workFlowId;
    }

    public ClientGetSnStepInfoDTO<T> setWorkFlowId(Long workFlowId) {
        this.workFlowId = workFlowId;
        return this;
    }

    public Integer getReworkTime() {
        return reworkTime;
    }

    public ClientGetSnStepInfoDTO<T> setReworkTime(Integer reworkTime) {
        this.reworkTime = reworkTime;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public ClientGetSnStepInfoDTO<T> setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public ClientStepSubWsInfoDTO getClientStepSubWsInfoDto() {
        return clientStepSubWsInfoDto;
    }

    public ClientGetSnStepInfoDTO<T> setClientStepSubWsInfoDto(ClientStepSubWsInfoDTO clientStepSubWsInfoDto) {
        this.clientStepSubWsInfoDto = clientStepSubWsInfoDto;
        return this;
    }

    @Override
    public String getXsrfToken() {
        return xsrfToken;
    }

    @Override
    public ClientGetSnStepInfoDTO<T> setXsrfToken(String xsrfToken) {
        this.xsrfToken = xsrfToken;
        return this;
    }

    public T getExtend() {
        return extend;
    }

    public ClientGetSnStepInfoDTO<T> setExtend(T extend) {
        this.extend = extend;
        return this;
    }
}
