package net.airuima.rbase.dto.pedigree;

import net.airuima.dto.AbstractDto;
import net.airuima.rbase.dto.bom.MaterialDTO;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系DTO
 *
 * <AUTHOR>
 * @date 2020/12/22
 */
public class PedigreeDTO extends AbstractDto {

    /**
     * 产品谱系编码
     */
    private String code;

    /**
     * 产品谱系名称
     */
    private String name;

    /**
     * 当前层级类型
     */
    private int type;

    /**
     * 父级产品谱系
     */
    private PedigreeDTO parent;

    /**
     * 物料
     */
    private MaterialDTO material;

    /**
     * 是否启用(0:不启用;1:启用)
     */
    private boolean isEnable;

    /**
     * 分单数量
     */
    private int splitNumber;

    /**
     * 是否需要重新定制流程框图(0:否;1:是)
     */
    private boolean isCustomWorkFlow;

    /**
     * 计划完成天数
     */
    private int planFinishDay;

    /**
     * 目标成品率
     */
    private Double qualifiedRate;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 是否为后一层级
     **/
    private Boolean isLastLevel;


    public String getCode() {
        return code;
    }

    public PedigreeDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public PedigreeDTO setName(String name) {
        this.name = name;
        return this;
    }

    public int getType() {
        return type;
    }

    public PedigreeDTO setType(int type) {
        this.type = type;
        return this;
    }

    public PedigreeDTO getParent() {
        return parent;
    }

    public PedigreeDTO setParent(PedigreeDTO parent) {
        this.parent = parent;
        return this;
    }

    public MaterialDTO getMaterial() {
        return material;
    }

    public PedigreeDTO setMaterial(MaterialDTO material) {
        this.material = material;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeDTO setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public int getSplitNumber() {
        return splitNumber;
    }

    public PedigreeDTO setSplitNumber(int splitNumber) {
        this.splitNumber = splitNumber;
        return this;
    }

    public boolean getIsCustomWorkFlow() {
        return isCustomWorkFlow;
    }

    public PedigreeDTO setIsCustomWorkFlow(boolean isCustomWorkFlow) {
        this.isCustomWorkFlow = isCustomWorkFlow;
        return this;
    }

    public int getPlanFinishDay() {
        return planFinishDay;
    }

    public PedigreeDTO setPlanFinishDay(int planFinishDay) {
        this.planFinishDay = planFinishDay;
        return this;
    }

    public Double getQualifiedRate() {
        return qualifiedRate;
    }

    public PedigreeDTO setQualifiedRate(Double qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public PedigreeDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public Boolean getIsLastLevel() {
        return isLastLevel;
    }

    public PedigreeDTO setIsLastLevel(Boolean isLastLevel) {
        this.isLastLevel = isLastLevel;
        return this;
    }
}
