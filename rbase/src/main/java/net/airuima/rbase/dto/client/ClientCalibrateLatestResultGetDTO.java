package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/2
 */
public class ClientCalibrateLatestResultGetDTO extends BaseClientDTO {

    /**
     * 台位
     */
    @Schema(description = "台位", required = true)
    private String workCellCode;

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime testTime;

    /**
     * 测试人员
     */
    @Schema(description = "测试人员")
    private String tester;

    public ClientCalibrateLatestResultGetDTO(BaseClientDTO baseClientDTO) {
        this.setStatus(baseClientDTO.getStatus());
        this.setMessage(baseClientDTO.getMessage());
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public ClientCalibrateLatestResultGetDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public ClientCalibrateLatestResultGetDTO setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public ClientCalibrateLatestResultGetDTO setTester(String tester) {
        this.tester = tester;
        return this;
    }
}
