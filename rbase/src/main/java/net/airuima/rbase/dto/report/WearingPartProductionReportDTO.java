package net.airuima.rbase.dto.report;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart;

import java.time.LocalDate;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/24
 */
@Schema(description = "RWorker获取易损件生产追溯相关信息DTO")
public class WearingPartProductionReportDTO extends BaseClientDTO {

    /**
     * 请求参数
     */
    @Schema(description = "请求参数DTO")
    public static class RequestInfo{
        /**
         * 易损件编码
         */
        @Schema(description = "易损件编码")
        private String wearingPartCode;
        /**
         * 生产详情易损件类型
         */
        @Schema(description = "生产详情易损件类型")
        private Integer category;
        /**
         * 日期开始范围
         */
        @Schema(description = "日期开始范围")
        private LocalDate startDate;

        /**
         * 日期结束范围
         */
        @Schema(description = "日期结束范围")
        private LocalDate endDate;

        /**
         * 当前页数
         */
        @Schema(description = "当前页数")
        private Integer currentPage;

        /**
         * 页面显示数
         */
        @Schema(description = "页面显示数")
        private Integer pageSize;

        public String getWearingPartCode() {
            return wearingPartCode;
        }

        public RequestInfo setWearingPartCode(String wearingPartCode) {
            this.wearingPartCode = wearingPartCode;
            return this;
        }

        public Integer getCategory() {
            return category;
        }

        public RequestInfo setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public LocalDate getStartDate() {
            return startDate;
        }

        public RequestInfo setStartDate(LocalDate startDate) {
            this.startDate = startDate;
            return this;
        }

        public LocalDate getEndDate() {
            return endDate;
        }

        public RequestInfo setEndDate(LocalDate endDate) {
            this.endDate = endDate;
            return this;
        }

        public Integer getCurrentPage() {
            return currentPage;
        }

        public RequestInfo setCurrentPage(Integer currentPage) {
            this.currentPage = currentPage;
            return this;
        }

        public Integer getPageSize() {
            return pageSize;
        }

        public RequestInfo setPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }
    }

    /**
     * 返回数据
     */
    @Schema(description = "返回数据DTO")
    public static class ResponseInfo{

        /**
         * 返回数据条数
         */
        @Schema(description = "返回数据条数")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long countSize;

        /**
         * 批量生产详情
         */
        @Schema(description = "批量生产详情")
        private List<WearingPartProductionReportDTO.BatchWearingPart> batchWorkWearingPartList;

        /**
         * 容器生产详情
         */
        @Schema(description = "容器生产详情")
        private List<WearingPartProductionReportDTO.ContainerWearingPart> containerWearingPartList;

        /**
         * 单支生产详情
         */
        @Schema(description = "单支生产详情")
        private List<WearingPartProductionReportDTO.SnWearingPart> snWearingPartList;

        public List<BatchWearingPart> getBatchWorkWearingPartList() {
            return batchWorkWearingPartList;
        }

        public ResponseInfo setBatchWorkWearingPartList(List<BatchWearingPart> batchWorkWearingPartList) {
            this.batchWorkWearingPartList = batchWorkWearingPartList;
            return this;
        }

        public List<ContainerWearingPart> getContainerWearingPartList() {
            return containerWearingPartList;
        }

        public ResponseInfo setContainerWearingPartList(List<ContainerWearingPart> containerWearingPartList) {
            this.containerWearingPartList = containerWearingPartList;
            return this;
        }

        public List<SnWearingPart> getSnWearingPartList() {
            return snWearingPartList;
        }

        public ResponseInfo setSnWearingPartList(List<SnWearingPart> snWearingPartList) {
            this.snWearingPartList = snWearingPartList;
            return this;
        }

        public Long getCountSize() {
            return countSize;
        }

        public ResponseInfo setCountSize(Long countSize) {
            this.countSize = countSize;
            return this;
        }
    }

    /**
     * SN详情易损件信息
     */
    @Schema(description = "SN详情易损件信息")
    public static class SnWearingPart{
        /**
         * SN
         */
        @Schema(description = "SN")
        private String sn;

        /**
         * 工序名称
         */
        @Schema(description = "工序名称")
        private String stepName;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码")
        private String stepCode;

        /**
         * 操作人名字
         */
        @Schema(description = "操作人名字")
        private String operatorName;

        /**
         * 操作人员工号
         */
        @Schema(description = "操作人员工号")
        private String operatorCode;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String workCellName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;

        /**
         * 子工单号
         */
        @Schema(description = "子工单号")
        private String subWorkSheetSerialNumber;

        /**
         * 易损件名称
         */
        @Schema(description = "易损件名称")
        private String wearingPartName;

        /**
         * 易损件编码
         */
        @Schema(description = "易损件编码")
        private String wearingPartCode;

        public SnWearingPart() {
        }

        public SnWearingPart(SnWorkDetailWearingPart snWorkDetailWearingPart) {
            this.sn = snWorkDetailWearingPart.getSnWorkDetail().getSn();
            this.stepName = snWorkDetailWearingPart.getSnWorkDetail().getStep().getName();
            this.stepCode = snWorkDetailWearingPart.getSnWorkDetail().getStep().getCode();
            this.operatorName = snWorkDetailWearingPart.getSnWorkDetail().getOperatorDto().getName();
            this.operatorCode = snWorkDetailWearingPart.getSnWorkDetail().getOperatorDto().getCode();
            this.workCellName = snWorkDetailWearingPart.getSnWorkDetail().getWorkCell().getName();
            this.workCellCode = snWorkDetailWearingPart.getSnWorkDetail().getWorkCell().getCode();
            this.subWorkSheetSerialNumber = snWorkDetailWearingPart.getSnWorkDetail().getSubWorkSheet().getSerialNumber();
            this.wearingPartName = snWorkDetailWearingPart.getWearingPart().getName();
            this.wearingPartCode = snWorkDetailWearingPart.getWearingPart().getCode();
        }

        public String getSn() {
            return sn;
        }

        public SnWearingPart setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public String getStepName() {
            return stepName;
        }

        public SnWearingPart setStepName(String stepName) {
            this.stepName = stepName;
            return this;
        }

        public String getStepCode() {
            return stepCode;
        }

        public SnWearingPart setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }

        public String getOperatorName() {
            return operatorName;
        }

        public SnWearingPart setOperatorName(String operatorName) {
            this.operatorName = operatorName;
            return this;
        }

        public String getOperatorCode() {
            return operatorCode;
        }

        public SnWearingPart setOperatorCode(String operatorCode) {
            this.operatorCode = operatorCode;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public SnWearingPart setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public SnWearingPart setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getSubWorkSheetSerialNumber() {
            return subWorkSheetSerialNumber;
        }

        public SnWearingPart setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
            this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
            return this;
        }

        public String getWearingPartName() {
            return wearingPartName;
        }

        public SnWearingPart setWearingPartName(String wearingPartName) {
            this.wearingPartName = wearingPartName;
            return this;
        }

        public String getWearingPartCode() {
            return wearingPartCode;
        }

        public SnWearingPart setWearingPartCode(String wearingPartCode) {
            this.wearingPartCode = wearingPartCode;
            return this;
        }
    }

    /**
     * 容器详情易损件信息
     */
    @Schema(description = "容器详情易损件信息")
    public static class ContainerWearingPart{
        /**
         * 容器编码
         */
        @Schema(description = "容器编码")
        private String containerCode;

        /**
         * 工序名称
         */
        @Schema(description = "工序名称")
        private String stepName;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码")
        private String stepCode;

        /**
         * 操作人名字
         */
        @Schema(description = "操作人名字")
        private String operatorName;

        /**
         * 操作人员工号
         */
        @Schema(description = "操作人员工号")
        private String operatorCode;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String workCellName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;

        /**
         * 子工单号
         */
        @Schema(description = "子工单号")
        private String subWorkSheetSerialNumber;

        /**
         * 易损件名称
         */
        @Schema(description = "易损件名称")
        private String wearingPartName;

        /**
         * 易损件编码
         */
        @Schema(description = "易损件编码")
        private String wearingPartCode;

        public ContainerWearingPart() {
        }

        public ContainerWearingPart(ContainerDetailWearingPart containerDetailWearingPart) {
            this.containerCode = containerDetailWearingPart.getContainerDetail().getContainerCode();
            this.stepName = containerDetailWearingPart.getContainerDetail().getBatchWorkDetail().getStep().getName();
            this.stepCode = containerDetailWearingPart.getContainerDetail().getBatchWorkDetail().getStep().getCode();
            this.operatorName = containerDetailWearingPart.getContainerDetail().getBatchWorkDetail().getOperatorDto().getName();
            this.operatorCode = containerDetailWearingPart.getContainerDetail().getBatchWorkDetail().getOperatorDto().getCode();
            this.workCellName = containerDetailWearingPart.getContainerDetail().getWorkCell().getName();
            this.workCellCode = containerDetailWearingPart.getContainerDetail().getWorkCell().getCode();
            this.subWorkSheetSerialNumber = containerDetailWearingPart.getContainerDetail().getBatchWorkDetail().getSubWorkSheet().getSerialNumber();
            this.wearingPartName = containerDetailWearingPart.getWearingPart().getName();
            this.wearingPartCode = containerDetailWearingPart.getWearingPart().getCode();
        }

        public String getContainerCode() {
            return containerCode;
        }

        public ContainerWearingPart setContainerCode(String containerCode) {
            this.containerCode = containerCode;
            return this;
        }

        public String getStepName() {
            return stepName;
        }

        public ContainerWearingPart setStepName(String stepName) {
            this.stepName = stepName;
            return this;
        }

        public String getStepCode() {
            return stepCode;
        }

        public ContainerWearingPart setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }

        public String getOperatorName() {
            return operatorName;
        }

        public ContainerWearingPart setOperatorName(String operatorName) {
            this.operatorName = operatorName;
            return this;
        }

        public String getOperatorCode() {
            return operatorCode;
        }

        public ContainerWearingPart setOperatorCode(String operatorCode) {
            this.operatorCode = operatorCode;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public ContainerWearingPart setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public ContainerWearingPart setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getSubWorkSheetSerialNumber() {
            return subWorkSheetSerialNumber;
        }

        public ContainerWearingPart setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
            this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
            return this;
        }

        public String getWearingPartName() {
            return wearingPartName;
        }

        public ContainerWearingPart setWearingPartName(String wearingPartName) {
            this.wearingPartName = wearingPartName;
            return this;
        }

        public String getWearingPartCode() {
            return wearingPartCode;
        }

        public ContainerWearingPart setWearingPartCode(String wearingPartCode) {
            this.wearingPartCode = wearingPartCode;
            return this;
        }
    }

    /**
     * 批量详情易损件信息
     */
    @Schema(description = "批量详情易损件信息")
    public static class BatchWearingPart{
        /**
         * 工序名称
         */
        @Schema(description = "工序名称")
        private String stepName;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码")
        private String stepCode;

        /**
         * 操作人名字
         */
        @Schema(description = "操作人名字")
        private String operatorName;

        /**
         * 操作人员工号
         */
        @Schema(description = "操作人员工号")
        private String operatorCode;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String workCellName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;

        /**
         * 子工单号
         */
        @Schema(description = "子工单号")
        private String subWorkSheetSerialNumber;


        /**
         * 易损件名称
         */
        @Schema(description = "易损件名称")
        private String wearingPartName;

        /**
         * 易损件编码
         */
        @Schema(description = "易损件编码")
        private String wearingPartCode;

        public BatchWearingPart() {
        }

        public BatchWearingPart(BatchWorkDetailWearingPart batchWorkDetailWearingPart) {
            this.stepName = batchWorkDetailWearingPart.getBatchWorkDetail().getStep().getName();
            this.stepCode = batchWorkDetailWearingPart.getBatchWorkDetail().getStep().getCode();
            this.operatorName = batchWorkDetailWearingPart.getBatchWorkDetail().getOperatorDto().getName();
            this.operatorCode = batchWorkDetailWearingPart.getBatchWorkDetail().getOperatorDto().getCode();
            this.workCellName = batchWorkDetailWearingPart.getBatchWorkDetail().getWorkCell().getName();
            this.workCellCode = batchWorkDetailWearingPart.getBatchWorkDetail().getWorkCell().getCode();
            this.subWorkSheetSerialNumber = batchWorkDetailWearingPart.getBatchWorkDetail().getSubWorkSheet().getSerialNumber();
            this.wearingPartName = batchWorkDetailWearingPart.getWearingPart().getName();
            this.wearingPartCode = batchWorkDetailWearingPart.getWearingPart().getCode();
        }

        public String getStepName() {
            return stepName;
        }

        public BatchWearingPart setStepName(String stepName) {
            this.stepName = stepName;
            return this;
        }

        public String getStepCode() {
            return stepCode;
        }

        public BatchWearingPart setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }

        public String getOperatorName() {
            return operatorName;
        }

        public BatchWearingPart setOperatorName(String operatorName) {
            this.operatorName = operatorName;
            return this;
        }

        public String getOperatorCode() {
            return operatorCode;
        }

        public BatchWearingPart setOperatorCode(String operatorCode) {
            this.operatorCode = operatorCode;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public BatchWearingPart setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public BatchWearingPart setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getSubWorkSheetSerialNumber() {
            return subWorkSheetSerialNumber;
        }

        public BatchWearingPart setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
            this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
            return this;
        }

        public String getWearingPartName() {
            return wearingPartName;
        }

        public BatchWearingPart setWearingPartName(String wearingPartName) {
            this.wearingPartName = wearingPartName;
            return this;
        }

        public String getWearingPartCode() {
            return wearingPartCode;
        }

        public BatchWearingPart setWearingPartCode(String wearingPartCode) {
            this.wearingPartCode = wearingPartCode;
            return this;
        }
    }
}
