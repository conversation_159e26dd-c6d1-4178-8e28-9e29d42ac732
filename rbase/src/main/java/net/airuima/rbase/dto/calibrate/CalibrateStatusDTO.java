package net.airuima.rbase.dto.calibrate;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;
import net.airuima.dto.AbstractDto;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.rfms.FacilityDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class CalibrateStatusDTO extends AbstractDto {

    /**
     * 工位ID
     */
    @Schema(description = "工位ID")
    private WorkCell workCell;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long facilityId;

    @Schema(description = "设备DTO")
    private FacilityDTO facilityDto = new FacilityDTO();

    /**
     * 校准程序，0：内校，1：外校
     **/
    @Schema(description = "校准程序，0：内校，1：外校", required = true)
    private int process;

    /**
     * 上次校准日期
     */
    @Schema(description = "上次校准日期", required = true)
    private LocalDateTime latestOperatorDate;

    /**
     * 下次校准日期
     */
    @Schema(description = "下次校准日期", required = true)
    private LocalDateTime nextOperatorDate;

    /**
     * 下次提醒日期
     */
    @Schema(description = "下次提醒日期", required = true)
    private LocalDateTime nextRemindDate;

    /**
     * 结果，0不合格, 1合格
     */
    @Schema(description = "结果，0不合格, 1合格", required = true)
    private boolean result;

    /**
     * 校准人ID
     */
    @Schema(description = "测试人员")
    private String tester;

    /**
     * 是否逾期，0已逾期, 1未逾期
     */
    @Schema(description = "是否逾期，0已逾期, 1未逾期", required = true)
    private boolean overdue;

    public WorkCell getWorkCell() {
        return workCell;
    }

    public CalibrateStatusDTO setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public CalibrateStatusDTO setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public CalibrateStatusDTO setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    public int getProcess() {
        return process;
    }

    public CalibrateStatusDTO setProcess(int process) {
        this.process = process;
        return this;
    }

    public LocalDateTime getLatestOperatorDate() {
        return latestOperatorDate;
    }

    public CalibrateStatusDTO setLatestOperatorDate(LocalDateTime latestOperatorDate) {
        this.latestOperatorDate = latestOperatorDate;
        return this;
    }

    public LocalDateTime getNextOperatorDate() {
        return nextOperatorDate;
    }

    public CalibrateStatusDTO setNextOperatorDate(LocalDateTime nextOperatorDate) {
        this.nextOperatorDate = nextOperatorDate;
        return this;
    }

    public LocalDateTime getNextRemindDate() {
        return nextRemindDate;
    }

    public CalibrateStatusDTO setNextRemindDate(LocalDateTime nextRemindDate) {
        this.nextRemindDate = nextRemindDate;
        return this;
    }

    public boolean getResult() {
        return result;
    }

    public CalibrateStatusDTO setResult(boolean result) {
        this.result = result;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public CalibrateStatusDTO setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public boolean getOverdue() {
        return overdue;
    }

    public CalibrateStatusDTO setOverdue(boolean overdue) {
        this.overdue = overdue;
        return this;
    }
}
