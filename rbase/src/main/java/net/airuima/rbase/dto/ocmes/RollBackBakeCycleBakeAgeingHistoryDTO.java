package net.airuima.rbase.dto.ocmes;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/19
 */
@Schema(description = "烘烤温循老化回退参数")
public class RollBackBakeCycleBakeAgeingHistoryDTO {

    /**
     *  工单
     */
    @Schema(description = "工单")
    private WorkSheet workSheet;

    /**
     *  子工单
     */
    @Schema(description = "子工单")
    private SubWorkSheet subWorkSheet;


    /**
     *  工序
     */
    @Schema(description = "工序")
    private Step step;

    /**
     *  容器
     */
    @Schema(description = "容器")
    private List<String> containerCodeList;

    /**
     *  sn
     */
    @Schema(description = "sn")
    private String sn;

    /**
     *  sn返修次数
     */
    @Schema(description = "sn返修次数")
    private Integer reworkTime = Constants.INT_ZERO;

    public RollBackBakeCycleBakeAgeingHistoryDTO(WorkSheet workSheet,SubWorkSheet subWorkSheet, Step step, List<String> containerCodeList, String sn,Integer reworkTime) {
        this.workSheet =workSheet;
        this.subWorkSheet = subWorkSheet;
        this.step = step;
        this.containerCodeList = containerCodeList;
        this.sn = sn;
        this.reworkTime = reworkTime;
    }

    public Integer getReworkTime() {
        return reworkTime;
    }

    public RollBackBakeCycleBakeAgeingHistoryDTO setReworkTime(Integer reworkTime) {
        this.reworkTime = reworkTime;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public RollBackBakeCycleBakeAgeingHistoryDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public RollBackBakeCycleBakeAgeingHistoryDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public RollBackBakeCycleBakeAgeingHistoryDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public List<String> getContainerCodeList() {
        return containerCodeList;
    }

    public RollBackBakeCycleBakeAgeingHistoryDTO setContainerCodeList(List<String> containerCodeList) {
        this.containerCodeList = containerCodeList;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public RollBackBakeCycleBakeAgeingHistoryDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }
}
