package net.airuima.rbase.dto.single;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/2/23
 */
@Schema(description = "sn标签替换DTO")
public class SnReplaceDTO {

    /**
     * 原始sn
     */
    @Schema(description = "原始sn")
    private String originalSn;

    /**
     * 替换sn
     */
    @Schema(description = "替换sn")
    private String replaceSn;

    public String getOriginalSn() {
        return originalSn;
    }

    public SnReplaceDTO setOriginalSn(String originalSn) {
        this.originalSn = originalSn;
        return this;
    }

    public String getReplaceSn() {
        return replaceSn;
    }

    public SnReplaceDTO setReplaceSn(String replaceSn) {
        this.replaceSn = replaceSn;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SnReplaceDTO that = (SnReplaceDTO) o;
        return originalSn.equals(that.originalSn) && replaceSn.equals(that.replaceSn);
    }

    @Override
    public int hashCode() {
        return Objects.hash(originalSn, replaceSn);
    }
}
