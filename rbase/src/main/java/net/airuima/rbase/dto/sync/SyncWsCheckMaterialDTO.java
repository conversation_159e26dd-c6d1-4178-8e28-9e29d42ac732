package net.airuima.rbase.dto.sync;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/7/14
 */
public class SyncWsCheckMaterialDTO {

    @Schema(description = "核料ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工单号
     */
    @NotNull
    @Schema(description = "工单号", required = true)
    private String serialNumber;

    /**
     * 核料凭证
     */
    @Schema(description = "核料凭证")
    private String checkMaterialCode;

    /**
     * 核料信息
     */
    @Schema(description = "核料信息")
    private List<CheckMaterialInfo> checkMaterialInfoList;

    /**
     * 同步类型(0:新增;1:修改;2:删除;3:退料结单)
     */
    @Schema(description = "同步类型(0:新增;1:修改;2:删除;3:退料结单)")
    private Integer operate;

    public SyncWsCheckMaterialDTO() {
        // 空构造
    }


    @Schema(description = "核料信息")
    public static class CheckMaterialInfo {
        /**
         * 物料编码
         */
        @Schema(description = "物料编码")
        private String materialCode;

        /**
         * 物料批次
         */
        @Schema(description = "物料批次")
        private String materialBatch;

        /**
         * 数量
         */
        @Schema(description = "数量")
        private Double materialNumber;

        /**
         * 供应商编码
         */
        @Schema(description = "供应商编码")
        private String supplierCode;

        public CheckMaterialInfo() {
            // 空构造
        }


        public String getMaterialCode() {
            return materialCode;
        }

        public CheckMaterialInfo setMaterialCode(String materialCode) {
            this.materialCode = materialCode;
            return this;
        }

        public String getMaterialBatch() {
            return materialBatch;
        }

        public CheckMaterialInfo setMaterialBatch(String materialBatch) {
            this.materialBatch = materialBatch;
            return this;
        }

        public Double getMaterialNumber() {
            return materialNumber;
        }

        public CheckMaterialInfo setMaterialNumber(Double materialNumber) {
            this.materialNumber = materialNumber;
            return this;
        }

        public String getSupplierCode() {
            return supplierCode;
        }

        public CheckMaterialInfo setSupplierCode(String supplierCode) {
            this.supplierCode = supplierCode;
            return this;
        }
    }

    public Long getId() {
        return id;
    }

    public SyncWsCheckMaterialDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public SyncWsCheckMaterialDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getCheckMaterialCode() {
        return checkMaterialCode;
    }

    public SyncWsCheckMaterialDTO setCheckMaterialCode(String checkMaterialCode) {
        this.checkMaterialCode = checkMaterialCode;
        return this;
    }

    public List<CheckMaterialInfo> getCheckMaterialInfoList() {
        return checkMaterialInfoList;
    }

    public SyncWsCheckMaterialDTO setCheckMaterialInfoList(List<CheckMaterialInfo> checkMaterialInfoList) {
        this.checkMaterialInfoList = checkMaterialInfoList;
        return this;
    }

    public Integer getOperate() {
        return operate;
    }

    public SyncWsCheckMaterialDTO setOperate(Integer operate) {
        this.operate = operate;
        return this;
    }
}


