package net.airuima.rbase.dto.flowable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/17
 */
@Schema(description = "当前业务个人待办任务信息DTO")
public class FlowableSingleToDoTaskDTO {

    /**
     * 是否存在待做任务
     */
    @Schema(description = "是否存在待做任务")
    private Boolean existTodoTask;

    /**
     * 流程实例Id
     */
    @Schema(description = "流程实例Id")
    private String processInstanceId;


    /**
     * 任务Id
     */
    @Schema(description = "任务Id")
    private String taskId;


    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     * 任务节点KEY
     */
    @Schema(description = "任务节点KEY")
    private String taskDefinitionKey;


    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    private String taskCategory;

    /**
     * 表单key
     */
    @Schema(description = "表单key")
    private String formKey;

    /**
     * 截止日期
     */
    @Schema(description = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;

    /**
     * 流程定义中的用户任务顺序表
     */
    @Schema(description = "流程定义中的用户任务顺序")
    private List<ProcessDefinitionUserTaskInfo> processDefinitionUserTaskInfoList;

    /**
     * 历史已完成任务列表
     */
    @Schema(description = "历史已完成任务列表")
    private List<HistoryTaskInfo> historyTaskInfoList;

    public Boolean getExistTodoTask() {
        return existTodoTask;
    }

    public FlowableSingleToDoTaskDTO setExistTodoTask(Boolean existTodoTask) {
        this.existTodoTask = existTodoTask;
        return this;
    }

    public String getTaskId() {
        return taskId;
    }

    public FlowableSingleToDoTaskDTO setTaskId(String taskId) {
        this.taskId = taskId;
        return this;
    }

    public String getTaskName() {
        return taskName;
    }

    public FlowableSingleToDoTaskDTO setTaskName(String taskName) {
        this.taskName = taskName;
        return this;
    }

    public String getTaskDefinitionKey() {
        return taskDefinitionKey;
    }

    public FlowableSingleToDoTaskDTO setTaskDefinitionKey(String taskDefinitionKey) {
        this.taskDefinitionKey = taskDefinitionKey;
        return this;
    }


    public String getTaskCategory() {
        return taskCategory;
    }

    public FlowableSingleToDoTaskDTO setTaskCategory(String taskCategory) {
        this.taskCategory = taskCategory;
        return this;
    }

    public String getFormKey() {
        return formKey;
    }

    public FlowableSingleToDoTaskDTO setFormKey(String formKey) {
        this.formKey = formKey;
        return this;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public FlowableSingleToDoTaskDTO setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
        return this;
    }

    public List<HistoryTaskInfo> getHistoryTaskInfoList() {
        return historyTaskInfoList;
    }

    public FlowableSingleToDoTaskDTO setHistoryTaskInfoList(List<HistoryTaskInfo> historyTaskInfoList) {
        this.historyTaskInfoList = historyTaskInfoList;
        return this;
    }

    public List<ProcessDefinitionUserTaskInfo> getProcessDefinitionUserTaskInfoList() {
        return processDefinitionUserTaskInfoList;
    }

    public FlowableSingleToDoTaskDTO setProcessDefinitionUserTaskInfoList(List<ProcessDefinitionUserTaskInfo> processDefinitionUserTaskInfoList) {
        this.processDefinitionUserTaskInfoList = processDefinitionUserTaskInfoList;
        return this;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public FlowableSingleToDoTaskDTO setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
        return this;
    }

    @Schema(description = "流程定义中的用户任务顺序信息")
    public static class ProcessDefinitionUserTaskInfo{
        /**
         * 任务名称
         */
        @Schema(description = "任务名称")
        private String taskName;

        /**
         * 任务节点KEY
         */
        @Schema(description = "任务节点KEY")
        private String taskDefinitionKey;

        /**
         * 是否高亮(true:是;false:否)
         */
        @Schema(description = "是否高亮(true:是;false:否)")
        private Boolean active;

        public String getTaskName() {
            return taskName;
        }

        public ProcessDefinitionUserTaskInfo setTaskName(String taskName) {
            this.taskName = taskName;
            return this;
        }

        public Boolean getActive() {
            return active;
        }

        public ProcessDefinitionUserTaskInfo setActive(Boolean active) {
            this.active = active;
            return this;
        }

        public String getTaskDefinitionKey() {
            return taskDefinitionKey;
        }

        public ProcessDefinitionUserTaskInfo setTaskDefinitionKey(String taskDefinitionKey) {
            this.taskDefinitionKey = taskDefinitionKey;
            return this;
        }
    }

    @Schema(description = "历史任务信息")
    public static class HistoryTaskInfo{

        /**
         * 任务ID
         */
        @Schema(description = "任务ID")
        private String taskId;

        /**
         * 任务节点KEY
         */
        @Schema(description = "任务节点KEY")
        private String taskDefinitionKey;

        /**
         * 任务名称
         */
        @Schema(description = "任务名称")
        private String taskName;

        /**
         * 任务处理人名称
         */
        @Schema(description = "任务处理人名称")
        private String assignUser;

        /**
         * 流程类型
         */
        @Schema(description = "流程类型")
        private String category;

        /**
         * 任务耗时
         */
        @Schema(description = "任务耗时")
        private String duration;

        /**
         * 任务创建时间
         */
        @Schema(description = "任务创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;

        /**
         * 任务完成时间
         */
        @Schema(description = "任务完成时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime finishTime;

        /**
         * 截止日期
         */
        @Schema(description = "截止日期")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime dueDate;

        /**
         * 审批备注意见
         */
        @Schema(description = "审批备注意见")
        private String comment;

        /**
         * 流程参数
         */
        @Schema(description = "流程参数")
        private Map<String, Object> parameter;


        /**
         * 附件列表
         */
        @Schema(description = "附件列表")
        private List<FlowableTaskCompleteDTO.AttachmentInfo> attachmentList;

        /**
         * 子任务信息列表
         */
        @Schema(description = "子任务信息列表")
        private List<HistoryTaskInfo> subHistoryTaskInfoList;

        public String getTaskId() {
            return taskId;
        }

        public HistoryTaskInfo setTaskId(String taskId) {
            this.taskId = taskId;
            return this;
        }

        public String getTaskName() {
            return taskName;
        }

        public HistoryTaskInfo setTaskName(String taskName) {
            this.taskName = taskName;
            return this;
        }

        public String getAssignUser() {
            return assignUser;
        }

        public HistoryTaskInfo setAssignUser(String assignUser) {
            this.assignUser = assignUser;
            return this;
        }

        public String getCategory() {
            return category;
        }

        public HistoryTaskInfo setCategory(String category) {
            this.category = category;
            return this;
        }

        public String getDuration() {
            return duration;
        }

        public HistoryTaskInfo setDuration(String duration) {
            this.duration = duration;
            return this;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public HistoryTaskInfo setCreateTime(LocalDateTime createTime) {
            this.createTime = createTime;
            return this;
        }

        public LocalDateTime getFinishTime() {
            return finishTime;
        }

        public HistoryTaskInfo setFinishTime(LocalDateTime finishTime) {
            this.finishTime = finishTime;
            return this;
        }

        public LocalDateTime getDueDate() {
            return dueDate;
        }

        public HistoryTaskInfo setDueDate(LocalDateTime dueDate) {
            this.dueDate = dueDate;
            return this;
        }

        public String getComment() {
            return comment;
        }

        public HistoryTaskInfo setComment(String comment) {
            this.comment = comment;
            return this;
        }

        public Map<String, Object> getParameter() {
            return parameter;
        }

        public HistoryTaskInfo setParameter(Map<String, Object> parameter) {
            this.parameter = parameter;
            return this;
        }

        public List<FlowableTaskCompleteDTO.AttachmentInfo> getAttachmentList() {
            return attachmentList;
        }

        public HistoryTaskInfo setAttachmentList(List<FlowableTaskCompleteDTO.AttachmentInfo> attachmentList) {
            this.attachmentList = attachmentList;
            return this;
        }

        public List<HistoryTaskInfo> getSubHistoryTaskInfoList() {
            return subHistoryTaskInfoList;
        }

        public HistoryTaskInfo setSubHistoryTaskInfoList(List<HistoryTaskInfo> subHistoryTaskInfoList) {
            this.subHistoryTaskInfoList = subHistoryTaskInfoList;
            return this;
        }

        public String getTaskDefinitionKey() {
            return taskDefinitionKey;
        }

        public HistoryTaskInfo setTaskDefinitionKey(String taskDefinitionKey) {
            this.taskDefinitionKey = taskDefinitionKey;
            return this;
        }
    }
}
