package net.airuima.rbase.dto.client;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/23
 */
@Schema(description = "Rworker上传或者页面导入GRR测试数据DTO")
public class ClientGrrHistorySaveDTO {
    /**
     * 台位编码
     */
    @Schema(description = "台位编码", required = true)
    @NotEmpty
    @Excel(name = "台位编码",orderNum = "1")
    private String workCellCode;


    /**
     * 测试人
     */
    @Schema(description = "测试人", required = true)
    @NotEmpty
    @Excel(name = "测试员工编号",orderNum = "2")
    private String tester;

    /**
     * 测试日期
     */
    @Schema(description = "测试日期", example = "2022-05-23 23:45:33")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @NotEmpty
    @Excel(name = "测试日期",orderNum = "3",format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime testTime;


    /**
     * 检测项目编码
     */
    @Schema(description = "检测项目编码", required = true)
    @NotEmpty
    @Excel(name = "检测项目编码",orderNum = "4")
    private String checkItemCode;

    /**
     * 子模块/通道
     */
    @Schema(description = "子模块", required = true)
    @NotEmpty
    @Excel(name = "子模块",orderNum = "5")
    private String subModule;

    /**
     * 样本SN
     */
    @Schema(description = "样本SN", required = true)
    @NotEmpty
    @Excel(name = "样本SN",orderNum = "6")
    private String sn;

    /**
     * 测试次数
     */
    @Schema(description = "测试次数", required = true)
    @NotEmpty
    @Excel(name = "测试次数",orderNum = "7")
    private Integer times;

    /**
     * 测试值
     */
    @Schema(description = "测试值", required = true)
    @NotEmpty
    @Excel(name = "测试值",orderNum = "8",numFormat = "#.###")
    private Double number;


    /**
     * GRR
     */
    @Schema(description = "GRR", required = true)
    @NotEmpty
    @Excel(name = "GRR",orderNum = "9",numFormat = "#.###")
    private Double grr;

    /**
     * NDC
     */
    @Schema(description = "NDC", required = true)
    @NotEmpty
    @Excel(name = "NDC",orderNum = "10",numFormat = "#.###")
    private Double ndc;

    public ClientGrrHistorySaveDTO() {

    }


    public String getWorkCellCode() {
        return workCellCode;
    }

    public ClientGrrHistorySaveDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public ClientGrrHistorySaveDTO setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public ClientGrrHistorySaveDTO setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public ClientGrrHistorySaveDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getCheckItemCode() {
        return checkItemCode;
    }

    public ClientGrrHistorySaveDTO setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }

    public String getSubModule() {
        return subModule;
    }

    public ClientGrrHistorySaveDTO setSubModule(String subModule) {
        this.subModule = subModule;
        return this;
    }

    public Integer getTimes() {
        return times;
    }

    public ClientGrrHistorySaveDTO setTimes(Integer times) {
        this.times = times;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public ClientGrrHistorySaveDTO setNumber(Double number) {
        this.number = number;
        return this;
    }

    public Double getGrr() {
        return grr;
    }

    public ClientGrrHistorySaveDTO setGrr(Double grr) {
        this.grr = grr;
        return this;
    }

    public Double getNdc() {
        return ndc;
    }

    public ClientGrrHistorySaveDTO setNdc(Double ndc) {
        this.ndc = ndc;
        return this;
    }
}
