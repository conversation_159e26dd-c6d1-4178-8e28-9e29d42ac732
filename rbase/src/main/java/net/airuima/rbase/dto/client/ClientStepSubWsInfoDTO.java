package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.OrganizationDTO;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork请求工单相关接口DTO
 *
 * <AUTHOR>
 * @date 2020/12/29
 */
@Schema(description = "Rwork请求工单返回参数信息")
public class ClientStepSubWsInfoDTO extends BaseClientDTO {

    /**
     * 子工单id
     */
    @Schema(description = "子工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long subWsId;

    /**
     * 工单id
     */
    @Schema(description = "工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wsId;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    private String serialNumber;

    /**
     * 子工单数量
     */
    @Schema(description = "子工单数量")
    private Integer subWsNumber;

    /**
     * 总工单数量
     */
    @Schema(description = "总工单数量")
    private Integer wsNumber;

    /**
     * 工单类型
     */
    @Schema(description = "工单类型")
    private Integer category;

    /**
     * 产品谱系id
     */
    @Schema(description = "产品谱系ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pedigreeId;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    private String pedigreeName;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    private String pedigreeCode;

    /**
     * 产品谱系型号规格
     */
    @Schema(description = "产品谱系规格型号")
    private String pedigreeSpecification;

    /**
     * 组织架构名称
     */
    @Schema(description = "组织架构名称")
    private String organizationName;

    /**
     * 组织架构编码
     */
    @Schema(description = "组织架构编码")
    private String organizationCode;

    /**
     * 生产线名称
     */
    @Schema(description = "生产线名称")
    private String workLineName;

    /**
     * 生产线编码
     */
    @Schema(description = "生产线编码")
    private String workLineCode;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String clientName;

    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    private String clientCode;

    /**
     * 生产过程批次管控级别(0:不管控物料库存;1:验证总工单物料库存;2:验证工位物料库存)
     */
    @Schema(description = "生产过程批次管控级别(0:不管控物料库存;1:验证总工单物料库存;2:验证工位物料库存)")
    private Integer materialControlLevel;
    /**
     * SN信息集合
     */
    @Schema(description = "SN信息集合")
    private List<SnInfo> snInfoList;


    /**
     * 验证容器返回的容器ID
     */
    @Schema(description = "验证容器号返回的容器ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long containerId;

    /**
     * 当前容器绑定状态 (0 ,解绑，1绑定)
     * <AUTHOR>
     * @date  2023/3/3
     * @return
     */
    private Integer containerIsBind;

    /**
     * 验证旧容器返回的容器投产数
     */
    @Schema(description = "验证旧容器返回的容器投产数")
    private Integer containerNumber;

    /**
     * 物料图片转base64编码
     */
    @Schema(description = "物料图片转base64编码")
    private List<String> pictureBase64Codes;

    /**
     * 物料基础信息dto
     */
    @Schema(description = "物料基础信息dto")
    private MaterialDTO materialDto;

    /**
     * 工单备注信息
     */
    @Schema(description = "工单备注信息")
    private String wsNote;

    /**
     * 子工单备注信息
     */
    @Schema(description = "子工单备注信息")
    private String subWsNote;

    /**
     * 工单合格数
     */
    @Schema(description = "工单合格数")
    private Integer wsQualifiedNumber;

    /**
     * 子工单合格数
     */
    @Schema(description = "子工单合格数")
    private Integer subWsQualifiedNumber;

    public ClientStepSubWsInfoDTO() {
    }

    public ClientStepSubWsInfoDTO(BaseClientDTO baseClientDTO) {
        this.setStatus(baseClientDTO.getStatus());
        this.setMessage(baseClientDTO.getMessage());
    }

    public ClientStepSubWsInfoDTO(SubWorkSheet subWorkSheet) {
        this.subWsId = subWorkSheet.getId();
        this.serialNumber = subWorkSheet.getSerialNumber();
        this.subWsNumber = subWorkSheet.getNumber();
        this.wsNumber = subWorkSheet.getWorkSheet().getNumber();
        this.category = subWorkSheet.getWorkSheet().getCategory();
        this.pedigreeId = subWorkSheet.getWorkSheet().getPedigree().getId();
        this.pedigreeName = subWorkSheet.getWorkSheet().getPedigree().getName();
        this.pedigreeCode = subWorkSheet.getWorkSheet().getPedigree().getCode();
        this.pedigreeSpecification = subWorkSheet.getWorkSheet().getPedigree().getSpecification();
        this.organizationName = Optional.ofNullable(subWorkSheet.getWorkSheet().getOrganizationDto()).map(OrganizationDTO::getName).orElse(null);
        this.organizationCode = Optional.ofNullable(subWorkSheet.getWorkSheet().getOrganizationDto()).map(OrganizationDTO::getCode).orElse(null);
        this.workLineName = Optional.ofNullable(subWorkSheet.getWorkLine()).map(WorkLine::getName).orElse(null);
        this.workLineCode = Optional.ofNullable(subWorkSheet.getWorkLine()).map(WorkLine::getCode).orElse(null);
        this.clientName = Optional.ofNullable(subWorkSheet.getWorkSheet().getClientDTO()).map(ClientDTO::getName).orElse(null);
        this.clientCode = Optional.ofNullable(subWorkSheet.getWorkSheet().getClientDTO()).map(ClientDTO::getCode).orElse(null);
        this.wsNote = subWorkSheet.getWorkSheet().getNote();
        this.subWsNote = subWorkSheet.getNote();
        this.subWsQualifiedNumber = subWorkSheet.getQualifiedNumber();
    }

    public ClientStepSubWsInfoDTO(WorkSheet workSheet) {
        this.wsId = workSheet.getId();
        this.serialNumber = workSheet.getSerialNumber();
        this.wsNumber = workSheet.getNumber();
        this.category = workSheet.getCategory();
        this.pedigreeId = workSheet.getPedigree().getId();
        this.pedigreeName = workSheet.getPedigree().getName();
        this.pedigreeCode = workSheet.getPedigree().getCode();
        this.pedigreeSpecification = workSheet.getPedigree().getSpecification();
        this.organizationName = Optional.ofNullable(workSheet.getOrganizationDto()).map(OrganizationDTO::getName).orElse(null);
        this.organizationCode = Optional.ofNullable(workSheet.getOrganizationDto()).map(OrganizationDTO::getCode).orElse(null);
        this.workLineName = Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getName).orElse(null);
        this.workLineCode = Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getCode).orElse(null);
        this.clientName = Optional.ofNullable(workSheet.getClientDTO()).map(ClientDTO::getName).orElse(null);
        this.clientCode = Optional.ofNullable(workSheet.getClientDTO()).map(ClientDTO::getCode).orElse(null);
        this.wsNote = workSheet.getNote();
        this.wsQualifiedNumber = workSheet.getQualifiedNumber();
    }

    public Integer getContainerIsBind() {
        return containerIsBind;
    }

    public ClientStepSubWsInfoDTO setContainerIsBind(Integer containerIsBind) {
        this.containerIsBind = containerIsBind;
        return this;
    }

    public String getWsNote() {
        return wsNote;
    }

    public ClientStepSubWsInfoDTO setWsNote(String wsNote) {
        this.wsNote = wsNote;
        return this;
    }

    public String getSubWsNote() {
        return subWsNote;
    }

    public ClientStepSubWsInfoDTO setSubWsNote(String subWsNote) {
        this.subWsNote = subWsNote;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public ClientStepSubWsInfoDTO setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public List<String> getPictureBase64Codes() {
        return pictureBase64Codes;
    }

    public ClientStepSubWsInfoDTO setPictureBase64Codes(List<String> pictureBase64Codes) {
        this.pictureBase64Codes = pictureBase64Codes;
        return this;
    }

    public Long getSubWsId() {
        return subWsId;
    }

    public ClientStepSubWsInfoDTO setSubWsId(Long subWsId) {
        this.subWsId = subWsId;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public ClientStepSubWsInfoDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getSubWsNumber() {
        return subWsNumber;
    }

    public ClientStepSubWsInfoDTO setSubWsNumber(Integer subWsNumber) {
        this.subWsNumber = subWsNumber;
        return this;
    }

    public Integer getWsNumber() {
        return wsNumber;
    }

    public ClientStepSubWsInfoDTO setWsNumber(Integer wsNumber) {
        this.wsNumber = wsNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public ClientStepSubWsInfoDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public ClientStepSubWsInfoDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public ClientStepSubWsInfoDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public ClientStepSubWsInfoDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeSpecification() {
        return pedigreeSpecification;
    }

    public ClientStepSubWsInfoDTO setPedigreeSpecification(String pedigreeSpecification) {
        this.pedigreeSpecification = pedigreeSpecification;
        return this;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public ClientStepSubWsInfoDTO setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
        return this;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public ClientStepSubWsInfoDTO setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public ClientStepSubWsInfoDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public String getWorkLineCode() {
        return workLineCode;
    }

    public ClientStepSubWsInfoDTO setWorkLineCode(String workLineCode) {
        this.workLineCode = workLineCode;
        return this;
    }

    public String getClientName() {
        return clientName;
    }

    public ClientStepSubWsInfoDTO setClientName(String clientName) {
        this.clientName = clientName;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public ClientStepSubWsInfoDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public List<SnInfo> getSnInfoList() {
        return snInfoList;
    }

    public ClientStepSubWsInfoDTO setSnInfoList(List<SnInfo> snInfoList) {
        this.snInfoList = snInfoList;
        return this;
    }

    public Long getContainerId() {
        return containerId;
    }

    public ClientStepSubWsInfoDTO setContainerId(Long containerId) {
        this.containerId = containerId;
        return this;
    }

    public Integer getContainerNumber() {
        return containerNumber;
    }

    public ClientStepSubWsInfoDTO setContainerNumber(Integer containerNumber) {
        this.containerNumber = containerNumber;
        return this;
    }

    public Integer getMaterialControlLevel() {
        return materialControlLevel;
    }

    public ClientStepSubWsInfoDTO setMaterialControlLevel(Integer materialControlLevel) {
        this.materialControlLevel = materialControlLevel;
        return this;
    }

    /**
     * 请求传过来的子工单信息
     */
    public static class SubWorkSheetInfo {

        /**
         * 子工单号
         */
        private String serialNumber;

        /**
         * 是否检查工单状态
         */
        private Boolean isCheck;

        /**
         * 是否需要SN信息
         */
        private Boolean isNeedSn;

        public String getSerialNumber() {
            return serialNumber;
        }

        public SubWorkSheetInfo setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }

        public Boolean getCheck() {
            return isCheck;
        }

        public SubWorkSheetInfo setCheck(Boolean check) {
            isCheck = check;
            return this;
        }

        public Boolean getNeedSn() {
            return isNeedSn;
        }

        public SubWorkSheetInfo setNeedSn(Boolean needSn) {
            isNeedSn = needSn;
            return this;
        }
    }

    /**
     * 请求传过来的容器信息
     */
    public static class ContainerInfo {

        /**
         * 容器号验证类型0:请求的旧容器号验证;1:绑定的新容器号验证
         */
        private Integer validateType;
        /**
         * 验证扫描的容器号(无论是旧容器还是新容器)
         */
        private String validateContainer;

        /**
         * 请求工序的容器号列表(数组内不允许重复)
         */
        private List<String> requestContainers;

        /**
         * 绑定新容器列表(不一定是新容器号，可能与请求的容器号相同,数组内不允许重复)
         */
        private List<String> bindContainers;

        public List<String> getRequestContainers() {
            return requestContainers;
        }

        public ContainerInfo setRequestContainers(List<String> requestContainers) {
            this.requestContainers = requestContainers;
            return this;
        }

        public List<String> getBindContainers() {
            return bindContainers;
        }

        public ContainerInfo setBindContainers(List<String> bindContainers) {
            this.bindContainers = bindContainers;
            return this;
        }

        public String getValidateContainer() {
            return validateContainer;
        }

        public ContainerInfo setValidateContainer(String validateContainer) {
            this.validateContainer = validateContainer;
            return this;
        }

        public Integer getValidateType() {
            return validateType;
        }

        public ContainerInfo setValidateType(Integer validateType) {
            this.validateType = validateType;
            return this;
        }
    }

    /**
     * 返回SN信息
     */
    @Schema(description = "SN信息")
    public static class SnInfo {

        /**
         * SN
         */
        @Schema(description = "SN")
        private String sn;

        /**
         * SN状态
         */
        @Schema(description = "SN状态")
        private Integer status;

        public SnInfo() {
        }

        public SnInfo(String sn, Integer status) {
            this.sn = sn;
            this.status = status;
        }

        public SnInfo(SnWorkStatus snWorkStatus) {
            this.sn = snWorkStatus.getSn();
            this.status = snWorkStatus.getStatus();
        }

        public String getSn() {
            return sn;
        }

        public SnInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public Integer getStatus() {
            return status;
        }

        public SnInfo setStatus(Integer status) {
            this.status = status;
            return this;
        }
    }

    public Long getWsId() {
        return wsId;
    }

    public ClientStepSubWsInfoDTO setWsId(Long wsId) {
        this.wsId = wsId;
        return this;
    }

    public Integer getWsQualifiedNumber() {
        return wsQualifiedNumber;
    }

    public ClientStepSubWsInfoDTO setWsQualifiedNumber(Integer wsQualifiedNumber) {
        this.wsQualifiedNumber = wsQualifiedNumber;
        return this;
    }

    public Integer getSubWsQualifiedNumber() {
        return subWsQualifiedNumber;
    }

    public ClientStepSubWsInfoDTO setSubWsQualifiedNumber(Integer subWsQualifiedNumber) {
        this.subWsQualifiedNumber = subWsQualifiedNumber;
        return this;
    }
}
