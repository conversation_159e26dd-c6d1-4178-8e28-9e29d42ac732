package net.airuima.rbase.dto.custom;


import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 备用字段DTO
 *
 * <AUTHOR>
 * @date 2023-02-01
 */
@Schema(description = "备用字段")
public class CustomDTO {

    /**
     * 备用字段1
     */
    @Schema(description = "备用字段1")
    private String custom1;

    /**
     * 备用字段2
     */
    @Schema(description = "备用字段2")
    private String custom2;

    /**
     * 备用字段3
     */
    @Schema(description = "备用字段3")
    private String custom3;

    /**
     * 备用字段4
     */
    @Schema(description = "备用字段4")
    private String custom4;

    /**
     * 备用字段5
     */
    @Schema(description = "备用字段5")
    private String custom5;

    public String getCustom1() {
        return custom1;
    }

    public CustomDTO setCustom1(String custom1) {
        this.custom1 = custom1;
        return this;
    }

    public String getCustom2() {
        return custom2;
    }

    public CustomDTO setCustom2(String custom2) {
        this.custom2 = custom2;
        return this;
    }

    public String getCustom3() {
        return custom3;
    }

    public CustomDTO setCustom3(String custom3) {
        this.custom3 = custom3;
        return this;
    }

    public String getCustom4() {
        return custom4;
    }

    public CustomDTO setCustom4(String custom4) {
        this.custom4 = custom4;
        return this;
    }

    public String getCustom5() {
        return custom5;
    }

    public CustomDTO setCustom5(String custom5) {
        this.custom5 = custom5;
        return this;
    }
}
