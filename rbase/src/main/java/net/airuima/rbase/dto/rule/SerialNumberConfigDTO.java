package net.airuima.rbase.dto.rule;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class SerialNumberConfigDTO extends AbstractDto {

    /**
     * 前缀类型(0:固定前缀;1:动态前缀)
     */
    @Schema(description = "前缀类型(0:无前缀;1:固定前缀;2:动态前缀)", required = true)
    private Integer category;

    /**
     * 序列编码
     */
    @Schema(description = "序列编码", required = true)
    private String serialCode;

    /**
     * 序列类型
     */
    @Schema(description = "序列类型", required = true)
    private String serialType;

    /**
     * 序列前缀
     */
    @Schema(description = "序列前缀")
    private String serialPrefix;

    /**
     * 序列日期格式
     */
    @Schema(description = "序列日期格式")
    private String serialDateFormat;

    /**
     * 随机数
     */
    @Schema(description = "随机数", required = true)
    private String serialRange;

    /**
     * 分隔符
     */
    @Schema(description = "分隔符")
    private String separate;

    @Schema(description = "序列号最大个数")
    private int maxNumber;

    /**
     * 描述信息
     */
    @Schema(description = "描述信息")
    private String description;

    public Integer getCategory() {
        return category;
    }

    public SerialNumberConfigDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getSerialCode() {
        return serialCode;
    }

    public SerialNumberConfigDTO setSerialCode(String serialCode) {
        this.serialCode = serialCode;
        return this;
    }

    public String getSerialType() {
        return serialType;
    }

    public SerialNumberConfigDTO setSerialType(String serialType) {
        this.serialType = serialType;
        return this;
    }

    public String getSerialPrefix() {
        return serialPrefix;
    }

    public SerialNumberConfigDTO setSerialPrefix(String serialPrefix) {
        this.serialPrefix = serialPrefix;
        return this;
    }

    public String getSerialDateFormat() {
        return serialDateFormat;
    }

    public SerialNumberConfigDTO setSerialDateFormat(String serialDateFormat) {
        this.serialDateFormat = serialDateFormat;
        return this;
    }

    public String getSerialRange() {
        return serialRange;
    }

    public SerialNumberConfigDTO setSerialRange(String serialRange) {
        this.serialRange = serialRange;
        return this;
    }

    public String getSeparate() {
        return separate;
    }

    public SerialNumberConfigDTO setSeparate(String separate) {
        this.separate = separate;
        return this;
    }

    public int getMaxNumber() {
        return maxNumber;
    }

    public SerialNumberConfigDTO setMaxNumber(int maxNumber) {
        this.maxNumber = maxNumber;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public SerialNumberConfigDTO setDescription(String description) {
        this.description = description;
        return this;
    }
}
