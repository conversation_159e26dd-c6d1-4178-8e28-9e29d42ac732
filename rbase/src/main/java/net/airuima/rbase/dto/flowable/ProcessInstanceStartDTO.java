package net.airuima.rbase.dto.flowable;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/10
 */
@Schema(description = "启动流程DTO")
public class ProcessInstanceStartDTO {
    /**
     * 流程定义ID
     */
    @Schema(description = "流程定义ID")
    private String processDefinitionId;

    /**
     * 登录名
     */
    @Schema(description = "登录名")
    private String loginName;

    /**
     * 业务数据ID
     */
    @Schema(description = "业务数据ID")
    private String businessKey;

    /**
     * 流程参数
     */
    @Schema(description = "流程参数")
    private Map<String, Object> parameter;

    /**
     * 流程备注
     */
    @Schema(description = "流程备注")
    private String comment;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<AttachmentDTO> attachmentList;


    public ProcessInstanceStartDTO() {
    }

    public ProcessInstanceStartDTO(String processDefinitionId, String loginName, String businessKey, Map<String, Object> parameter, String comment, List<AttachmentDTO> attachmentList) {
        this.processDefinitionId = processDefinitionId;
        this.loginName = loginName;
        this.businessKey = businessKey;
        this.parameter = parameter;
        this.comment = comment;
        this.attachmentList = attachmentList;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public ProcessInstanceStartDTO setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
        return this;
    }

    public String getLoginName() {
        return loginName;
    }

    public ProcessInstanceStartDTO setLoginName(String loginName) {
        this.loginName = loginName;
        return this;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public ProcessInstanceStartDTO setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
        return this;
    }

    public Map<String, Object> getParameter() {
        return parameter;
    }

    public ProcessInstanceStartDTO setParameter(Map<String, Object> parameter) {
        this.parameter = parameter;
        return this;
    }

    public String getComment() {
        return comment;
    }

    public ProcessInstanceStartDTO setComment(String comment) {
        this.comment = comment;
        return this;
    }

    public List<AttachmentDTO> getAttachmentList() {
        return attachmentList;
    }

    public ProcessInstanceStartDTO setAttachmentList(List<AttachmentDTO> attachmentList) {
        this.attachmentList = attachmentList;
        return this;
    }
}
