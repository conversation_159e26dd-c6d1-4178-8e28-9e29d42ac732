package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.domain.base.scene.WorkCell;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork员工相关接口DTO
 *
 * <AUTHOR>
 * @date 2020/12/28
 */
@Schema(description = "Rwork请求返回员工信息")
public class ClientStaffDTO extends BaseClientDTO {

    /**
     * 员工id
     */
    @Schema(description = "员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    /**
     * 员工名称
     */
    @Schema(description = "员工姓名")
    private String staffName;

    /**
     * 员工编码
     */
    @Schema(description = "员工编码")
    private String staffCode;

    /**
     * 员工所属组织架构名称
     */
    @Schema(description = "员工所属组织架构名称")
    private String staffOrganizationName;

    /**
     * 员工所属组织架构编码
     */
    @Schema(description = "员工所属组织架构编码")
    private String staffOrganizationCode;

    /**
     * 员工绑定工位数组
     */
    @Schema(description = "员工绑定的工位列表")
    private List<WorkCellDTO> workCellDtoList;

    public ClientStaffDTO() {
        // 空构造
    }

    public Long getStaffId() {
        return staffId;
    }

    public ClientStaffDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public String getStaffName() {
        return staffName;
    }

    public ClientStaffDTO setStaffName(String staffName) {
        this.staffName = staffName;
        return this;
    }

    public String getStaffCode() {
        return staffCode;
    }

    public ClientStaffDTO setStaffCode(String staffCode) {
        this.staffCode = staffCode;
        return this;
    }

    public String getStaffOrganizationName() {
        return staffOrganizationName;
    }

    public ClientStaffDTO setStaffOrganizationName(String staffOrganizationName) {
        this.staffOrganizationName = staffOrganizationName;
        return this;
    }

    public String getStaffOrganizationCode() {
        return staffOrganizationCode;
    }

    public ClientStaffDTO setStaffOrganizationCode(String staffOrganizationCode) {
        this.staffOrganizationCode = staffOrganizationCode;
        return this;
    }

    public List<WorkCellDTO> getWorkCellDtoList() {
        return workCellDtoList;
    }

    public ClientStaffDTO setWorkCellDtoList(List<WorkCellDTO> workCellDtoList) {
        this.workCellDtoList = workCellDtoList;
        return this;
    }

    /**
     * 工位DTO
     */
    @Schema(description = "工位信息")
    public static class WorkCellDTO {

        /**
         * 工位id
         */
        @Schema(description = "工位ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long workCellId;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String workCellName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;

        /**
         * 是否连接设备
         */
        @Schema(description = "是否连接设备")
        private Boolean isConnectEquipment;

        @Schema(description = "工位类型")
        private Integer category;

        @Schema(description = "工位IP")
        private String workCellIp;

        @Schema(description = "线体DTO")
        private WorkLineDTO workLineDto;

        public WorkCellDTO() {
        }

        public WorkCellDTO(WorkCell workCell) {
            this.workCellId = workCell.getId();
            this.workCellName = workCell.getName();
            this.workCellCode = workCell.getCode();
            this.category = workCell.getCategory();
            this.isConnectEquipment = workCell.getIsConnectEquipment();
            this.workCellIp = workCell.getIp();
            this.workLineDto = new WorkLineDTO(workCell.getWorkLine() != null?workCell.getWorkLine().getCode():null,workCell.getWorkLine() != null?workCell.getWorkLine().getName():null);
        }

        public Long getWorkCellId() {
            return workCellId;
        }

        public WorkCellDTO setWorkCellId(Long workCellId) {
            this.workCellId = workCellId;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public WorkCellDTO setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public WorkCellDTO setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public Integer getCategory() {
            return category;
        }

        public WorkCellDTO setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public Boolean getConnectEquipment() {
            return isConnectEquipment;
        }

        public WorkCellDTO setConnectEquipment(Boolean connectEquipment) {
            isConnectEquipment = connectEquipment;
            return this;
        }

        public String getWorkCellIp() {
            return workCellIp;
        }

        public WorkCellDTO setWorkCellIp(String workCellIp) {
            this.workCellIp = workCellIp;
            return this;
        }

        public WorkLineDTO getWorkLineDto() {
            return workLineDto;
        }

        public WorkCellDTO setWorkLineDto(WorkLineDTO workLineDto) {
            this.workLineDto = workLineDto;
            return this;
        }
    }

    /**
     * 线体DTO
     */
    public static class WorkLineDTO{
        /**
         * 线体名称
         */
        @Schema(description = "线体名称")
        private String workLineName;

        /**
         * 线体编码
         */
        @Schema(description = "线体编码")
        private String workLineCode;

        public WorkLineDTO() {
        }

        public WorkLineDTO(String workLineCode, String workLineName) {
            this.workLineName = workLineName;
            this.workLineCode = workLineCode;
        }

        public String getWorkLineName() {
            return workLineName;
        }

        public WorkLineDTO setWorkLineName(String workLineName) {
            this.workLineName = workLineName;
            return this;
        }

        public String getWorkLineCode() {
            return workLineCode;
        }

        public WorkLineDTO setWorkLineCode(String workLineCode) {
            this.workLineCode = workLineCode;
            return this;
        }

    }

}
