package net.airuima.rbase.dto.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/8/25
 */
@Schema(description = "上道容器详情下交信息DTO")
public class PreContainerDetailInfo implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "上道容器详情id")
    private Long preContainerDetailId;

    @Schema(description = "上容器详情流转数量")
    private Integer number;

    public PreContainerDetailInfo() {
    }

    public PreContainerDetailInfo(Long preContainerDetailId, Integer number) {
        this.preContainerDetailId = preContainerDetailId;
        this.number = number;
    }

    public Long getPreContainerDetailId() {
        return preContainerDetailId;
    }

    public PreContainerDetailInfo setPreContainerDetailId(Long preContainerDetailId) {
        this.preContainerDetailId = preContainerDetailId;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public PreContainerDetailInfo setNumber(Integer number) {
        this.number = number;
        return this;
    }
}
