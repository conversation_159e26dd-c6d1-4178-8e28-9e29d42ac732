package net.airuima.rbase.dto.qms;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;

@Schema(name = "缺陷类型表(DefectGroup)", description = "缺陷类型表")
public class DefectGroupDTO extends AbstractDto implements Serializable {

    /**
     * 缺陷类型名称
     */
    @Schema(description = "缺陷类型名称")
    private String name;

    /**
     * 缺陷类型编码
     */
    @Schema(description = "缺陷类型编码")
    private String code;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    private boolean isEnable;

    public String getName() {
        return name;
    }

    public DefectGroupDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public DefectGroupDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean isEnable() {
        return isEnable;
    }

    public DefectGroupDTO setEnable(boolean enable) {
        isEnable = enable;
        return this;
    }
}
