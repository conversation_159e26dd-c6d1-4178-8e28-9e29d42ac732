package net.airuima.rbase.dto.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistory;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * MRB评审申请请求参数DTO
 * <AUTHOR>
 */
public class MrbApplicantRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验单号
     */
    @Schema(description = "检验单号")
    private String inspectSerialNumber;

    /**
     * 检验品编码
     */
    @Schema(description = "检验品编码")
    private String inspectTargetCode;

    /**
     * 检验品名称
     */
    @Schema(description = "检验品名称")
    private String inspectTargetName;

    /**
     * 检验品规格型号
     */
    @Schema(description = "检验品规格型号")
    private String inspectTargetSpecification;

    /**
     * 供应商编码
     */
    @Schema(description = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    private String clientCode;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String clientName;

    /**
     * 来源：0:来料检;1:抽检;2:终检
     */
    @Schema(description = "来源：0:来料检;1:抽检;2:终检")
    private int source;

    /**
     * 申请人名称
     */
    @Schema(description = "申请人名称")
    private String applicantName;

    /**
     * 申请人编码
     */
    @Schema(description = "申请人编码")
    private String applicantCode;

    /**
     * 申请人组织编码
     */
    @Schema(description = "申请人组织编码")
    private String applicantOrgCode;

    /**
     * 申请人组织名称
     */
    @Schema(description = "申请人组织名称")
    private String applicantOrgName;

    /**
     * 申请原因
     */
    @Schema(description = "申请原因")
    private String reason;

    /**
     * 申请时间
     */
    @Schema(description = "申请时间")
    private LocalDateTime applicantDate;

    /**
     * 质检明细数据
     */
    @Schema(description = "质检明细数据")
    private MrbApplicantRequestDetailDTO sourceInspectInfo;

    public MrbApplicantRequestDTO(IqcCheckHistory iqcCheckHistory){
        this.applicantDate = LocalDateTime.now();
        this.applicantCode = Objects.nonNull(iqcCheckHistory.getDealerId())?iqcCheckHistory.getDealerDto().getCode(): StringUtils.EMPTY;
        this.applicantName = Objects.nonNull(iqcCheckHistory.getDealerId())?iqcCheckHistory.getDealerDto().getName(): StringUtils.EMPTY;
        this.applicantOrgCode = Objects.nonNull(iqcCheckHistory.getDealerId())?iqcCheckHistory.getDealerDto().getOrganization().getCode(): StringUtils.EMPTY;
        this.applicantOrgName = Objects.nonNull(iqcCheckHistory.getDealerId())?iqcCheckHistory.getDealerDto().getOrganization().getName(): StringUtils.EMPTY;
        this.inspectSerialNumber = iqcCheckHistory.getSerialNumber();
        this.inspectTargetCode = iqcCheckHistory.getMaterialDto().getCode();
        this.inspectTargetName = iqcCheckHistory.getMaterialDto().getName();
        this.inspectTargetSpecification = iqcCheckHistory.getMaterialDto().getSpecification();
        this.supplierCode = iqcCheckHistory.getSupplierDto().getCode();
        this.supplierName = iqcCheckHistory.getSupplierDto().getName();
        this.clientCode = iqcCheckHistory.getClientDto().getCode();
        this.clientName = iqcCheckHistory.getClientDto().getName();
        this.source = Constants.INT_ZERO;
    }

    public MrbApplicantRequestDTO(CheckHistory checkHistory, WorkSheet workSheet,StaffDTO dealerDto){
        this.applicantDate = LocalDateTime.now();
        this.applicantCode = dealerDto.getCode();
        this.applicantName = dealerDto.getName();
        this.applicantOrgCode = dealerDto.getOrganization().getCode();
        this.applicantOrgName = dealerDto.getOrganization().getName();
        this.inspectSerialNumber = checkHistory.getSerialNumber();
        this.inspectTargetCode = workSheet.getPedigree().getCode();
        this.inspectTargetName = workSheet.getPedigree().getName();
        this.inspectTargetSpecification = workSheet.getPedigree().getSpecification();
        this.clientCode = Objects.nonNull(workSheet.getClientId()) ? workSheet.getClientDTO().getCode():null;
        this.clientName = Objects.nonNull(workSheet.getClientId()) ? workSheet.getClientDTO().getName():null;
        this.source = checkHistory.getCategory() == Constants.INT_THREE? Constants.INT_TWO:Constants.INT_ONE;
    }

    public MrbApplicantRequestDTO(CheckHistory checkHistory){

    }

    public String getInspectSerialNumber() {
        return inspectSerialNumber;
    }

    public MrbApplicantRequestDTO setInspectSerialNumber(String inspectSerialNumber) {
        this.inspectSerialNumber = inspectSerialNumber;
        return this;
    }

    public String getInspectTargetCode() {
        return inspectTargetCode;
    }

    public MrbApplicantRequestDTO setInspectTargetCode(String inspectTargetCode) {
        this.inspectTargetCode = inspectTargetCode;
        return this;
    }

    public String getInspectTargetName() {
        return inspectTargetName;
    }

    public MrbApplicantRequestDTO setInspectTargetName(String inspectTargetName) {
        this.inspectTargetName = inspectTargetName;
        return this;
    }

    public String getInspectTargetSpecification() {
        return inspectTargetSpecification;
    }

    public MrbApplicantRequestDTO setInspectTargetSpecification(String inspectTargetSpecification) {
        this.inspectTargetSpecification = inspectTargetSpecification;
        return this;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public MrbApplicantRequestDTO setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
        return this;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public MrbApplicantRequestDTO setSupplierName(String supplierName) {
        this.supplierName = supplierName;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public MrbApplicantRequestDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public String getClientName() {
        return clientName;
    }

    public MrbApplicantRequestDTO setClientName(String clientName) {
        this.clientName = clientName;
        return this;
    }

    public int getSource() {
        return source;
    }

    public MrbApplicantRequestDTO setSource(int source) {
        this.source = source;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public MrbApplicantRequestDTO setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public MrbApplicantRequestDTO setApplicantName(String applicantName) {
        this.applicantName = applicantName;
        return this;
    }

    public String getApplicantCode() {
        return applicantCode;
    }

    public MrbApplicantRequestDTO setApplicantCode(String applicantCode) {
        this.applicantCode = applicantCode;
        return this;
    }

    public String getApplicantOrgCode() {
        return applicantOrgCode;
    }

    public MrbApplicantRequestDTO setApplicantOrgCode(String applicantOrgCode) {
        this.applicantOrgCode = applicantOrgCode;
        return this;
    }

    public String getApplicantOrgName() {
        return applicantOrgName;
    }

    public MrbApplicantRequestDTO setApplicantOrgName(String applicantOrgName) {
        this.applicantOrgName = applicantOrgName;
        return this;
    }

    public LocalDateTime getApplicantDate() {
        return applicantDate;
    }

    public MrbApplicantRequestDTO setApplicantDate(LocalDateTime applicantDate) {
        this.applicantDate = applicantDate;
        return this;
    }

    public MrbApplicantRequestDetailDTO getSourceInspectInfo() {
        return sourceInspectInfo;
    }

    public MrbApplicantRequestDTO setSourceInspectInfo(MrbApplicantRequestDetailDTO sourceInspectInfo) {
        this.sourceInspectInfo = sourceInspectInfo;
        return this;
    }

    @Schema(description = "质检明细数据")
    public static class MrbApplicantRequestDetailDTO {
        /**
         * 报检数量
         */
        @Schema(description = "报检数量")
        private Integer population;

        /**
         * 抽样数量
         */
        @Schema(description = "抽样数量")
        private Integer sample;

        /**
         * 不良数量
         */
        @Schema(description = "不良数量")
        private Integer unqualifiedNumber;

        /**
         * 不良描述
         */
        @Schema(description = "不良描述")
        private String unqualifiedDescription;

        /**
         * SN检验信息列表
         */
        @Schema(description = "SN检验信息列表")
        private List<InspectInfo> inspectInfoList;


        public Integer getPopulation() {
            return population;
        }

        public MrbApplicantRequestDetailDTO setPopulation(Integer population) {
            this.population = population;
            return this;
        }

        public Integer getSample() {
            return sample;
        }

        public MrbApplicantRequestDetailDTO setSample(Integer sample) {
            this.sample = sample;
            return this;
        }

        public Integer getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public MrbApplicantRequestDetailDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public String getUnqualifiedDescription() {
            return unqualifiedDescription;
        }

        public MrbApplicantRequestDetailDTO setUnqualifiedDescription(String unqualifiedDescription) {
            this.unqualifiedDescription = unqualifiedDescription;
            return this;
        }

        public List<InspectInfo> getInspectInfoList() {
            return inspectInfoList;
        }

        public MrbApplicantRequestDetailDTO setInspectInfoList(List<InspectInfo> inspectInfoList) {
            this.inspectInfoList = inspectInfoList;
            return this;
        }

        /**
         * 检验信息
         */
        @Schema(description = "检验信息")
        public static class InspectInfo {

            /**
             * SN
             */
            @Schema(description = "SN")
            private String sn;

            /**
             * 缺陷
             */
            @Schema(description = "缺陷")
            private List<String> defectList;

            /**
             * 检验项目信息列表
             */
            @Schema(description = "检验项目信息列表")
            private List<InspectItemInfo> inspectItemInfoList;

            public String getSn() {
                return sn;
            }

            public InspectInfo setSn(String sn) {
                this.sn = sn;
                return this;
            }

            public List<String> getDefectList() {
                return defectList;
            }

            public InspectInfo setDefectList(List<String> defectList) {
                this.defectList = defectList;
                return this;
            }

            public List<InspectItemInfo> getInspectItemInfoList() {
                return inspectItemInfoList;
            }

            public InspectInfo setInspectItemInfoList(List<InspectItemInfo> inspectItemInfoList) {
                this.inspectItemInfoList = inspectItemInfoList;
                return this;
            }

            /**
             * 检验项目信息
             */
            @Schema(description = "检验项目信息")
            public static class InspectItemInfo {
                /**
                 * 检验项目编码
                 */
                @Schema(description = "检验项目编码")
                private String code;

                /**
                 * 检验项目名称
                 */
                @Schema(description = "检验项目名称")
                private String name;

                /**
                 * 检验项目合格范围
                 */
                @Schema(description = "检验项目合格范围")
                private String qualifiedRange;

                /**
                 * 检验项目检测数据
                 */
                @Schema(description = "检验项目检测数据")
                private String checkData;

                /**
                 * 检验项目检测结果
                 */
                @Schema(description = "检验项目检测结果")
                private Boolean result;

                public String getCode() {
                    return code;
                }

                public InspectItemInfo setCode(String code) {
                    this.code = code;
                    return this;
                }

                public String getName() {
                    return name;
                }

                public InspectItemInfo setName(String name) {
                    this.name = name;
                    return this;
                }

                public String getQualifiedRange() {
                    return qualifiedRange;
                }

                public InspectItemInfo setQualifiedRange(String qualifiedRange) {
                    this.qualifiedRange = qualifiedRange;
                    return this;
                }

                public String getCheckData() {
                    return checkData;
                }

                public InspectItemInfo setCheckData(String checkData) {
                    this.checkData = checkData;
                    return this;
                }

                public Boolean getResult() {
                    return result;
                }

                public InspectItemInfo setResult(Boolean result) {
                    this.result = result;
                    return this;
                }
            }

        }

    }
}
