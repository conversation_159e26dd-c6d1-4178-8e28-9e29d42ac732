package net.airuima.rbase.dto.client;

import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;

import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
public class MaintainReworkDTO {

    /**
     * 工单
     */
    private WorkSheet workSheet;

    /**
     * 维系历史
     */
    private MaintainHistoryDTO maintainHistory;

    /**
     * 工艺路线
     */
    private WorkFlow workFlow;

    /**
     * 工序
     */
    private Step step;

    public MaintainReworkDTO() {
    }

    public MaintainReworkDTO(MaintainReworkDTO maintainReworkDto) {
        this.workSheet = maintainReworkDto.getWorkSheet();
        this.maintainHistory = maintainReworkDto.getMaintainHistory();
        this.workFlow = maintainReworkDto.workFlow;
        this.step = maintainReworkDto.getStep();
    }

    public MaintainReworkDTO(WorkSheet workSheet, MaintainHistoryDTO maintainHistory, WorkFlow workFlow, Step step) {
        this.workSheet = workSheet;
        this.maintainHistory = maintainHistory;
        this.workFlow = workFlow;
        this.step = step;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public MaintainReworkDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public MaintainHistoryDTO getMaintainHistory() {
        return maintainHistory;
    }

    public MaintainReworkDTO setMaintainHistory(MaintainHistoryDTO maintainHistory) {
        this.maintainHistory = maintainHistory;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public MaintainReworkDTO setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public MaintainReworkDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MaintainReworkDTO that = (MaintainReworkDTO) o;
        return Objects.equals(workSheet, that.workSheet) && Objects.equals(workFlow, that.workFlow) && Objects.equals(step, that.step);
    }

    @Override
    public int hashCode() {
        return Objects.hash(workSheet, workFlow, step);
    }
}
