package net.airuima.rbase.dto.aps;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.dto.AbstractDto;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.batch.WsMaterialDTO;
import net.airuima.rbase.dto.bom.BomInfoDTO;
import net.airuima.rbase.dto.flowable.FlowableTaskCompleteDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import net.airuima.rbase.dto.process.StepDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020-12-24
 */
@Schema(description = "工单信息DTO(包含工单信息、工序快照及投料单信息)")
@FetchEntity
public class WorkSheetDTO extends AbstractDto {

    /**
     * 工单号
     */
    @Schema(description = "工单号",type = "string")
    private String serialNumber;

    /**
     * 工单投产数量
     */
    @Schema(description = "工单投产数量",type = "int",format = "int32")
    private Integer number;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期",type = "date",format = "date-time")
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划结单日期",type = "date",format = "date-time")
    private LocalDateTime planEndDate;

    /**
     * 工单类型
     */
    @Schema(description = "工单类型",type = "int",format = "int32",example = "-1:返工单,0:返修单,1:正常单")
    private Integer category;

    /**
     * 是否自动生成子工单
     */
    @Schema(description = "是否自动生成子工单")
    private Boolean isAutoGenerateSubWs = false;

    /**
     * 备注信息
     */
    private String note;
    /**
     * 产品谱系
     */
    @NotNull
    private Pedigree pedigree;
    /**
     * 物料方案ID
     */
    @NotNull
    private Long bomInfoId;
    /**
     * 物料方案DTO
     */
    private BomInfoDTO bomInfoDto;

    /**
     * 流程框图
     */
    private WorkFlow workFlow;

    /**
     * 组织架构DTO
     */
    private OrganizationDTO organizationDto;

    /**
     * 组织架构ID
     */
    @NotNull
    private Long organizationId;

    /**
     * 生产线
     */
    private WorkLine workLine;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    private ClientDTO clientDTO = new ClientDTO();

    /**
     * 定制的工序DTO
     */
    private List<StepDTO> stepDtoList;

    /**
     * 投料单DTO
     */
    private List<WsMaterialDTO> wsMaterialDTOList;

    /**
     * 序列号配置编码
     */
    private String serialNumberConfigCode;

    /**
     * 任务ID(完成任务请求参数)
     */
    @Schema(description = "任务ID")
    private String taskId;

    /**
     * 附件列表
     */
    @Schema(description = "附件列表")
    private List<FlowableTaskCompleteDTO.AttachmentInfo> attachmentList;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private Long saleOrderId;

    /**
     * 优先级
     */
    @Schema(description = "优先级", required = true)
    private int priority;

    /**
     * 交付日期
     */
    @Schema(description = "交付日期", required = true)
    private LocalDate deliveryDate;

    /**
     * 动态表单数据id
     */
    @Schema(description = "动态表单数据id",hidden = true)
    private Long formDataId;

    public WorkSheetDTO() {

    }

    public WorkSheetDTO(WorkSheet workSheet) {
        this.serialNumber = workSheet.getSerialNumber();
        this.category = workSheet.getCategory();
        this.pedigree = workSheet.getPedigree();
        this.number = workSheet.getNumber();
        this.bomInfoId = workSheet.getBomInfoId();
        this.bomInfoDto = workSheet.getBomInfoDto();
        this.organizationId = workSheet.getOrganizationId();
        this.organizationDto = workSheet.getOrganizationDto();
        this.planStartDate = workSheet.getPlanStartDate();
        this.workFlow = workSheet.getWorkFlow();
        this.workLine = workSheet.getWorkLine();
        this.clientId = workSheet.getClientId();
        this.clientDTO = workSheet.getClientDTO();
        this.note = workSheet.getNote();

    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public void setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public WorkSheetDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public Boolean getIsAutoGenerateSubWs() {
        return isAutoGenerateSubWs;
    }

    public WorkSheetDTO setIsAutoGenerateSubWs(Boolean isAutoGenerateSubWs) {
        this.isAutoGenerateSubWs = isAutoGenerateSubWs;
        return this;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public void setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
    }

    public BomInfoDTO getBomInfoDto() {
        return bomInfoDto;
    }

    public void setBomInfoDto(BomInfoDTO bomInfoDto) {
        this.bomInfoDto = bomInfoDto;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public void setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public void setOrganizationDto(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
    }


    public List<StepDTO> getStepDtoList() {
        return stepDtoList;
    }

    public void setStepDtoList(List<StepDTO> stepDtoList) {
        this.stepDtoList = stepDtoList;
    }

    public List<WsMaterialDTO> getWsMaterialDTOList() {
        return wsMaterialDTOList;
    }

    public WorkSheetDTO setWsMaterialDTOList(List<WsMaterialDTO> wsMaterialDTOList) {
        this.wsMaterialDTOList = wsMaterialDTOList;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public WorkSheetDTO setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDTO() {
        return clientDTO;
    }

    public WorkSheetDTO setClientDTO(ClientDTO clientDTO) {
        this.clientDTO = clientDTO;
        return this;
    }

    public Long getBomInfoId() {
        return bomInfoId;
    }

    public void setBomInfoId(Long bomInfoId) {
        this.bomInfoId = bomInfoId;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public void setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
    }

    public String getSerialNumberConfigCode() {
        return serialNumberConfigCode;
    }

    public WorkSheetDTO setSerialNumberConfigCode(String serialNumberConfigCode) {
        this.serialNumberConfigCode = serialNumberConfigCode;
        return this;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public List<FlowableTaskCompleteDTO.AttachmentInfo> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<FlowableTaskCompleteDTO.AttachmentInfo> attachmentList) {
        this.attachmentList = attachmentList;
    }

    public Long getSaleOrderId() {
        return saleOrderId;
    }

    public WorkSheetDTO setSaleOrderId(Long saleOrderId) {
        this.saleOrderId = saleOrderId;
        return this;
    }

    public int getPriority() {
        return priority;
    }

    public WorkSheetDTO setPriority(int priority) {
        this.priority = priority;
        return this;
    }

    public LocalDate getDeliveryDate() {
        return deliveryDate;
    }

    public WorkSheetDTO setDeliveryDate(LocalDate deliveryDate) {
        this.deliveryDate = deliveryDate;
        return this;
    }

    public WorkSheetDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public WorkSheetDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getFormDataId() {
        return formDataId;
    }

    public WorkSheetDTO setFormDataId(Long formDataId) {
        this.formDataId = formDataId;
        return this;
    }
}
