package net.airuima.rbase.dto.client;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;

import java.util.List;

public class ClientWsMaterialDTO extends BaseClientDTO {


    private List<WsMaterial> wsMaterialList;


    public List<WsMaterial> getWsMaterialList() {
        return wsMaterialList;
    }

    public ClientWsMaterialDTO setWsMaterialList(List<WsMaterial> wsMaterialList) {
        this.wsMaterialList = wsMaterialList;
        return this;
    }

    public ClientWsMaterialDTO(String status, List<WsMaterial> wsMaterialList) {
        super(status);
        this.wsMaterialList = wsMaterialList;
    }

    public ClientWsMaterialDTO(String status,String message) {
        super(status,message);
    }
}
