package net.airuima.rbase.dto.control;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.dto.message.MessageGroupConfigDTO;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rule服务中事件配置DTO
 * <AUTHOR>
 * @date 2023/2/14
 */
@Schema(description = "事件配置DTO")
public class EventConfigDTO extends AbstractDto implements Serializable {

    /**
     * 事件名称
     */
    @Schema(description = "事件名称", required = true)
    private String name;

    /**
     * 事件编码
     */
    @Schema(description = "事件编码", required = true)
    private String code;

    /**
     * 流程定义ID
     */
    @Schema(description = "流程定义ID")
    private String processDefinitionId;


    /**
     * 消息组绑定的消息配置 (一个消息组可以绑定多个消息)
     */
    private MessageGroupConfigDTO messageGroupConfigDto;

    public String getName() {
        return name;
    }

    public EventConfigDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public EventConfigDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public EventConfigDTO setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
        return this;
    }

    public MessageGroupConfigDTO getMessageGroupConfigDto() {
        return messageGroupConfigDto;
    }

    public EventConfigDTO setMessageGroupConfigDto(MessageGroupConfigDTO messageGroupConfigDto) {
        this.messageGroupConfigDto = messageGroupConfigDto;
        return this;
    }
}
