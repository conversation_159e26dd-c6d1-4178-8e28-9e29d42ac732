package net.airuima.rbase.dto.flowable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-02-26
 */
@Schema(description = "流程定义信息DTO")
public class ProcessDefinitionDTO {
    /**
     * 流程定义ID
     */
    @Schema(description = "流程定义ID")
    private String processDefinitionId;

    /**
     * 流程定义KEY
     */
    @Schema(description = "流程定义KEY")
    private String processDefinitionKey;

    /**
     * 流程定义名称
     */
    @Schema(description = "流程定义名称")
    private String processDefinitionName;

    /**
     * 流程定义版本
     */
    @Schema(description = "流程定义版本")
    private Integer processDefinitionVersion;


    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String category;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean isActive;

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public ProcessDefinitionDTO setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
        return this;
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public ProcessDefinitionDTO setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
        return this;
    }

    public String getProcessDefinitionName() {
        return processDefinitionName;
    }

    public ProcessDefinitionDTO setProcessDefinitionName(String processDefinitionName) {
        this.processDefinitionName = processDefinitionName;
        return this;
    }

    public Integer getProcessDefinitionVersion() {
        return processDefinitionVersion;
    }

    public ProcessDefinitionDTO setProcessDefinitionVersion(Integer processDefinitionVersion) {
        this.processDefinitionVersion = processDefinitionVersion;
        return this;
    }

    public String getCategory() {
        return category;
    }

    public ProcessDefinitionDTO setCategory(String category) {
        this.category = category;
        return this;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public ProcessDefinitionDTO setIsActive(Boolean active) {
        isActive = active;
        return this;
    }
}
