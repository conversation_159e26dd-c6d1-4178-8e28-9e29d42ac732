package net.airuima.rbase.dto.ocmes;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/19
 */
@Schema(description = "替换容器dto")
public class OcReplaceContainerDTO {

    /**
     *  工单
     */
    @Schema(description = "工单")
    private WorkSheet workSheet;

    /**
     *  子工单
     */
    @Schema(description = "子工单")
    private SubWorkSheet subWorkSheet;


    /**
     *  工序
     */
    @Schema(description = "工序")
    private Step step;

    /**
     *  原始容器
     */
    @Schema(description = "原始容器")
    private String originalContainer;

    /**
     *  替换容器
     */
    @Schema(description = "替换容器")
    private String replaceContainer;

    public OcReplaceContainerDTO(SubWorkSheet subWorkSheet, Step step, String originalContainer, String replaceContainer) {
        this.subWorkSheet = subWorkSheet;
        this.step = step;
        this.originalContainer = originalContainer;
        this.replaceContainer = replaceContainer;
    }

    public OcReplaceContainerDTO(WorkSheet workSheet, Step step, String originalContainer, String replaceContainer) {
        this.workSheet = workSheet;
        this.step = step;
        this.originalContainer = originalContainer;
        this.replaceContainer = replaceContainer;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public OcReplaceContainerDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public OcReplaceContainerDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public OcReplaceContainerDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public String getOriginalContainer() {
        return originalContainer;
    }

    public OcReplaceContainerDTO setOriginalContainer(String originalContainer) {
        this.originalContainer = originalContainer;
        return this;
    }

    public String getReplaceContainer() {
        return replaceContainer;
    }

    public OcReplaceContainerDTO setReplaceContainer(String replaceContainer) {
        this.replaceContainer = replaceContainer;
        return this;
    }
}
