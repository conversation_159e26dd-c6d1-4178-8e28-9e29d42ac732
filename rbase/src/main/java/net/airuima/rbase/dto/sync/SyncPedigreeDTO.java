package net.airuima.rbase.dto.sync;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/29
 */
@Schema(description = "产品谱系同步")
public class SyncPedigreeDTO implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 产品编码
     */
    @Schema(description = "产品名称")
    private String name;

    /**
     * 产品编码
     */
    @Schema(description = "产品编码")
    private String code;


    /**
     * 父级产品编码
     */
    @Schema(description = "父级产品编码")
    private String parentCode;

    /**
     * 只有产品型号 才填入对应的物料编码
     */
    @Schema(description = "只有产品型号 才填入对应的物料编码")
    private String materialCode;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    private String specification;

    /**
     * 同步类型(0:新增;1:修改;2:删除;3:禁用;4:启用)
     */
    @Schema(description = "同步类型(0:新增;1:修改;2:删除;3:禁用;4:启用)")
    private Integer operate;

    public Long getId() {
        return id;
    }

    public SyncPedigreeDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public SyncPedigreeDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public SyncPedigreeDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getParentCode() {
        return parentCode;
    }

    public SyncPedigreeDTO setParentCode(String parentCode) {
        this.parentCode = parentCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public SyncPedigreeDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public Integer getOperate() {
        return operate;
    }

    public SyncPedigreeDTO setOperate(Integer operate) {
        this.operate = operate;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public SyncPedigreeDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }
}
