package net.airuima.rbase.dto.rworker.process.dto.general;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.rfms.FacilityDTO;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工序生产设备信息DTO
 * <AUTHOR>
 * @date 2023/4/6
 */
@Schema(description = "工序生产设备信息")
public class FacilityGetInfo implements Serializable {

    /**
     * 设备ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "设备ID")
    private Long id;

    /**
     * 设备编码
     */
    @Schema(description = "设备编码")
    private String code;

    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    private String name;

    public FacilityGetInfo() {

    }

    public FacilityGetInfo(FacilityDTO facilityDTO) {
        this.id = facilityDTO.getId();
        this.code = facilityDTO.getCode();
        this.name = facilityDTO.getName();
    }

    public Long getId() {
        return id;
    }

    public FacilityGetInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCode() {
        return code;
    }

    public FacilityGetInfo setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public FacilityGetInfo setName(String name) {
        this.name = name;
        return this;
    }
}
