package net.airuima.rbase.dto.bom;

import net.airuima.dto.AbstractDto;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020/12/15
 */
public class BomDTO extends AbstractDto {
    /**
     * 是否倒冲物料
     */
    private Boolean backFlush;

    /**
     * 用量分子
     */
    private Double proportion;

    /**
     * 用量分母
     */
    private Double base;

    /**
     * 损耗率
     */
    private Double wastage;

    /**
     * 用料类型
     */
    private int category;

    /**
     * BOM信息
     */
    private BomInfoDTO bomInfo;

    /**
     * 物料
     * 和bomInfo中的物料id一致
     */
    private MaterialDTO material;

    /**
     * 子物料
     */
    private MaterialDTO childMaterial;

    /**
     * 替换料列表
     */
    private List<BomMaterialReplaceDTO> bomMaterialReplaceDtoList;

    public Double getProportion() {
        return proportion;
    }

    public BomDTO setProportion(Double proportion) {
        this.proportion = proportion;
        return this;
    }

    public Double getBase() {
        return base;
    }

    public BomDTO setBase(Double base) {
        this.base = base;
        return this;
    }

    public Double getWastage() {
        return wastage;
    }

    public BomDTO setWastage(Double wastage) {
        this.wastage = wastage;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public BomDTO setCategory(int category) {
        this.category = category;
        return this;
    }

    public BomInfoDTO getBomInfo() {
        return bomInfo;
    }

    public BomDTO setBomInfo(BomInfoDTO bomInfo) {
        this.bomInfo = bomInfo;
        return this;
    }

    public MaterialDTO getMaterial() {
        return material;
    }

    public BomDTO setMaterial(MaterialDTO material) {
        this.material = material;
        return this;
    }

    public MaterialDTO getChildMaterial() {
        return childMaterial;
    }

    public BomDTO setChildMaterial(MaterialDTO childMaterial) {
        this.childMaterial = childMaterial;
        return this;
    }

    public List<BomMaterialReplaceDTO> getBomMaterialReplaceDtoList() {
        return bomMaterialReplaceDtoList;
    }

    public BomDTO setBomMaterialReplaceDtoList(List<BomMaterialReplaceDTO> bomMaterialReplaceDtoList) {
        this.bomMaterialReplaceDtoList = bomMaterialReplaceDtoList;
        return this;
    }

    public Boolean getBackFlush() {
        return backFlush;
    }

    public BomDTO setBackFlush(Boolean backFlush) {
        this.backFlush = backFlush;
        return this;
    }
}
