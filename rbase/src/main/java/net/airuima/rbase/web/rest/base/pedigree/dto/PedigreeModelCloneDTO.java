package net.airuima.rbase.web.rest.base.pedigree.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.rbase.domain.base.pedigree.Pedigree;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系模型克隆DTO
 *
 * <AUTHOR>
 * @date 2023/11/23
 */
@Schema(description = "产品谱系模型DTO")
public class PedigreeModelCloneDTO  {


    /**
     * 克隆产品谱系id
     */
    @NotNull
    @Schema(description = "克隆产品谱系id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clonePedigreeId;

    /**
     * 名称
     */
    @NotNull
    @Schema(description = "名称")
    private String name;

    /**
     * 编码
     */
    @NotNull
    @Schema(description = "编码")
    private String code;

    /**
     * 父级id
     */
    @Schema(description = "父级")
    private Pedigree parent;

    /**
     * 当前层级类型
     */
    @NotNull
    private Integer type;

    /**
     * 是否启用(0:不启用;1:启用)
     */
    @Schema(description = "是否启用(0:不启用;1:启用)")
    private Boolean isEnable;

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    private Long materialId;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    private String specification;



    public Long getClonePedigreeId() {
        return clonePedigreeId;
    }

    public PedigreeModelCloneDTO setClonePedigreeId(Long clonePedigreeId) {
        this.clonePedigreeId = clonePedigreeId;
        return this;
    }

    public String getName() {
        return name;
    }

    public PedigreeModelCloneDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public PedigreeModelCloneDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Pedigree getParent() {
        return parent;
    }

    public PedigreeModelCloneDTO setParent(Pedigree parent) {
        this.parent = parent;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public PedigreeModelCloneDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeModelCloneDTO setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public PedigreeModelCloneDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public PedigreeModelCloneDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }
}
