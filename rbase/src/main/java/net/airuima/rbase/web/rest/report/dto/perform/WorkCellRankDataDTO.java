package net.airuima.rbase.web.rest.report.dto.perform;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.procedure.report.StaffPerform;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位报工排行数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/28
 */
@Schema(description = "工位报工排行数据DTO")
public class WorkCellRankDataDTO {

    /**
     * 工位名字
     */
    @Schema(description = "工位名字")
    private String name;

    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    private String code;

    /**
     * 报工数量
     */
    @Schema(description = "报工数量")
    private Long number;

    /**
     * 耗时(分钟)
     */
    @Schema(description = "耗时(分钟)")
    private Double workHour;

    public WorkCellRankDataDTO() {
    }

    public WorkCellRankDataDTO(StaffPerform staffPerform,String name, String code, Long number,Double workHour) {
        this.name = name;
        this.code = code;
        this.number = number;
        this.workHour = workHour;
    }

    public String getName() {
        return name;
    }

    public WorkCellRankDataDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WorkCellRankDataDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public WorkCellRankDataDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Double getWorkHour() {
        return workHour;
    }

    public WorkCellRankDataDTO setWorkHour(Double workHour) {
        this.workHour = workHour;
        return this;
    }

}
