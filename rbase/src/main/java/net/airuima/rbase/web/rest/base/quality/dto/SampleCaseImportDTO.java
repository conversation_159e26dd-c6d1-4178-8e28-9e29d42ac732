package net.airuima.rbase.web.rest.base.quality.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.rbase.constant.SampleCaseCategoryEnum;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 抽样方案导入DTO
 * <AUTHOR>
 * @date 2023-04-26
 */
@Schema(description = "抽样方案导入DTO")
public class SampleCaseImportDTO implements Serializable {

    /**
     * 抽样方案编码
     */
    @NotNull
    @Schema(description = "抽样方案编码", required = true)
    @Excel(name = "抽样方案编码")
    private String code;

    /**
     * 抽样方案名称
     */
    @NotNull
    @Schema(description = "抽样方案名称", required = true)
    @Excel(name = "抽样方案名称")
    private String name;

    /**
     * 抽样类型:全检0/固定数量1/按百分比抽样2/按国标抽样3
     */
    @NotNull
    @Schema(description = "抽样类型:全检0/固定数量1/按百分比抽样2/按国标抽样3", required = true)
    @Excel(name = "抽样类型")
    private String categoryName;

    /**
     * 有效期
     */
    @NotNull
    @Schema(description = "有效期", required = true)
    @Excel(name = "有效期",importFormat = "yyyy-MM-dd", exportFormat = "yyyy-MM-dd")
    private LocalDate expiryDate;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @NotNull
    @Schema(description = "是否启用(false:禁用;true:启用)", required = true)
    @Excel(name = "是否启用", replace = {"禁用_false", "启用_true"})
    private  Boolean isEnable;;

    /**
     * 抽样数量,类型为固定数量时必输入
     */
    @Schema(description = "抽样数量,类型为固定数量时必输入")
    @Excel(name = "抽样数量")
    private Integer number;

    /**
     * 允收数ac,类型为固定数量时必输入
     */
    @Schema(description = "允收数ac,类型为固定数量时必输入")
    @Excel(name = "允收数ac")
    private Integer ac;

    /**
     * 抽样百分比,类型为百分比抽样时必输入*
     */
    @Schema(description = "抽样百分比,类型为百分比抽样时必输入")
    @Excel(name = "抽样百分比")
    private Double rate;

    /**
     * 合格百分比,类型为百分比抽样时必输入*
     */
    @Schema(description = "合格百分比,类型为百分比抽样时必输入", required = true)
    @Excel(name = "合格百分比")
    private Double qualifiedRate;

    /**
     * 国标类型编码,类型为国标抽样时比输入
     */
    @Schema(description = "国标类型编码,类型为国标抽样时比输入")
    @Excel(name = "国标类型编码")
    private String gbtCode;

    /**
     * 检验水平,类型为国标抽样时比输入
     */
    @Schema(description = "检验水平,类型为国标抽样时比输入")
    @Excel(name = "检验水平")
    private String insolationLevel;

    /**
     * 接收质量限AQL,类型为国标抽样时比输入*
     */
    @Schema(description = "接收质量限AQL,类型为国标抽样时比输入")
    @Excel(name = "接收质量限AQL")
    private Double aql;

    public SampleCaseImportDTO() {
    }

    public SampleCaseImportDTO(SampleCaseDTO sampleCase) {
        this.code = sampleCase.getCode();
        this.name = sampleCase.getName();
        this.categoryName = SampleCaseCategoryEnum.getCategoryName(sampleCase.getCategory());
        this.expiryDate = sampleCase.getExpiryDate();
        this.isEnable = sampleCase.isEnable();
        this.number = sampleCase.getNumber();
        this.ac = sampleCase.getAc();
        this.rate = sampleCase.getRate();
        this.qualifiedRate = sampleCase.getQualifiedRate();
        this.gbtCode = !ObjectUtils.isEmpty(sampleCase.getGbt())?sampleCase.getGbt().getCode():null;
        this.insolationLevel = sampleCase.getInsolationLevel();
        this.aql = sampleCase.getAql();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getAc() {
        return ac;
    }

    public void setAc(Integer ac) {
        this.ac = ac;
    }

    public Double getRate() {
        return rate;
    }

    public void setRate(Double rate) {
        this.rate = rate;
    }

    public Double getQualifiedRate() {
        return qualifiedRate;
    }

    public void setQualifiedRate(Double qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
    }

    public String getGbtCode() {
        return gbtCode;
    }

    public void setGbtCode(String gbtCode) {
        this.gbtCode = gbtCode;
    }

    public String getInsolationLevel() {
        return insolationLevel;
    }

    public void setInsolationLevel(String insolationLevel) {
        this.insolationLevel = insolationLevel;
    }

    public Double getAql() {
        return aql;
    }

    public void setAql(Double aql) {
        this.aql = aql;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SampleCaseImportDTO that = (SampleCaseImportDTO) o;
        return Objects.equals(code, that.code);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code);
    }
}
