package net.airuima.rbase.web.rest.base.pedigree;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.constant.Constants;
import net.airuima.dto.ExportParamDTO;
import net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.service.base.pedigree.PedigreeWorkFlowService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系流程框图Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "产品谱系正常工艺路线Resource")
@RestController
@RequestMapping("/api/pedigree-work-flows")
@AuthorityRegion("工艺模型")
public class PedigreeWorkFlowResource extends ProtectBaseResource<PedigreeWorkFlow> {
    private static final Logger log = LoggerFactory.getLogger(PedigreeWorkFlowResource.class);

    private static final String MODULE = "产品谱系正常/返修工艺路线";

    private static final String ERROR = "error";

    private final PedigreeWorkFlowService pedigreeWorkFlowService;

    public PedigreeWorkFlowResource(PedigreeWorkFlowService pedigreeWorkFlowService) {
        this.pedigreeWorkFlowService = pedigreeWorkFlowService;
        this.mapUri = "/api/pedigree-work-flows";
    }


    /**
     * 重写新增函数
     *
     * @param entity 产品谱系正常工艺路线
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.pedigree.PedigreeWorkFlow> 产品谱系正常工艺路线
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<PedigreeWorkFlow> create(@Valid @RequestBody PedigreeWorkFlow entity) throws URISyntaxException {
        BaseResultDTO<PedigreeWorkFlow> baseResultDto = pedigreeWorkFlowService.saveInstance(entity);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(baseResultDto.getKey(), baseResultDto.getMessage())).build();
        } else {
            return (ResponseEntity.created(new URI(this.mapUri + "/" + baseResultDto.getData().getId())).headers(HeaderUtil.createdAlert(this.entityName, baseResultDto.getData().getId().toString()))).body(baseResultDto.getData());
        }
    }

    /**
     * 重写更新函数，
     *
     * @param entity 产品谱系正常工艺路线
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.pedigree.PedigreeWorkFlow> 产品谱系正常工艺路线
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<PedigreeWorkFlow> update(@Valid @RequestBody PedigreeWorkFlow entity) throws URISyntaxException {
        BaseResultDTO<PedigreeWorkFlow> baseResultDto = pedigreeWorkFlowService.saveInstance(entity);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(baseResultDto.getKey(), baseResultDto.getMessage())).build();
        } else {
            return ((ResponseEntity.BodyBuilder) ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, entity.getId().toString()))).body(baseResultDto.getData());
        }
    }


    /**
     * 下单根据产品谱系和客户id获取启用的流程框图列表
     *
     * @param pedigreeId 谱系ID
     * @param clientId   客户id
     * @param category 工艺路线类型
     * @return java.util.List<net.airuima.domain.base.process.WorkFlow>  工艺路线列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "下单根据产品谱系ID和客户id获取启用的的工艺路线列表")
    @GetMapping("/byPedigreeIdAndClientId")
    public ResponseEntity<ResponseData<List<WorkFlow>>> findByPedigreeIdAndClientId(@RequestParam("pedigreeId") Long pedigreeId, @RequestParam(value = "clientId", required = false) Long clientId , @RequestParam(value = "category",required = false) Integer category) {
        return ResponseData.ok(pedigreeWorkFlowService.findByPedigreeIdAndClientId(pedigreeId, clientId,category));
    }

    /**
     * 通过谱系ID和工艺路线ID逻辑删除绑定关系
     *
     * @param pedigreeId 谱系ID
     * @param workFlowId 工艺路线ID
     * @return ResponseEntity<ResponseData<Void>>
     * <AUTHOR>
     * @date 2021-05-10
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过产品谱系ID和工艺路线ID删除关联关系")
    @DeleteMapping("/deleteByPedigreeIdAndWorkFlowId")
    public ResponseEntity<ResponseData<Void>> deleteByPedigreeIdAndWorkFlowId(@RequestParam("pedigreeId") Long pedigreeId, @RequestParam("workFlowId") Long workFlowId, @RequestParam(value = "clientId", required = false) Long clientId) {
        try {
            pedigreeWorkFlowService.deleteByPedigreeIdAndWorkFlowId(pedigreeId, workFlowId, clientId);
            return ResponseData.delete();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 工艺spa页面 获取产品谱系绑定流程框图列表
     *
     * @param pedigreeId 产品谱系
     * @return 流程框图列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过产品谱系ID获取工艺路线列表")
    @GetMapping("/byPedigreeId")
    public ResponseEntity<ResponseData<List<WorkFlow>>> findWorkFLowByPedigree(@RequestParam("pedigreeId") Long pedigreeId,
                                                                               @RequestParam(value = "keyword", required = false) String keyword,
                                                                               @RequestParam(value = "category", required = false) Integer category,
                                                                               @RequestParam(value = "clientId",required = false) Long clientId,
                                                                               @RequestParam(value = "isEnable", required = false) Boolean isEnable) {
        return ResponseData.ok(pedigreeWorkFlowService.findByPedigreeAndKeyword(pedigreeId,clientId, keyword, category, isEnable));
    }

    /**
     * 启用/禁用指定产品谱系正常工艺路线
     *
     * @param pedigreeWorkFlowId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "启用/禁用指定产品谱系正常工艺路线")
    @Parameters({
            @Parameter(name = "pedigreeWorkFlowId", description = "产品谱系正常工艺路线ID", required = true)
    })
    @PutMapping("/pedigreeWorkFlowId/{pedigreeWorkFlowId}")
    public ResponseEntity<ResponseData<Void>> enableByStepId(@PathVariable("pedigreeWorkFlowId") Long pedigreeWorkFlowId) {
        try {
            pedigreeWorkFlowService.enableByPedigreeWorkFlowId(pedigreeWorkFlowId);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return ResponseData.error(e);
        }
    }


    /**
     * 通过产品谱系ID获取当前层级的工艺路线列表(正常、返工、返修)
     * @param pedigreeId 产品谱系
     * @return List<WorkFlow>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过产品谱系ID获取当前层级的工艺路线列表(正常、返工、返修)")
    @GetMapping("/work-flow/pedigree/{pedigreeId}")
    public ResponseEntity<ResponseData<List<WorkFlow>>> allOnlyPedigreeWorkFlow(@PathVariable("pedigreeId") Long pedigreeId){
        return ResponseData.ok(pedigreeWorkFlowService.findByPedigreeId(pedigreeId));
    }

    /**
     * 产品谱系工艺路线导入
     *
     * @param file 产品谱系工艺路线导入
     * @return org.springframework.http.ResponseEntity<java.lang.Void>通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "产品谱系工艺路线导入")
    @Override
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "data", required = false) String data, @RequestParam(value = "suffix", required = false) String suffix, @RequestParam(value = "metaColumn", required = false) String metaColumn, HttpServletResponse response) throws Exception {
        this.prepareImportParams();
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = pedigreeWorkFlowService.importPedigreeWorkFlowExcel(file);
        //获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> org.apache.commons.lang3.StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(org.apache.commons.lang3.StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
