package net.airuima.rbase.web.rest.procedure.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/5/6
 */
@Schema(description = "检测历史处理结果DTO")
public class InspectionResultDTO {

    /**
     *检测结果id
     */
    @Schema(description = "检测结果id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long checkHistoryId;

    /**
     * 处理方式：待处理0/通过1/重检2/放行3
     */
    @Schema(description = "处理方式：待处理0/通过1/重检2/放行3")
    private Integer dealWay;


    /**
     * MRB申请原因
     */
    @Schema(description = "MRB申请原因")
    private String reason;

    public Long getCheckHistoryId() {
        return checkHistoryId;
    }

    public InspectionResultDTO setCheckHistoryId(Long checkHistoryId) {
        this.checkHistoryId = checkHistoryId;
        return this;
    }

    public Integer getDealWay() {
        return dealWay;
    }

    public InspectionResultDTO setDealWay(Integer dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public InspectionResultDTO setReason(String reason) {
        this.reason = reason;
        return this;
    }
}
