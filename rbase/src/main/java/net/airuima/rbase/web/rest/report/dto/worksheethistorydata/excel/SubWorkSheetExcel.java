package net.airuima.rbase.web.rest.report.dto.worksheethistorydata.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class SubWorkSheetExcel {

    /**
     * 工单号
     */
    @Excel(name = "工单号")
    private String serialNumber;

    /**
     * 工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单，6：收货中)
     */
    @Excel(name = "工单状态", replace = {"已取消_-2", "审批中_-1", "已下单_0", "投产中_1", "已暂停_2", "已完成_3", "正常结单_4", "异常结单_5"})
    private int status;

    /**
     * 工单进度
     */
    @Excel(name = "工单进度")
    private String progress;

    /**
     * 产品谱系
     */
    @Excel(name = "产品谱系")
    private String pedigreeNameCode;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    private String specification;

    /**
     * 产线
     */
    @Excel(name = "产线")
    private String workLineNameCode;

    /**
     * 工艺路线
     */
    @Excel(name = "工艺路线")
    private String workFlowNameCode;


    /**
     * 物料清单
     */
    @Excel(name = "物料清单")
    private String bomInfoNameCode;

    /**
     * 计划开工日期
     */
    @Excel(name = "计划开工日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Excel(name = "计划结单日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planEndDate;

    /**
     * 实际开工日期
     */
    @Excel(name = "实际开工日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualStartDate;

    /**
     * 实际完成日期
     */
    @Excel(name = "实际完成日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualEndDate;

    /**
     * 投产数
     */
    @Excel(name = "投产数")
    private Integer number;

    /**
     * 合格数
     */
    @Excel(name = "合格数")
    private Integer qualifiedNumber;

    /**
     * 工序履历信息
     */
    @ExcelCollection(name = "工序履历信息")
    private List<StepHistoryExcel> stepHistoryExcelList;

    public static class StepHistoryExcel {

        /**
         * 工序信息
         */
        @Excel(name = "工序信息")
        private String stepNameCode;

        /**
         * 是否完成(1:完成;0:未完成)
         */
        @Excel(name = "工单状态", replace = {"未完成_0", "完成_1"})
        private Integer finish;

        /**
         * 时长
         */
        @Excel(name = "时长")
        private Double duration;

        /**
         * 投产数
         */
        @Excel(name = "投产数")
        private Integer number;

        /**
         * 合格数
         */
        @Excel(name = "合格数")
        private Integer qualifiedNumber;

        /**
         * 人员信息
         */
        @Excel(name = "人员信息")
        private String staffNameCode;

        /**
         * 工位信息
         */
        @Excel(name = "工位信息")
        private String workCellNameCode;

        /**
         * 设备履历
         */
        @ExcelCollection(name = "设备履历")
        private List<FacilityHistoryExcel> facilityHistoryExcelList;

        /**
         * 物料履历
         */
        @ExcelCollection(name = "物料履历")
        private List<MaterialHistoryExcel> materialHistoryExcelList;

        /**
         * 动态数据履历
         */
        @ExcelCollection(name = "动态数据履历")
        private List<DynamicDataHistoryExcel> dynamicDataHistoryExcelList;

        /**
         * 不良履历
         */
        @ExcelCollection(name = "不良履历")
        private List<UnqualifiedItemHistoryExcel> unqualifiedItemHistoryExcelList;

        public List<FacilityHistoryExcel> getFacilityHistoryExcelList() {
            return facilityHistoryExcelList;
        }

        public StepHistoryExcel setFacilityHistoryExcelList(List<FacilityHistoryExcel> facilityHistoryExcelList) {
            this.facilityHistoryExcelList = facilityHistoryExcelList;
            return this;
        }

        public String getStepNameCode() {
            return stepNameCode;
        }

        public StepHistoryExcel setStepNameCode(String stepNameCode) {
            this.stepNameCode = stepNameCode;
            return this;
        }

        public Integer getFinish() {
            return finish;
        }

        public StepHistoryExcel setFinish(Integer finish) {
            this.finish = finish;
            return this;
        }

        public Double getDuration() {
            return duration;
        }

        public StepHistoryExcel setDuration(Double duration) {
            this.duration = duration;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public StepHistoryExcel setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public StepHistoryExcel setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public String getStaffNameCode() {
            return staffNameCode;
        }

        public StepHistoryExcel setStaffNameCode(String staffNameCode) {
            this.staffNameCode = staffNameCode;
            return this;
        }

        public String getWorkCellNameCode() {
            return workCellNameCode;
        }

        public StepHistoryExcel setWorkCellNameCode(String workCellNameCode) {
            this.workCellNameCode = workCellNameCode;
            return this;
        }

        public List<MaterialHistoryExcel> getMaterialHistoryExcelList() {
            return materialHistoryExcelList;
        }

        public StepHistoryExcel setMaterialHistoryExcelList(List<MaterialHistoryExcel> materialHistoryExcelList) {
            this.materialHistoryExcelList = materialHistoryExcelList;
            return this;
        }

        public List<DynamicDataHistoryExcel> getDynamicDataHistoryExcelList() {
            return dynamicDataHistoryExcelList;
        }

        public StepHistoryExcel setDynamicDataHistoryExcelList(List<DynamicDataHistoryExcel> dynamicDataHistoryExcelList) {
            this.dynamicDataHistoryExcelList = dynamicDataHistoryExcelList;
            return this;
        }

        public List<UnqualifiedItemHistoryExcel> getUnqualifiedItemHistoryExcelList() {
            return unqualifiedItemHistoryExcelList;
        }

        public StepHistoryExcel setUnqualifiedItemHistoryExcelList(List<UnqualifiedItemHistoryExcel> unqualifiedItemHistoryExcelList) {
            this.unqualifiedItemHistoryExcelList = unqualifiedItemHistoryExcelList;
            return this;
        }
    }

    public static class MaterialHistoryExcel {

        /**
         * 物料信息
         */
        @Excel(name = "物料信息")
        private String materialNameCode;

        /**
         * 批次/序列号
         */
        @Excel(name = "批次/序列号")
        private String batch;

        /**
         * 数量
         */
        @Excel(name = "数量")
        private Double number;

        public String getMaterialNameCode() {
            return materialNameCode;
        }

        public MaterialHistoryExcel setMaterialNameCode(String materialNameCode) {
            this.materialNameCode = materialNameCode;
            return this;
        }

        public String getBatch() {
            return batch;
        }

        public MaterialHistoryExcel setBatch(String batch) {
            this.batch = batch;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public MaterialHistoryExcel setNumber(Double number) {
            this.number = number;
            return this;
        }
    }

    public static class DynamicDataHistoryExcel {

        /**
         * 动态数据信息
         */
        @Excel(name = "动态数据信息")
        private String columnNameCode;

        /**
         * 动态数据明细
         */
        @Excel(name = "动态数据明细")
        private String value;

        public String getColumnNameCode() {
            return columnNameCode;
        }

        public DynamicDataHistoryExcel setColumnNameCode(String columnNameCode) {
            this.columnNameCode = columnNameCode;
            return this;
        }

        public String getValue() {
            return value;
        }

        public DynamicDataHistoryExcel setValue(String value) {
            this.value = value;
            return this;
        }
    }

    public static class UnqualifiedItemHistoryExcel {

        /**
         * 不良信息
         */
        @Excel(name = "不良信息")
        private String unqualifiedItemNameCode;

        /**
         * 不良数量
         */
        @Excel(name = "不良数量")
        private Integer number;

        public String getUnqualifiedItemNameCode() {
            return unqualifiedItemNameCode;
        }

        public UnqualifiedItemHistoryExcel setUnqualifiedItemNameCode(String unqualifiedItemNameCode) {
            this.unqualifiedItemNameCode = unqualifiedItemNameCode;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public UnqualifiedItemHistoryExcel setNumber(Integer number) {
            this.number = number;
            return this;
        }
    }

    public static class FacilityHistoryExcel {

        /**
         * 设备信息
         */
        @Excel(name = "设备信息")
        private String facilityNameCode;

        public String getFacilityNameCode() {
            return facilityNameCode;
        }

        public FacilityHistoryExcel setFacilityNameCode(String facilityNameCode) {
            this.facilityNameCode = facilityNameCode;
            return this;
        }
    }


    public String getSerialNumber() {
        return serialNumber;
    }

    public SubWorkSheetExcel setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public SubWorkSheetExcel setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getProgress() {
        return progress;
    }

    public SubWorkSheetExcel setProgress(String progress) {
        this.progress = progress;
        return this;
    }

    public String getPedigreeNameCode() {
        return pedigreeNameCode;
    }

    public SubWorkSheetExcel setPedigreeNameCode(String pedigreeNameCode) {
        this.pedigreeNameCode = pedigreeNameCode;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public SubWorkSheetExcel setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getWorkLineNameCode() {
        return workLineNameCode;
    }

    public SubWorkSheetExcel setWorkLineNameCode(String workLineNameCode) {
        this.workLineNameCode = workLineNameCode;
        return this;
    }

    public String getWorkFlowNameCode() {
        return workFlowNameCode;
    }

    public SubWorkSheetExcel setWorkFlowNameCode(String workFlowNameCode) {
        this.workFlowNameCode = workFlowNameCode;
        return this;
    }

    public String getBomInfoNameCode() {
        return bomInfoNameCode;
    }

    public SubWorkSheetExcel setBomInfoNameCode(String bomInfoNameCode) {
        this.bomInfoNameCode = bomInfoNameCode;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public SubWorkSheetExcel setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public SubWorkSheetExcel setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public LocalDateTime getActualStartDate() {
        return actualStartDate;
    }

    public SubWorkSheetExcel setActualStartDate(LocalDateTime actualStartDate) {
        this.actualStartDate = actualStartDate;
        return this;
    }

    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public SubWorkSheetExcel setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public SubWorkSheetExcel setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public SubWorkSheetExcel setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public List<StepHistoryExcel> getStepHistoryExcelList() {
        return stepHistoryExcelList;
    }

    public SubWorkSheetExcel setStepHistoryExcelList(List<StepHistoryExcel> stepHistoryExcelList) {
        this.stepHistoryExcelList = stepHistoryExcelList;
        return this;
    }
}
