package net.airuima.rbase.web.rest.report;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.service.report.WorkSheetHistoryDataReportService;
import net.airuima.rbase.web.rest.report.dto.worksheethistorydata.*;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工单履历报表
 *
 * <AUTHOR>
 * @version 1.8.0
 * @since 1.8.0
 */
@Tag(name = "工单履历报表Resource")
@RestController
@RequestMapping("/api/report/work-sheet-history-data-report")
@AuthorityRegion("报表看板")
public class WorkSheetHistoryDataReportResource {

    @Autowired
    private WorkSheetHistoryDataReportService workSheetHistoryDataReportService;

    /**
     * 获取订单列表
     *
     * @param orderListRequestDto 订单列表请求DTO
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取订单列表")
    @PostMapping("/get-order-list")
    public ResponseEntity<ResponseData<OrderListDTO>> getOrderList(@RequestBody OrderListRequestDTO orderListRequestDto) {
        return ResponseData.ok(workSheetHistoryDataReportService.getOrderList(orderListRequestDto));
    }

    /**
     * 获取销售订单履历
     *
     * @param code 销售订单编码
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取销售订单履历")
    @PostMapping("/sale-order-history/{code}")
    public ResponseEntity<ResponseData<SaleOrderHistoryDTO>> getSaleOrderHistory(@PathVariable String code) {
        return ResponseData.ok(workSheetHistoryDataReportService.getSaleOrderHistory(code));
    }

    /**
     * 获取工单履历
     *
     * @param code 工单编码
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取工单履历")
    @PostMapping("/work-sheet-history/{code}")
    public ResponseEntity<ResponseData<WorkSheetHistoryDTO>> getWorkSheetHistory(@PathVariable String code) {
        return ResponseData.ok(workSheetHistoryDataReportService.getWorkSheetHistory(code));
    }

    /**
     * 获取子工单履历
     *
     * @param code 子工单编码
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取子工单履历")
    @PostMapping("/sub-work-sheet-history/{code}")
    public ResponseEntity<ResponseData<SubWorkSheetHistoryDTO>> getSubWorkSheetHistory(@PathVariable String code) {
        return ResponseData.ok(workSheetHistoryDataReportService.getSubWorkSheetHistory(code));
    }

    /**
     * 获取容器履历
     *
     * @param code 容器编码
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取容器履历")
    @PostMapping("/container/{code}")
    public ResponseEntity<ResponseData<ContainerHistoryDTO>> getContainerHistory(@PathVariable String code) {
        return ResponseData.ok(workSheetHistoryDataReportService.getContainerHistory(code));
    }

    /**
     * 获取Sn履历
     *
     * @param code sn编码
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取Sn履历")
    @PostMapping("/sn/{code}")
    public ResponseEntity<ResponseData<SnHistoryDTO>> getSnHistory(@PathVariable String code) {
        return ResponseData.ok(workSheetHistoryDataReportService.getSnHistory(code));
    }

    /**
     * 获取物料履历
     *
     * @param orderListRequestDTO 请求参数
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取物料履历")
    @PostMapping("/material")
    public ResponseEntity<ResponseData<MaterialHistoryDTO>> getMaterialHistory(@RequestBody OrderListRequestDTO orderListRequestDTO) {
        return ResponseData.ok(workSheetHistoryDataReportService.getMaterialHistory(orderListRequestDTO));
    }

    /**
     * 获取设备履历
     *
     * @param code 设备编码
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取设备履历")
    @PostMapping("/facility/{code}")
    public ResponseEntity<ResponseData<FacilityHistoryDTO>> getFacilityHistory(@PathVariable String code) {
        return ResponseData.ok(workSheetHistoryDataReportService.getFacilityHistory(code));
    }

    /**
     * 获取易损件履历
     *
     * @param wearingPartHistoryRequestDTO 易损件履历请求DTO
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取易损件履历")
    @PostMapping("/wearing-part")
    public ResponseEntity<ResponseData<WearingPartHistoryDTO>> getWearingPartHistory(@RequestBody WearingPartHistoryRequestDTO wearingPartHistoryRequestDTO) {
        return ResponseData.ok(workSheetHistoryDataReportService.getWearingPartHistory(wearingPartHistoryRequestDTO));
    }

    /**
     * 获取员工履历
     *
     * @param code 员工编码
     * <AUTHOR>
     * @since 1.8.0
     */
    @Operation(summary = "获取员工履历")
    @PostMapping("/staff/{code}")
    public ResponseEntity<ResponseData<StaffHistoryDTO>> getStaffHistory(@PathVariable String code) {
        return ResponseData.ok(workSheetHistoryDataReportService.getStaffHistory(code));
    }

    /**
     * 工单报表表格导出数据
     */
    @PostMapping("/export")
    public ResponseEntity<ResponseData<Void>> export(@RequestBody List<WorkSheetHistoryExportDTO> workSheetHistoryExportDTOList, HttpServletResponse response) {
        try {
            workSheetHistoryDataReportService.export(workSheetHistoryExportDTOList, response);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
        return ResponseData.ok();
    }

}
