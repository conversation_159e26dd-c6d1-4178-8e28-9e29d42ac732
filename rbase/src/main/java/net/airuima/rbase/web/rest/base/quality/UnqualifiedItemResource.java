package net.airuima.rbase.web.rest.base.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.service.base.quality.UnqualifiedItemService;
import net.airuima.rbase.web.rest.base.quality.dto.UnqualifiedItemCreateDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良项目Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "不良项目Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/unqualified-items")
@AuthorityRegion("质量不良")
@AuthSkip("D")
public class UnqualifiedItemResource extends ProtectBaseResource<UnqualifiedItem> {

    private final Logger log = LoggerFactory.getLogger(UnqualifiedItemResource.class);

    private final UnqualifiedItemService unqualifiedItemService;

    public UnqualifiedItemResource(UnqualifiedItemService unqualifiedItemService) {
        this.unqualifiedItemService = unqualifiedItemService;
        this.mapUri = "/api/unqualified-items";
    }

    /**
     * 通过名称和编码模糊查询启用的不良项目
     * 获取不合格种类下的所有不良现象
     *
     * @param unqualifiedGroupId 不良种类id
     * @return 不良现象列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping("/byUnqualifiedGroupId/{unqualifiedGroupId}")
    @Operation(summary= "获取不合格种类下的所有不良现象")
    public ResponseEntity<ResponseData<List<UnqualifiedItem>>> findByUnqualifiedGroup(@PathVariable("unqualifiedGroupId") Long unqualifiedGroupId) {
        return ResponseData.ok(unqualifiedItemService.findByUqualifiedGroupId(unqualifiedGroupId));
    }

    /**
     * 通过名称和编码模糊查询不良项目
     *
     * @param text 名称或编码
     * @param size 查询数量
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过名称和编码模糊查询不良项目")
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<UnqualifiedItem>>> findByNameOrCode(@RequestParam(value = "text") String text,
                                                                                @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(unqualifiedItemService.findByCodeOrName(text, size));
    }

    /**
     * 通过处理方式、名称和编码模糊查询不良项目
     * @param dealWay
     * @param text
     * @param size
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过处理方式、名称和编码模糊查询不良项目")
    @GetMapping("/byNameOrCode/{dealWay}")
    public ResponseEntity<ResponseData<List<UnqualifiedItem>>> findByNameOrCodeAndDealWay(@PathVariable("dealWay") Integer dealWay, @RequestParam(value = "text") String text,
                                                                                          @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(unqualifiedItemService.findByCodeOrNameAndDealWay(text,dealWay, size));
    }

    /**
     * 新增不良项目
     *
     * @param entity 新增不良项目DTO
     * @return : org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.UnqualifiedItem>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @PostMapping("/custom")
    public ResponseEntity<ResponseData<UnqualifiedItem>> custom(@RequestBody UnqualifiedItemCreateDTO entity) {
        try {
            UnqualifiedItem unqualifiedItem = unqualifiedItemService.create(entity);
            return ResponseData.ok(unqualifiedItem);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 启用/禁用指定不良项目
     *
     * @param unqualifiedItemId 不良项目ID
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "启用/禁用指定不良项目")
    @Parameters({
            @Parameter(name = "unqualifiedItemId", description = "不良项目ID", required = true)
    })
    @PutMapping("/unqualifiedItemId/{unqualifiedItemId}")
    public ResponseEntity<ResponseData<Void>> enableByUnqualifiedItemId(@PathVariable("unqualifiedItemId") Long unqualifiedItemId) {
        try {
            unqualifiedItemService.enableByUnqualifiedItemId(unqualifiedItemId);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(entityName, authority, "不良项目");
    }

}
