package net.airuima.rbase.web.rest.base.quality;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.dto.quality.StepWarningStandardImportDTO;
import net.airuima.rbase.service.base.quality.StepWarningStandardService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 生产工序良率预警标准Resource
 *
 * <AUTHOR>
 * @date 2021-01-20
 */
@Tag(name = "生产工序良率预警标准Resource")
@RestController
@RequestMapping("/api/step-warning-standards")
@AuthorityRegion("质量预警配置")
@FuncInterceptor("QPrewarning")
public class StepWarningStandardResource extends ProtectBaseResource<StepWarningStandard> {
    private static final String MODULE = "良率预警规则";
    private final StepWarningStandardService stepWarningStandardService;

    public StepWarningStandardResource(StepWarningStandardService stepWarningStandardService) {
        this.stepWarningStandardService = stepWarningStandardService;
        this.mapUri = "/api/step-warning-standards";
    }

    /**
     * 逻辑删除预警规则
     * <AUTHOR>
     * @date 2022/10/27
     * @param id 预警规则id
     * @return ResponseEntity<ResponseData<Void>>
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        stepWarningStandardService.deletedById(id);
        return ((ResponseEntity.BodyBuilder)ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, id.toString()))).build();
    }

    /**
     * 新增预警规则
     * <AUTHOR>
     * @date 2022/10/25
     * @param  stepWarningStandard 预警规则DTO
     * @return ResponseEntity<ResponseData<StepWarningStandard>>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping()
    @PreventRepeatSubmit
    @Operation(summary = "新增预警规则")
    @Override
    public ResponseEntity<StepWarningStandard> create(@Valid @RequestBody StepWarningStandard stepWarningStandard) throws URISyntaxException {
        try {
            stepWarningStandardService.saveInstance(stepWarningStandard);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 修改预警规则
     * <AUTHOR>
     * @date 2022/10/25
     * @param stepWarningStandard 预警规则DTO
     * @return ResponseEntity<ResponseData<StepWarningStandard>>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping()
    @PreventRepeatSubmit
    @Operation(summary = "修改预警规则")
    @Override
    public ResponseEntity<StepWarningStandard> update(@Valid @RequestBody StepWarningStandard stepWarningStandard) throws URISyntaxException{
        try {
            stepWarningStandardService.updateInstance(stepWarningStandard);
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 预警规则数据导入
     * <AUTHOR>
     * @date 2022/10/26 18:00
     * @param file 预警规则导入数据
     * @return ResponseEntity<ResponseData<Void>>
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/importExcel"})
    public ResponseEntity<Void> importExcel(@RequestParam("file") MultipartFile file) throws IOException, Exception {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(entityName, "FileEmpty", "File invalid.")).build();
            }
            ImportParams importParams = new ImportParams();
            importParams.setHeadRows(Constants.INT_ONE);
            List<StepWarningStandardImportDTO> stepWarningStandardImportDTOList = ExcelImportUtil.importExcel(file.getInputStream(), StepWarningStandardImportDTO.class, importParams);

            //过滤空行
            stepWarningStandardImportDTOList = stepWarningStandardImportDTOList.stream().filter(i -> null!=i.getBaseNumber() && null != i.getWaringRate() && null != i.getStopRate()).collect(Collectors.toList());
            stepWarningStandardService.importExcel(stepWarningStandardImportDTOList);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(entityName + ".importSuccess", entityName)).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "importFailed", e.toString())).build();
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
