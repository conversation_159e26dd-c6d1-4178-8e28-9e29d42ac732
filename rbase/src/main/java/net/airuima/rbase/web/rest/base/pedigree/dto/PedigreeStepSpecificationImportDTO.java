package net.airuima.rbase.web.rest.base.pedigree.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.rbase.util.ValidateUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Schema(description = "产品谱系工序指标导入DTO")
public class PedigreeStepSpecificationImportDTO implements Serializable {

    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    @Excel(name = "工序名称", orderNum = "1")
    private String stepName;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    @Excel(name = "工序编码", orderNum = "2")
    private String stepCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    @Excel(name = "产品谱系名称",orderNum = "3")
    private String pedigreeName;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码",orderNum = "4")
    private String pedigreeCode;

    /**
     * 工艺路线名称
     */
    @Schema(description = "工艺路线名称")
    @Excel(name = "工艺路线名称", orderNum = "5")
    private String workflowName;

    /**
     * 工艺路线编码
     */
    @Schema(description = "工艺路线编码")
    @Excel(name = "工艺路线编码", orderNum = "6")
    private String workflowCode;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    @Excel(name = "客户名称",orderNum = "7")
    private String clientName;

    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    @Excel(name = "客户编码",orderNum = "8")
    private String clientCode;

    /**
     * 文件编码列表
     */
    @Schema(description = "文件列表")
    @ExcelCollection(name = "文件列表",orderNum = "9")
    private List<ForeignDocumentCodeDTO> foreignDocumentCodeDtos;
    public PedigreeStepSpecificationImportDTO() {

    }

    public PedigreeStepSpecificationImportDTO(PedigreeStepSpecificationImportDTO importDto) {
        this.pedigreeName = importDto.getPedigreeName();
        this.pedigreeCode = importDto.getPedigreeCode();
        this.workflowName = importDto.getWorkflowName();
        this.workflowCode = importDto.getWorkflowCode();
        this.stepName = importDto.getStepName();
        this.stepCode = importDto.getStepCode();
        this.clientName = importDto.getClientName();
        this.clientCode = importDto.getClientCode();
        this.foreignDocumentCodeDtos = ValidateUtils.isValid(importDto.getForeignDocumentCodeDtos())? importDto.getForeignDocumentCodeDtos():null;
    }

    public PedigreeStepSpecificationImportDTO(PedigreeStepSpecification pedigreeStepSpecification) {
        this.pedigreeName = Objects.nonNull(pedigreeStepSpecification.getPedigree()) ? pedigreeStepSpecification.getPedigree().getName() : null;
        this.pedigreeCode = Objects.nonNull(pedigreeStepSpecification.getPedigree())? pedigreeStepSpecification.getPedigree().getCode() : null;
        this.workflowName = Objects.nonNull(pedigreeStepSpecification.getWorkFlow())? pedigreeStepSpecification.getWorkFlow().getName() : null;
        this.workflowCode = Objects.nonNull(pedigreeStepSpecification.getWorkFlow())? pedigreeStepSpecification.getWorkFlow().getCode() : null;
        this.stepName = Objects.nonNull(pedigreeStepSpecification.getStep())? pedigreeStepSpecification.getStep().getName() : null;
        this.stepCode = Objects.nonNull(pedigreeStepSpecification.getStep())? pedigreeStepSpecification.getStep().getCode() : null;
        this.clientName = Objects.nonNull(pedigreeStepSpecification.getClientDto())? pedigreeStepSpecification.getClientDto().getName() : null;
        this.clientCode = Objects.nonNull(pedigreeStepSpecification.getClientDto())? pedigreeStepSpecification.getClientDto().getCode() : null;
        this.foreignDocumentCodeDtos = ValidateUtils.isValid(pedigreeStepSpecification.getForeignDocumentCodeList())?pedigreeStepSpecification.getForeignDocumentCodeList().stream().map(ForeignDocumentCodeDTO::new).collect(Collectors.toList()):null;
    }
    public String getStepName() {
        return stepName;
    }

    public PedigreeStepSpecificationImportDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public PedigreeStepSpecificationImportDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public PedigreeStepSpecificationImportDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public PedigreeStepSpecificationImportDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getWorkflowName() {
        return workflowName;
    }

    public PedigreeStepSpecificationImportDTO setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
        return this;
    }

    public String getWorkflowCode() {
        return workflowCode;
    }

    public PedigreeStepSpecificationImportDTO setWorkflowCode(String workflowCode) {
        this.workflowCode = workflowCode;
        return this;
    }

    public String getClientName() {
        return clientName;
    }

    public PedigreeStepSpecificationImportDTO setClientName(String clientName) {
        this.clientName = clientName;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public PedigreeStepSpecificationImportDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public List<ForeignDocumentCodeDTO> getForeignDocumentCodeDtos() {
        return foreignDocumentCodeDtos;
    }

    public PedigreeStepSpecificationImportDTO setForeignDocumentCodeDtos(List<ForeignDocumentCodeDTO> foreignDocumentCodeDtos) {
        this.foreignDocumentCodeDtos = foreignDocumentCodeDtos;
        return this;
    }


    @Schema(description = "外部文件编码")
    public static class ForeignDocumentCodeDTO{

        @Schema(description = "文件编码")
        @Excel(name = "文件编码")
        private String documentCode;

        // 需要无参构造器
        public ForeignDocumentCodeDTO() {}

        public ForeignDocumentCodeDTO(String documentCode) {
            this.documentCode = documentCode;
        }

        public String getDocumentCode() {
            return documentCode;
        }

        public ForeignDocumentCodeDTO setDocumentCode(String documentCode) {
            this.documentCode = documentCode;
            return this;
        }
    }
}
