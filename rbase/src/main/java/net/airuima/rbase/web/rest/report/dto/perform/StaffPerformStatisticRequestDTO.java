package net.airuima.rbase.web.rest.report.dto.perform;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.report.PageDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 报工统计报表参数DTO
 *
 * <AUTHOR>
 * @date 2023/06/27
 */
@Schema(description = "报工统计报表参数DTO")
public class StaffPerformStatisticRequestDTO extends PageDTO {

    /**
     * 计划完工时间类型
     */
    @Schema(description = "计划完工时间类型 0今天 1本周 2本月")
    private Integer planFinishTimeCategory;

    /**
     * 产品谱系id(最小层级)
     */
    @Schema(description = "产品谱系id(最小层级)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pedigreeId;

    /**
     * 生产线id
     */
    @Schema(description = "产线id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workLineId;

    /**
     * 员工id
     */
    @Schema(description = "员工id")
    private Long staffId;

    /**
     * 下单日期开始范围
     */
    @Schema(description = "下单日期开始范围")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;

    /**
     * 下单日期结束范围
     */
    @Schema(description = "下单日期结束范围")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;

    /**
     * 下单日期结束范围
     */
    @Schema(description = "是否导出 true导出 false不导出")
    private Boolean exportStatus;

    /**
     * 报表类型
     */
    @Schema(description = "报表类型 0 工单 1子工单")
    private Integer reportType;

    /**
     * 排行榜类型
     */
    @Schema(description = "排行榜类型 0员工 1工位 2工序")
    private Integer rankCategory;


    public Integer getPlanFinishTimeCategory() {
        return planFinishTimeCategory;
    }

    public StaffPerformStatisticRequestDTO setPlanFinishTimeCategory(Integer planFinishTimeCategory) {
        this.planFinishTimeCategory = planFinishTimeCategory;
        return this;
    }

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public StaffPerformStatisticRequestDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Long getWorkLineId() {
        return workLineId;
    }

    public StaffPerformStatisticRequestDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public StaffPerformStatisticRequestDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public StaffPerformStatisticRequestDTO setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public StaffPerformStatisticRequestDTO setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public Boolean getExportStatus() {
        return exportStatus;
    }

    public StaffPerformStatisticRequestDTO setExportStatus(Boolean exportStatus) {
        this.exportStatus = exportStatus;
        return this;
    }

    public Integer getReportType() {
        return reportType;
    }

    public StaffPerformStatisticRequestDTO setReportType(Integer reportType) {
        this.reportType = reportType;
        return this;
    }

    public Integer getRankCategory() {
        return rankCategory;
    }

    public StaffPerformStatisticRequestDTO setRankCategory(Integer rankCategory) {
        this.rankCategory = rankCategory;
        return this;
    }
}
