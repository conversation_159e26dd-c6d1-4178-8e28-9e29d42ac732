package net.airuima.rbase.web.rest.base.pedigree;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportParamDTO;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.rbase.service.base.pedigree.PedigreeStepMaterialRuleService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序上料规则Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "产品谱系工序上料规则Resource")
@RestController
@RequestMapping("/api/pedigree-step-material-rules")
@AuthorityRegion("工艺模型")
@FuncInterceptor("WorksheetMaterial && WsMaterialBatch")
public class PedigreeStepMaterialRuleResource extends ProtectBaseResource<PedigreeStepMaterialRule> {

    private static final String MODULE = "工序上料规则";

    private final PedigreeStepMaterialRuleService pedigreeStepMaterialRuleService;

    public PedigreeStepMaterialRuleResource(PedigreeStepMaterialRuleService pedigreeStepMaterialRuleService) {
        this.pedigreeStepMaterialRuleService = pedigreeStepMaterialRuleService;
        this.mapUri = "/api/pedigree-step-material-rules";
    }

    /**
     * 新增产品谱系工序上料规则
     *
     * @param pedigreeStepMaterialRule 产品谱系工序上料规则
     * @return ResponseEntity<ResponseData<StepWarningStandard>>
     * <AUTHOR>
     * @date 2022/11/14
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping()
    @PreventRepeatSubmit
    @Operation(summary = "新增产品谱系工序上料规则")
    @Override
    public ResponseEntity<PedigreeStepMaterialRule> create(@Valid @RequestBody PedigreeStepMaterialRule pedigreeStepMaterialRule) throws URISyntaxException {
        try {
            PedigreeStepMaterialRule entity = pedigreeStepMaterialRuleService.createEntity(pedigreeStepMaterialRule);
            return ResponseEntity.created(new URI(this.mapUri + "/" + entity.getId())).headers(HeaderUtil.createdAlert(this.entityName, entity.getId().toString())).body(entity);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 更新产品谱系工序上料规则
     *
     * @param pedigreeStepMaterialRule 产品谱系工序上料规则
     * @return ResponseEntity<ResponseData<StepWarningStandard>>
     * <AUTHOR>
     * @date 2022/11/14
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping()
    @PreventRepeatSubmit
    @Operation(summary = "更新产品谱系工序上料规则")
    @Override
    public ResponseEntity<PedigreeStepMaterialRule> update(@Valid @RequestBody PedigreeStepMaterialRule pedigreeStepMaterialRule) throws URISyntaxException {
        try {
            PedigreeStepMaterialRule entity = pedigreeStepMaterialRuleService.updateEntity(pedigreeStepMaterialRule);
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, entity.getId().toString())).body(entity);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 批量更新产品谱系工序上料规则
     * @param pedigreeStepMaterialRules 参数列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "批量更新产品谱系工序上料规则")
    @PreventRepeatSubmit
    @PostMapping("/batch/custom")
    public ResponseEntity<ResponseData<Void>> batch(@RequestBody List<PedigreeStepMaterialRule> pedigreeStepMaterialRules){
        try {
            pedigreeStepMaterialRuleService.batchUpdate(pedigreeStepMaterialRules);
            return ResponseData.save();
        }  catch (ResponseException e) {
            return ResponseData.error(e);
        }
    }

    /**
     *
     * @param pedigreeId 产品谱系主键id
     * @param clientId 客户ID
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序ID
     * @return List<PedigreeStepMaterialRule>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据产品谱系ID,工艺路线Id、客户ID、工序ID获取工序配置信息")
    @GetMapping("/byConditions")
    public ResponseEntity<ResponseData<List<PedigreeStepMaterialRule>>> byCondition(@RequestParam(value = "pedigreeId",required = false) Long pedigreeId,
                                                                                    @RequestParam(value = "workFlowId",required = false) Long workFlowId,
                                                                                    @RequestParam(value = "clientId",required = false) Long clientId,
                                                                                    @RequestParam(value = "stepId",required = false) Long stepId){
        return ResponseData.ok(pedigreeStepMaterialRuleService.findByPedigreeIdAndClientIdAndWorkFlowIdAndStepId(pedigreeId, clientId,workFlowId, stepId));
    }

    /**
     * 上料规则导入
     *
     * @param file 上料规则导入
     * @return org.springframework.http.ResponseEntity<java.lang.Void> 通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "上料规则导入")
    @Override
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file,
                                                 @RequestParam(value = "data", required = false) String data,
                                                 @RequestParam(value = "suffix", required = false) String suffix,
                                                 @RequestParam(value = "metaColumn", required = false) String metaColumn,
                                                 HttpServletResponse response) throws Exception {
        this.prepareImportParams();
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = pedigreeStepMaterialRuleService.importPedigreeStepMaterialRuleExcel(file);
        //获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> org.apache.commons.lang3.StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(org.apache.commons.lang3.StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
