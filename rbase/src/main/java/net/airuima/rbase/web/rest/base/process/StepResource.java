package net.airuima.rbase.web.rest.base.process;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.dto.exception.BadRequestException;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncStepDTO;
import net.airuima.rbase.service.base.process.StepService;
import net.airuima.rbase.web.rest.base.process.dto.StepCreateDTO;
import net.airuima.rbase.web.rest.base.process.dto.StepDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产工序Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "工序信息Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/steps")
@AuthorityRegion("工艺模型")
@AuthSkip("D")
public class StepResource extends ProtectBaseResource<Step> {
    private static final String EXCEPTION = "exception";
    private static final String MODULE = "工序信息";
    private final Logger log = LoggerFactory.getLogger(StepResource.class);
    private final StepService stepService;
    private static final String ERROR = "error";
    public StepResource(StepService stepService) {
        this.stepService = stepService;
        this.mapUri = "/api/steps";
    }

    /**
     * @throws
     * @description 根据编码或者名称获取启用的工序信息
     * <AUTHOR>
     * @param: text 工序编码或者名称
     * @param: size 最大返回数据条数
     * @updateTime 2020/12/22 18:56
     * @return: java.util.List<net.airuima.rbase.domain.base.process.Step>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "编码或者名称获取工序信息")
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<Step>>> findByNameOrCode(@RequestParam(value = "text",required = false) String text,
                                       @RequestParam(value = "size") Integer size,
                                       @RequestParam(value = "isEnable",required = false) Boolean isEnable,
                                       @RequestParam(value = "categoryList",required = false) List<Integer> categoryList) {
        return ResponseData.ok(stepService.findByCodeOrName(text,size,isEnable,categoryList));
    }

    @PreAuthorize("hasAnyAuthority('PEDIGREESTEP_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过工序ID及工艺路线ID获取工序及配置信息")
    @GetMapping("/stepAndConfigByStepIdAndWorkFlowId")
    public ResponseEntity<ResponseData<StepDTO>> findStepAndConfigByStep(@RequestParam(value = "stepId",required = true) Long stepId,@RequestParam(value="workFlowId",required = false) Long workFlowId){
        return ResponseData.ok(stepService.findStepAndConfigByStepId(stepId,workFlowId));
    }

    /**
     * 同时新增或者修改工序及工序属性配置信息
     * <AUTHOR>
     * @param stepDto  请求参数
     * @return ResponseEntity<ResponseData<Step>>
     * @date 2021-05-07
     **/
    @PreAuthorize("hasAnyAuthority('PEDIGREESTEP_CREATE') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "同时新增或者修改工序及工序属性配置信息")
    @PostMapping("/createCustom")
    public ResponseEntity<ResponseData<Step>> createCustom(@RequestBody StepDTO stepDto){
        try{
          Step step =  stepService.updateInstance(stepDto);
           return ResponseData.ok(step);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 新增工序
     *
     * @param entity 新增工序DTO
     * @return : org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.Step>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @PostMapping("/custom")
    public ResponseEntity<ResponseData<Step>> custom(@RequestBody StepCreateDTO entity) {
        try {
            Step step = stepService.create(entity);
            return ResponseData.ok(step);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 启用/禁用指定工序
     *
     * @param stepId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "启用/禁用指定工序")
    @Parameters({
            @Parameter(name = "stepId", description = "工序ID", required = true)
    })
    @PutMapping("/stepId/{stepId}")
    public ResponseEntity<ResponseData<Void>> enableByStepId(@PathVariable("stepId") Long stepId) {
        try {
            stepService.enableByStepId(stepId);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return ResponseData.error(e);
        }
    }

    /**
     * 同步工序信息
     * @param syncStepDtoList
     * <AUTHOR>
     * @date  2023/3/14
     * @return List<SyncResultDTO>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "同步工序信息")
    @PostMapping("/syncStep")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> syncStep(@RequestBody List<SyncStepDTO> syncStepDtoList) {
        try {
            return ResponseData.ok(stepService.syncStep(syncStepDtoList));
        } catch (Exception e) {
            e.printStackTrace();
            throw new BadRequestException(e.getMessage());
        }
    }
    /**
     * 通过编码和否启用查询对应工序
     *
     * @param code 编码
     * @param isEnable 是否启用
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.step.Step> 工序
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过编码和否启用查询对应工序")
    @GetMapping("/byCode")
    public ResponseEntity<ResponseData<Step>> findByCode(@RequestParam(value = "code") String code, @RequestParam(value = "isEnable", required = false) Boolean isEnable) {
        return ResponseData.ok(stepService.findByCode(code, isEnable));
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
