package net.airuima.rbase.web.rest.base.process.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工艺路线克隆DTO
 *
 * <AUTHOR>
 * @date 2023/07/17
 */
@Schema(description = "工艺路线克隆DTO")
public class WorkFlowCloneDTO {

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;


    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;



    /**
     * 工艺路线类型
     */
    @Schema(description = "工艺路线类型")
    private Integer category;

    /**
     * 被克隆的工艺路线id
     */
    @Schema(description = "被克隆的工艺路线id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workFlowId;

    public String getName() {
        return name;
    }

    public WorkFlowCloneDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WorkFlowCloneDTO setCode(String code) {
        this.code = code;
        return this;
    }



    public Integer getCategory() {
        return category;
    }

    public WorkFlowCloneDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getWorkFlowId() {
        return workFlowId;
    }

    public WorkFlowCloneDTO setWorkFlowId(Long workFlowId) {
        this.workFlowId = workFlowId;
        return this;
    }
}
