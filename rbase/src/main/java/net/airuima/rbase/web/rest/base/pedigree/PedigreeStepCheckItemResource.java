package net.airuima.rbase.web.rest.base.pedigree;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.service.base.pedigree.PedigreeStepCheckItemService;
import net.airuima.util.ResponseData;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 产品谱系工序检测项目Resource
 * <AUTHOR>
 * @date 2021-03-22
 */
@Tag(name = "产品谱系工序检测项目Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/pedigree-step-check-items")
@AuthorityRegion("质量检测")
@FuncInterceptor("FAI || IPQC || PQC || FQC")
public class PedigreeStepCheckItemResource extends BaseResource<PedigreeStepCheckItem> {

    private static final String MODULE = "产品谱系工序检测项目";
    private final PedigreeStepCheckItemService pedigreeStepCheckItemService;

    public PedigreeStepCheckItemResource(PedigreeStepCheckItemService pedigreeStepCheckItemService) {
        this.pedigreeStepCheckItemService = pedigreeStepCheckItemService;
        this.mapUri = "/api/pedigree-step-check-items";
    }

    /**
     * 根据检测规则id查询产品谱系工序检测项目
     * <AUTHOR>
     * @date 2022/11/3 15:20
     * @param pedigreeStepCheckRuleId 检测规则id
     * @return List<PedigreeStepCheckItem>
     */
    @PreAuthorize("hasAnyAuthority('PEDIGREESTEPCHECKRULE_READ') or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据检测规则id查询产品谱系工序检测项目",parameters = {
            @Parameter(name = "pedigreeStepCheckRuleId",required = true, description = "检测规则主键ID",schema = @Schema(type = "int64") ,in = ParameterIn.PATH),
    })
    @GetMapping("/findById/{PedigreeStepCheckRuleId}")
    public ResponseEntity<ResponseData<List<PedigreeStepCheckItem>>> findByPedigreeStepCheckRuleId(@PathVariable("PedigreeStepCheckRuleId") Long pedigreeStepCheckRuleId) {
        return ResponseData.ok(pedigreeStepCheckItemService.findByPedigreeStepCheckRuleId(pedigreeStepCheckRuleId));
    }

}
