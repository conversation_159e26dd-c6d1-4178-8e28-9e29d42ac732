package net.airuima.rbase.proxy.organization;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.organization.SupplierDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseSupplierProxy {

    @BeanDefine(value = "supplierRepository", funcKey = "OrgSupplier")
    public SupplierDTO findByCodeAndDeleted(String code, Long deleted){
        return null;
    }

    @BeanDefine(value = "supplierRepository", funcKey = "OrgSupplier")
    public List<SupplierDTO> findByCodeInAndDeleted(List<String> codeList, Long deleted){
        return null;
    }
}
