package net.airuima.rbase.proxy.bom;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.bom.MaterialDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseMaterialProxy {

    @BeanDefine("materialRepository")
    public Optional<MaterialDTO> findByCodeAndDeleted(String code, Long deleted){
        return Optional.empty();
    }

    @BeanDefine("materialRepository")
    public MaterialDTO findByIdAndDeleted(Long id, Long deleted){
        return null;
    }

    @BeanDefine("materialRepository")
    public List<MaterialDTO> findByCodeInAndDeleted(List<String> codeList, long deleted){
        return null;
    }

    @BeanDefine("materialService")
    public List<MaterialDTO> findByNameOrCode(String text, Boolean isEnable, Integer size){
        return new ArrayList<>();
    }
}
