package net.airuima.rbase.proxy.rwms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.rwms.SyncShipmentOrderDTO;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseShipmentOrderProxy {

    /**
     * 保存同步出库单数据
     * @param syncShipmentOrderDto 同步出库单DTO
     */
    @BeanDefine(value = "shipmentOrderService", funcKey = "RwmsService")
    public void syncShipmentOrder(@ObjectField("net.airuima.rwms.web.rest.procedure.deliver.dto.SyncShipmentOrderDTO") SyncShipmentOrderDTO syncShipmentOrderDto) {

    }
}
