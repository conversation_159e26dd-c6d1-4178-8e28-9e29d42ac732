package net.airuima.rbase.proxy.organization;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseStaffProxy {

    @BeanDefine("staffRepository")
    public Optional<StaffDTO> findByCodeAndIsLeaveAndDeleted(String code, Boolean leave, Long deleted) {
        return Optional.empty();
    }

    @BeanDefine("staffRepository")
    public Optional<StaffDTO> findByCodeAndDeleted(String code, long deleted) {
        return Optional.empty();
    }

    @BeanDefine("staffRepository")
    public StaffDTO findByIdAndDeleted(Long id,Long deleted){
        return null;
    }
}
